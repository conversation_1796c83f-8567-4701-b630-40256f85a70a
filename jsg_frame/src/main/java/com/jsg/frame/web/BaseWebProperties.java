package com.jsg.frame.web;

import com.jsg.frame.cache.MemcachedKeyInfo;
import java.util.HashMap;
import java.util.Map;
import org.springframework.context.ApplicationContext;

/* loaded from: jsg_frame-********.jar:com/jsg/frame/web/BaseWebProperties.class */
public class BaseWebProperties {
    public static String SYS_NUM = "";
    public static String SYS_CODE = "";
    public static String CONTEXT_NAME = "";
    public static String KEY_ZCM = "";
    public static String HOSPNAME = "";
    public static int TIME_SERVER_PORT = 0;
    public static String TIME_SERVER_IP = "";
    public static int REDIS_PORT = 0;
    public static String REDIS_IP = "";
    public static int LOCK_EXPIRE = 0;
    public static int LOCK_WAIT_TIME = 0;
    public static ApplicationContext SPRING_CONTEXT = null;
    public static Map<String, MemcachedKeyInfo> MEMCACHE_KEY_MAP = new HashMap();
    public static Map<String, MemcachedKeyInfo> CLEAR_MEMCACHE_KEY = new HashMap();
}
