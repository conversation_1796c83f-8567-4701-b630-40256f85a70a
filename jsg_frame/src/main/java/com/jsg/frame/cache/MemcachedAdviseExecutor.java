//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.jsg.frame.cache;

import com.alibaba.fastjson.JSONObject;
import com.jsg.frame.web.BaseWebProperties;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class MemcachedAdviseExecutor {
    private static Logger logger = LoggerFactory.getLogger(MemcachedAdviseExecutor.class);
    private static final Logger countTimeLogger = LoggerFactory.getLogger("countTimeLogger");
    private static final Logger MemcachedLog = LoggerFactory.getLogger("MemcachedLog");

    public MemcachedAdviseExecutor() {
    }

    public Object getMemData(ProceedingJoinPoint pjp) throws Exception {
        String target = pjp.getSignature().getDeclaringTypeName();
        String methodName = pjp.getSignature().getName();
        Object[] args = pjp.getArgs();
        Object ret = null;
        if (!methodName.endsWith("4Mem")) {
            long startTime = System.currentTimeMillis();
            boolean var16 = false;

            try {
                var16 = true;
                ret = pjp.proceed();
                var16 = false;
            } catch (Throwable var18) {
                logger.error("执行" + target + "的" + methodName + "发生错误，", var18);
                throw new Exception(var18);
            } finally {
                if (var16) {
                    long endTime = System.currentTimeMillis();
                    countTimeLogger.info(target + "的" + methodName + "执行花费了" + (endTime - startTime) + "毫秒");
                }
            }

            long endTime = System.currentTimeMillis();
            countTimeLogger.info(target + "的" + methodName + "执行花费了" + (endTime - startTime) + "毫秒");
        } else {
            String key = target + "_" + methodName;
            MemcachedKeyInfo memcachedKey = (MemcachedKeyInfo)BaseWebProperties.MEMCACHE_KEY_MAP.get(key);
            if (memcachedKey == null) {
                logger.warn("方法 " + target + "-" + methodName + " 的memcached key信息未配置，将直接执行方法不使用缓存");
                // 如果没有配置缓存key，直接执行方法并返回结果，不使用缓存
                try {
                    ret = pjp.proceed();
                } catch (Throwable var17) {
                    throw new Exception(var17);
                }
                return ret;
            }

            String finalMemKey = memcachedKey.getMemKey();
            if (args != null && args.length > 0 && memcachedKey.isNeedParam()) {
                finalMemKey = finalMemKey + JSONObject.toJSONString(args);
            }

            finalMemKey = BaseWebProperties.SYS_NUM + "_" + finalMemKey;
            IMemcachedManager cache = (IMemcachedManager)BaseWebProperties.SPRING_CONTEXT.getBean(memcachedKey.getCacheName());
            ret = cache.get(finalMemKey);
            if (ret == null) {
                try {
                    ret = pjp.proceed();
                    MemcachedLog.info(finalMemKey);
                    // 只有当返回值不为null时才存储到缓存中
                    if (ret != null) {
                        cache.add(finalMemKey, ret, memcachedKey.getExpireInSeconds());
                    } else {
                        logger.warn("方法 " + target + "." + methodName + " 返回null值，跳过缓存存储，key: " + finalMemKey);
                    }
                } catch (Throwable var17) {
                    throw new Exception(var17);
                }
            }
        }

        return ret;
    }

    public Object clearMemData(ProceedingJoinPoint pjp) throws Exception {
        String target = pjp.getSignature().getDeclaringTypeName();
        String methodName = pjp.getSignature().getName();
        Object[] args = pjp.getArgs();
        Object ret = null;
        long startTime;
        long endTime;
        if (!methodName.endsWith("3Mem")) {
            startTime = System.currentTimeMillis();
            boolean var25 = false;

            try {
                var25 = true;
                ret = pjp.proceed();
                var25 = false;
            } catch (Throwable var28) {
                logger.error("执行" + target + "的" + methodName + "发生错误，", var28);
                throw new Exception(var28);
            } finally {
                if (var25) {
                    endTime = System.currentTimeMillis();
                    countTimeLogger.info(target + "的" + methodName + "执行花费了" + (endTime - startTime) + "毫秒");
                }
            }

            endTime = System.currentTimeMillis();
            countTimeLogger.info(target + "的" + methodName + "执行花费了" + (endTime - startTime) + "毫秒");
        } else {
            startTime = System.currentTimeMillis();
            boolean var20 = false;

            try {
                var20 = true;
                ret = pjp.proceed();
                MemcachedKeyInfo memcachedKey = (MemcachedKeyInfo)BaseWebProperties.CLEAR_MEMCACHE_KEY.get(BaseWebProperties.SYS_NUM);
                IMemcachedManager var9 = (IMemcachedManager)BaseWebProperties.SPRING_CONTEXT.getBean(memcachedKey.getCacheName());
                var9.fullAll();
                var20 = false;
            } catch (Throwable var26) {
                logger.error("执行" + target + "的" + methodName + "发生错误，", var26);
                throw new Exception(var26);
            } finally {
                if (var20) {
                    endTime = System.currentTimeMillis();
                    countTimeLogger.info(target + "的" + methodName + "执行花费了" + (endTime - startTime) + "毫秒");
                }
            }

            endTime = System.currentTimeMillis();
            countTimeLogger.info(target + "的" + methodName + "执行花费了" + (endTime - startTime) + "毫秒");
        }

        return ret;
    }

    private Object[] parseArgs(Object[] args) {
        Object firstArg = args[0];
        return firstArg instanceof Map ? ((Map)firstArg).values().toArray() : args;
    }
}
