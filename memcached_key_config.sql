-- 为缺失的 memcached key 配置添加数据
-- 系统编码：09 (jsg_csp)
-- 缓存名称：csp_cache

-- 1. 联系人关系查询方法
INSERT INTO T_MEMCACHED_KEY_DA (
    F_MEM_KEY, 
    F_SYS_NUM, 
    F_CACHED_NAME, 
    F_DAO_NAME, 
    F_DAO_METHOD, 
    F_IS_NEED_PARAM, 
    F_EXPIRE_IN_SECONDS
) VALUES (
    'gyb_lxrgx_list', 
    '09', 
    'csp_cache', 
    'com.jsg.csp.xtwh.ksry.dao.New1Gyb_lxrgxModelMapper', 
    'querygyb_lxrgx4Mem', 
    1, 
    3600
);

-- 2. 国籍查询方法
INSERT INTO T_MEMCACHED_KEY_DA (
    F_MEM_KEY, 
    F_SYS_NUM, 
    F_CACHED_NAME, 
    F_DAO_NAME, 
    F_DAO_METHOD, 
    F_IS_NEED_PARAM, 
    F_EXPIRE_IN_SECONDS
) VALUES (
    'gyb_gj_list', 
    '09', 
    'csp_cache', 
    'com.jsg.csp.xtwh.ksry.dao.New1Gyb_gjModelMapper', 
    'querygyb_gj4Mem', 
    1, 
    3600
);

-- 3. 民族查询方法
INSERT INTO T_MEMCACHED_KEY_DA (
    F_MEM_KEY, 
    F_SYS_NUM, 
    F_CACHED_NAME, 
    F_DAO_NAME, 
    F_DAO_METHOD, 
    F_IS_NEED_PARAM, 
    F_EXPIRE_IN_SECONDS
) VALUES (
    'gyb_mz_list', 
    '09', 
    'csp_cache', 
    'com.jsg.csp.xtwh.ksry.dao.New1Gyb_mzModelMapper', 
    'querygyb_mz4Mem', 
    1, 
    3600
);

-- 4. 婚姻状况查询方法
INSERT INTO T_MEMCACHED_KEY_DA (
    F_MEM_KEY, 
    F_SYS_NUM, 
    F_CACHED_NAME, 
    F_DAO_NAME, 
    F_DAO_METHOD, 
    F_IS_NEED_PARAM, 
    F_EXPIRE_IN_SECONDS
) VALUES (
    'gyb_hyzk_list', 
    '09', 
    'csp_cache', 
    'com.jsg.csp.xtwh.ksry.dao.New1Gyb_hyzkModelMapper', 
    'querygyb_hyzk4Mem', 
    1, 
    3600
);

-- 5. 行政区划查询方法
INSERT INTO T_MEMCACHED_KEY_DA (
    F_MEM_KEY, 
    F_SYS_NUM, 
    F_CACHED_NAME, 
    F_DAO_NAME, 
    F_DAO_METHOD, 
    F_IS_NEED_PARAM, 
    F_EXPIRE_IN_SECONDS
) VALUES (
    'gyb_xzqh_list', 
    '09', 
    'csp_cache', 
    'com.jsg.csp.xtwh.ksry.dao.New1Gyb_xzqhModelMapper', 
    'querygyb_xzqh4Mem', 
    1, 
    3600
);

-- 提交事务
COMMIT;
