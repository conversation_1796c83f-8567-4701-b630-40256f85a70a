<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>备案管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="index.css">
</head>
<body>
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top flex-container flex-align-c">
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="ryba">慢病备案</button>
            
        </div>
    </div>
    <div class="tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">病人基本信息</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class="grid-box">
                <div class="">
                    <div class="flex-container flex-align-c flex-wrap-w ">
						<div class=" flex-container flex-align-c padd-l-20 margin-b-10">
						    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">挂号序号</span>
						    <input  class="zui-input text-indent-5 wh122" type="text"
						           v-model="brxxContent.ryghxh"  placeholder="挂号序号"/>
						</div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">姓名</span>
                            <input  class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.brxm" @keydown.13="resultMzsfChange('brxm')"  placeholder="姓名"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">治疗开始日期</span>
                            <input  id="begndate" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.begndate"  placeholder="开始日期"/>
                        </div>
                        <div class=" flex-container flex-align-c padd-l-20 margin-b-10">
                            <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">治疗结束日期</span>
                            <input id="enddate" class="zui-input text-indent-5 wh122" type="text"
                                   v-model="brxxContent.enddate"  placeholder="结束日期"/>
                        </div>
						<div class=" flex-container flex-align-c padd-l-20 margin-b-10">
						    <span class=" wh66 text-right font-bolder padd-l-5 red">诊断疾病</span>
						    <div class="position flex-container ">
						        <input class="zui-input height-input" v-model="brxxContent.opspDiseCode" data-notEmpty="true"
						               
						               @keydown="changeDown($event,'xy','jbContent','jbsearchCon','opspDiseCode','opspDiseCode','opspDiseName','selSearchs2')"
						               @input="change(false,'xy2',$event.target.value,'selSearchs2','opspDiseName','opspDiseCode')"
						               v-if="ysOrBasIsShow">
						        <jbsearch-table :message="jbsearchCon" :selected="selSearchs2" :page="page" :them="jbthem"
						                        @click-one="checkedOneOut"
						                        @click-two="jbselectOne">
						        </jbsearch-table>
						</div>
						<div class=" flex-container flex-align-c padd-l-20 margin-b-10">
						    <span class="padd-r-5 ft-14 font-bolder red wh80 text-right">医院鉴定日期</span>
						    <input id="hospIdeDate" class="zui-input text-indent-5 wh122" type="text"
						           v-model="brxxContent.hospIdeDate"  placeholder="鉴定日期"/>
						</div>
						
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
	<div id="popCenter" v-cloak>
	    <div class="popshow" v-if="isShow"></div>
	    <div v-if="isShow" class="zui-form podrag bcsz-layer  flex-container flex-dir-c"
	         style="width: auto;overflow: hidden;top: 80px;bottom: 50px; height: auto; left: 100px; right: 100px">
	        <div class="layui-layer-title">请输入参合人员信息</div>
	        <span class="layui-layer-setwin">
	            <a @click="isShow=false" class="closex ti-close" href="javascript:;"></a>
	        </span>
	        <div class="layui-layer-content flex-container flex-dir-c flex-one">
	            <div class="layui-height flex-container flex-dir-c flex-one " id="loadPage">
	
	            </div>
	        </div>
	    </div>
	</div>
</div>

<script type="application/javascript" src="ryba.js"></script>
</body>
</html>
