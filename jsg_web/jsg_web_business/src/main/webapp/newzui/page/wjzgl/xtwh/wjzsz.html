<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危急值参数设置</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="wjzsz.css">
</head>
<body class="skin-default flex-container flex-one background-f padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div v-cloak id="wrapper" class="flex-container flex-one flex-dir-c">
    <div class="panel tong-top flex-container flex-align-c">
        <button v-waves class="tong-btn btn-parmary-b  icon-font14 paddr-r5 margin-l-10" @click="addData">新增</button>
        <button v-waves class="tong-btn btn-parmary-b  icon-font14 paddr-r5 margin-l-10" @click="saveData">保存</button>
    </div>
   <div class="main-content">
       <div class="padd-l-10  padd-t-10">
           <div class="flex-container flex-wrap-w flex-align-c padd-r-10 padd-b-10">
               <span class="whiteSpace">&emsp;危急值消息：</span>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'检验设备生成结果时是否发送危急值消息'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
           </div>
           <div class="flex-container flex-wrap-w flex-align-c padd-b-10 padd-r-10">
               <span class="whiteSpace">消息发送范围：</span>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'值班检验师'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'检验小组人员'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'检验科所有人员'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'是否显示危急值警示框'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
           </div>
           <div class="flex-container   flex-align-c padd-b-10 padd-r-10">
               <span class="whiteSpace">处理接收范围：</span>
               <div class="flex-container flex-wrap-w ">
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'主管医生'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'值班医生'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'患者科室'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'病区护士'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'弹窗消息框是否霸屏显示'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <div class="flex-container flex-align-c padd-r-10">
                       <span>待完成处理危急值刷新间隔时间：</span>
                       <input class="zui-input wh70 margin-l-10 margin-r-10"/>小时
                   </div>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'是否启用危急值处理知识库'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'危急值处理完成后是否回传报告医生'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'危急值处理完成后是否回传报告科室主任'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'是否必须写危急值病程'" :val="'bk'"
                                 :new-value="bk"></vue-checkbox>
                   <div class="flex-container flex-align-c padd-r-10">
                       <span>危急值病程记录完成时间：</span>
                       <input class="zui-input wh70 margin-l-10 margin-r-10"/>小时
                   </div>
               </div>
           </div>
           <div class="flex-container flex-wrap-w flex-align-c padd-b-10 padd-r-10">
               <span class="whiteSpace">消息内容设置：</span>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'科室'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'床号'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'患者姓名'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'性别'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'年龄'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'检查/检验项目'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'报告结果'" :val="'bk'"
                             :new-value="bk"></vue-checkbox>
               <input class="zui-input wh180 margin-l-10 margin-r-10"/>
           </div>
       </div>
       <div class="flex-container flex-wrap-w padd-l-10 padd-b-10" v-for="(item,index) in popContent.msgList">
           <div class=" ">
               <div class="padd-r-20 ">
                   <div class="flex-container padd-b-10">
                       <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'督促处理'" :val="'bk'"
                                     :new-value="bk"></vue-checkbox>
                       <button v-waves class="tong-btn btn-parmary-b  icon-font14 paddr-r5 margin-l-10" @click="addPop('ryList'+index,index)">添加人员
                       </button>
                   </div>
                   <div rows="4" class="padd-r-10 padd-t-10 whTextarea padd-l-10 padd-r-10" cols="30">
                       <span v-for="(item,index) in item['ryList'+index]">{{item.text}},</span>
                   </div>
               </div>
           </div>
           <div class="padd-r-20 ">
               <button v-waves class="tong-btn btn-parmary-b  margin-b-10 icon-font14 paddr-r5 margin-l-10" @click="addPop('zwList'+index,index)">
                   添加职务
               </button>
               <div rows="4" class="padd-r-10 padd-t-10 whTextarea padd-l-10 padd-r-10" cols="30">
                   <span v-for="(item,index) in item['zwList'+index]">{{item.text}},</span>
               </div>
           </div>
           <div class="">
               <button v-waves class="tong-btn btn-parmary-b  margin-b-10 icon-font14 paddr-r5 margin-l-10" @click="addPop('xzList'+index,index)">
                   添加小组
               </button>
               <div rows="4" class="padd-r-10 padd-t-10 whTextarea padd-l-10 padd-r-10" cols="30">
                   <span v-for="(item,index) in item['xzList'+index]">{{item.text}},</span>
               </div>
           </div>
           <div class=" padd-l-10">
               <div class="flex-container flex-align-c">
                   <span>间隔时间：</span>
                   <input class="zui-input wh70 margin-l-10 margin-r-10"/>分钟
               </div>
           </div>
       </div>
   </div>
<model :s="'确定'" :c="'取消'" @default-click="popSave" @result-clear="listShow=false" :model-show="true"
       @result-close="listShow=false" v-if="listShow" :title="title">
    <div class="bqcydj_model flex-container">
        <div class="flex-container border-solid flex-one">
            <div class="wh50Max padd-r-10  flex-container flex-dir-c flex-one">
                <div class="titles padd-l-5">已添加人员</div>
                <div class="flex-container  flex-one background-f ">
                    <select class="padd-l-5" multiple="multiple">
                        <option @dblclick="delFun(item,index)" v-for="(item,index) in addJsonList" class="name cursor">
                            {{item.text}}
                        </option>
                    </select>
                </div>
            </div>
            <div class="wh50Max  flex-container flex-dir-c flex-one">
                <div class="titles padd-l-5">待添加人员</div>
                <div class="  flex-container  flex-one background-f ">
                    <select class="padd-l-5" multiple="multiple">
                        <option @dblclick="addFun(item,index)" v-for="(item,index) in delJsonList" class="name cursor">
                            {{item.text}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</model>
    <div class="zui-table-tool padd-b-10 padd-t-10 padd-l-10">
        说明：
          <div>&emsp;&emsp;1、间隔时间是指未处理的危急值记录发送与当前的间隔时间</div>
          <div>&emsp;&emsp;2、危急值处理回报范围设置：报告医生、报告科室主任（要求必须提供有效的联系方式（手机号、微信号、钉钉号等））</div>
          <div>&emsp;&emsp;3、督促处理业务中添加的人员来源于医院现有人事系统或者HIS系统，要求必须提供有效的联系方式（手机号、微信号、钉钉号等）</div>
          <div>&emsp;&emsp;4、督促处理流程：督促处理1→督促处理2→督促处理3→督促处理4，例如：科主任（30分钟）→医务科主任（40分钟）→主管院领导（50分钟）</div>
          <div class="whiteSpaceNormal">&emsp;&emsp;5、必须写危急值病程勾选时，危急值报告完成确认则显示所有处理确认后的危急值记录，处理中的需要完成同时进入书写病程页面，已完成直接进入病程页面，要求回报书写完成标志；设置了危急值病程完成时间的情况下，系统要提醒临床医生书写，否则不允许处理其他业务；</div>
    </div>
</div>
<script type="text/javascript" src="wjzsz.js"></script>
</body>
</html>
