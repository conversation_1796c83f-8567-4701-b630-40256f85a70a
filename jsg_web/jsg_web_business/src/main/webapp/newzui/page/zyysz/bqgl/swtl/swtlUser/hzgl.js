var userNameBg=new Vue({
    el:'.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        Brxx_List:[],
        popContent:{}
    },

    methods:{

    },
    create:function(){

    },
})
var content=new Vue({
    el:'#content',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat,scrollOps],
    data:{
        objAvatar:8,
        edit:false,
        editText:true,
        tipsShow:false,
        tipsShow1:false,
        userName:false,
        previewshow:false,
        Brxx_List:[],
        oldCyrList:[],
        objabsolute:{},
        popContent:{
            insert:[],
            remove:[],
            xczp:[],
            storeStatus:"",
        },
        defaltObj:{
            title:'死亡讨论邀请',
            cs:'background-f4b26b color-8e9694',
            cb:'拒绝',
            sb:'接受'
        },
        subObj:{
            title:'死亡讨论邀请',
            cs:'',
            cb:'',
            sb:''
        },
        refuseObj:{
            title:'拒绝原因',
        },
    },
    filters: {
        imgStyle: function (value) {
            return 'background-image: url('+value+')'
        }
    },
    mounted:function(){
        var obj = JSON.parse(sessionStorage.getItem('ynbtlDetail'));
        userNameBg.popContent = obj;
        this.popContent = obj;
        this.oldCyrList = obj.cyrList;
        this.popContent.xczp = this.popContent.xctp.split(",");
        this.popContent.shsj =this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    content.popContent.shsj = value;
                } else {
                    content.popContent.shsj = '';
                }
            }
        });
    },
    methods:{
        upload:function(event){
            var formData = new FormData();
            var files = event.target.files;
            if(files.length<=0)
                return;
            var arr = new Array();
            var brr = new Array();
            for(var i=0;i<files.length;i++)
            {
                if(files.length>8)
                {
                    malert("最多只能上传8个图片",'','defeadted');
                    return;
                }
                else {
                    formData.append(i+"", files[i]);
                }
            }
            this.$http.post('/actionDispatcher.do?reqUrl=New1FileUpload&types=uploadFile',formData).then(function (data) {
                if (data.body.a == 0) {
                    content.popContent.xczp = data.body.d;
                    content.popContent.xctp = data.body.d.toString();
                    content.$forceUpdate();
                    malert('操作成功！','','success');
                } else {
                    malert('操作失败！','','defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        deleteFile:function(index)
        {
            var arr = content.popContent.xczp;
            content.popContent.filePath = arr[index];
            arr.splice(index,1);
            content.popContent.xczp = arr;
            if(arr.length<=0)
            {
                content.popContent.xctp = "";
            }
            else
            {
                content.popContent.xctp = arr.toString();
            }
            content.$forceUpdate();
            content.$http.post('/actionDispatcher.do?reqUrl=New1FileUpload&types=deleteFile',
                JSON.stringify(content.popContent)).then(function (data) {
                if (data.body.a == 0) {
                    malert('删除一张图片成功！','','success');
                } else {
                    malert('操作失败！','','defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        tjys:function(flag){
            brzcList.isChecked = [];
            if(flag)
            {
                brzcList.isMulti = "some";
            }
            else
            {
                brzcList.isMulti = "one";
            }
            brzcList.type=false;
            var dg = {"page": 1, "rows": 20000, "sort": "", "order": "asc"};
            var bean = {"ysbz": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" +
                JSON.stringify(dg) + "&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    brzcList.ryList = json.d.list;
                } else {
                    malert(json.c+",医生列表查询失败",'','defeadted');
                    return false;
                }
            });
        },
        rowsJ:function(event){
            if(event.keyCode==13){
                event.srcElement.rows=event.srcElement.rows+1
            }else if(event.keyCode==8){
                if(event.srcElement.rows!=1){
                    event.srcElement.rows=event.srcElement.rows-1
                }
            }
        },
        editShow:function(){
            this.editText=!this.editText;
            content.popContent.storeStatus = content.popContent.status;
        },
        todoEdit:function()
        {
            this.editText=!this.editText;
            content.popContent.ifRecord = '1';
            content.popContent.storeStatus = "4";//填写讨论记录置状态为待审核
        },
        cancelEdit:function(){
            this.editText=!this.editText;
            content.popContent.ifRecord == '';
        },
        preview:function(){
            this.previewshow=true
        },
        previewHide:function(){
            this.previewshow=false
        },
        hoverName:function (falg,index,event){
            this.objabsolute.left=event!=undefined?event.clientX-80:0
            this.objabsolute.top=event!=undefined?event.clientY+20:0
            this.userName= falg==true ? true:falg
            this.$forceUpdate()
        },
        accept:function(){
            common.openConfirm('确定接受患者：<span class="color-green">'+ content.popContent.brxm +'</span>的'+ (content.popContent.type=='1'?'疑难病':'死亡') +'讨论吗？',function () {
                content.popContent.status = '2';
                content.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=operate',
                    JSON.stringify(content.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('接收成功！','','success');
                        sessionStorage.setItem("status",new Date().getTime().toString());
                    } else {
                        malert('接收失败！','','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            },function () {
                common.openConfirm('<textarea placeholder="请输入拒绝原因" oninput="getJJYY(this)"  class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>',function () {
                    content.popContent.status = '3';
                    content.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=operate',
                        JSON.stringify(content.popContent)).then(function (data) {
                        if (data.body.a == 0) {
                            malert('拒绝成功！','','success');
                            sessionStorage.setItem("status",new Date().getTime().toString());
                        } else {
                            malert('拒绝失败！','','defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });
                },function () {

                },content.refuseObj)
            },this.defaltObj)
        },
        agree:function(flag){
            var msg = '通过';
            if(flag=='5')
            {
                msg = '拒绝';
            }
            common.openConfirm('确定将患者：<span class="color-green">'+ content.popContent.brxm +'&nbsp;</span>的'+ (content.popContent.type=='1'?'疑难病':'死亡') +'讨论记录审核<span class="' + (flag=='6'?'color-green':'color-ff5c63') + ' font20">【'+msg+'】</span>吗？',function () {
                content.popContent.status = flag;
                content.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=audit',
                    JSON.stringify(content.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('审核成功！','','success');
                        content.getData(content.popContent.relId);
                        content.editText = !content.editText;
                        sessionStorage.setItem("status",new Date().getTime().toString())
                    } else {
                        malert('审核失败！','','defeadted');
                    }
                })
            },function(){

            })
        },
        submit:function(){
            var oldList = JSON.parse(JSON.stringify(content.oldCyrList));
            var newList = JSON.parse(JSON.stringify(content.popContent.cyrList));
            //如果没有参与人员变动
            if(JSON.stringify(newList) == JSON.stringify(oldList))
            {
                content.popContent.remove = "";
                content.popContent.insert = "";
            }else {
                handleNewList(oldList,newList);
            }
            content.popContent.status = content.popContent.storeStatus;//切换状态
            content.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlTLJL&types=save',
                JSON.stringify(content.popContent)).then(function (data) {
                if (data.body.a == 0) {
                    malert('操作成功！','','success');
                    content.getData(content.popContent.relId);
                } else {
                    malert('操作失败！','','defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        create:function(){

        },
        getData:function(){
            $.getJSON("/actionDispatcher.do?reqUrl=New1BqglYnbtlTLJL&types=query&json=" +
               JSON.stringify(content.popContent), function (json) {
                if (json.a == 0) {
                    content.popContent.cyrList = json.d.list[0].cyrList;
                    content.oldCyrList = json.d.list[0].cyrList;
                    content.popContent = json.d.list[0];
                    content.popContent.xczp = json.d.list[0].xctp.split(",");
                    userNameBg.popContent = json.d.list[0];
                    content.editText = !content.editText;
                    sessionStorage.setItem("status",new Date().getTime().toString())
                } else {
                    malert(json.c+",详情信息查询失败",'','defeadted');
                    return false;
                }
            });
        },
    },
});
var brzcList=new Vue({
    el:'#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],

    data:{
        type:true,
        isMulti:'one',
        ryList:[],
        popContent:{}
    },
    create:function(){

    },
    methods:{
        fqtl:function () {

        },
        getData:function(){

        },
        submit:function () {
            var List = [];//选中的数据列
            if (this.isChecked.length > 0) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    var obj = {};
                    if (this.isChecked[i] == true) {
                        obj.cyrId = brzcList.ryList[i].rybm;
                        obj.yljgbm = brzcList.ryList[i].yljgbm;
                        obj.ksbm = brzcList.ryList[i].ksbm;
                        obj.ryxm = brzcList.ryList[i].ryxm;
                        List.push(obj);//将选中的元素，放到List
                    }
                }
            }
            if(brzcList.isMulti == 'one')//单选选主持人
            {
                content.popContent.zcrId = List[0].rybm;
                content.popContent.zcrxm = List[0].ryxm;
            }
            else {//多选选参与医生
                content.popContent.cyrList = List;
            }
            this.type=true
        },
        close:function () {
            this.type=true
        }
    },
});

var orignalSetItem = sessionStorage.setItem;
sessionStorage.setItem = function (key, newValue) {
    var setItemEvent = new Event('setItemEvent');
    setItemEvent.newValue = newValue;
    window.dispatchEvent(setItemEvent);
    orignalSetItem.apply(this, arguments);
};
window.addEventListener('storage',function (e) {
    if(e.key=='ynbtlDetail'){
        var obj = JSON.parse(e.newValue);
        content.popContent = obj;
        userNameBg.popContent = obj;
        content.popContent.xczp = obj.xctp.split(",");
        content.oldCyrList = obj.cyrList;
    }
});

function getJJYY(val){
    content.popContent.jjyy = $(val).val();
}
function handleNewList(oldList,newList) {
    var commonList = [];
    //选出不需要操作的人
    for(var i=0;i<oldList.length;i++)
    {
        for(var j=0;j<newList.length;j++)
        {
            if(oldList[i].rybm == newList[j].cyrId)
            {
                commonList.push(oldList[i]);
            }
        }
    }
    //需要删除的人员
    var olen = oldList.length;
    for(var k=0;k<olen;k++)
    {
        for(var t=0;t<commonList.length;t++)
        {
            if(oldList[k].rybm == commonList[t].rybm)
            {
                oldList.splice(k,1);
                olen--;
            }
        }
    }
    //需要插入的人员
    var nlen = oldList.length;
    for(var a=0;a<nlen;a++)
    {
        for(var b=0;b<commonList.length;b++)
        {
            if(newList[a].cyrId == commonList[b].rybm)
            {
                newList.splice(a,1);
                nlen--;
            }
        }
    }
    content.popContent.remove = oldList;
    content.popContent.insert = newList;
}