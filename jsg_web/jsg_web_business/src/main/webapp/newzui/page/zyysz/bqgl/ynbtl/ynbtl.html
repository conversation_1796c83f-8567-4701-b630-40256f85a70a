<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>死亡讨论</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="swtl.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
</head>
<body class="skin-default">
<div class="wrapper background-f swtl">
    <div class="panel flex-container flex-dir-c">
        <div class="tong-top">
            <button v-waves class="tong-btn btn-parmary" @click="fqtl()"><i class="iconfont iconfont-no-hover icon-icon70  icon-font19"></i>发起讨论</button>
            <button v-waves class="tong-btn btn-parmary-b" @click="getData()"><i class="iconfont icon-iocn56 icon-c1 icon-font19"></i>刷新</button>
        </div>
        <div class="tong-search padd-l-20">
            <div class="top-form">
                <label class="top-label font14">状态</label>
                <select-input style="width: 122px;" @change-data="resultChange" :not_empty="false"
                              :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                              :name="'popContent.jzbz'">
                </select-input>
            </div>
            <div class="top-form">
                <label class="top-label font14">检索</label>
                <div class="top-zinle">
                    <div class="top-zinle">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsVal"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view hzList padd-r-10 padd-l-10">
        <div class="zui-table-header" >
            <table class="zui-table"ref="tableHeader" >
                <thead>
                <tr>
                    <th>
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>身份证号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>年龄</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>讨论时间</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left"><span>讨论地点</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>主持人</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left"><span>参与人员</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>

        <div class="zui-table-body" @scroll="scrollTable($event)">
            <vue-scroll :ops="pageScrollOps"   @handle-scroll="handleScroll">
                <table class="zui-table">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" v-for="(item, $index) in 20"  @dblclick="newOpenPage()">
                        <td ><div class="zui-table-cell cell-m"><span>{{item}}</span></div></td>
                        <td ><div class="zui-table-cell cell-s"><span>李昊</span></div></td>
                        <td ><div class="zui-table-cell cell-xl"><span>123456789012345678</span></div></td>
                        <td ><div class="zui-table-cell cell-s"><span>//</span></div></td>
                        <td ><div class="zui-table-cell cell-xl"><span>2018/12/12 10:50</span></div></td>
                        <td ><div class="zui-table-cell cell-xl text-left"><span >不能开机</span></div></td>
                        <td ><div class="zui-table-cell cell-s "><span>henry</span></div></td>
                        <td ><div class="zui-table-cell cell-xl text-left "><span>晓不得 晓得 哦豁</span></div></td>
                        <td ><div class="zui-table-cell cell-s color-wtg color-green color-cf3" ><span>带接受</span></div></td>
                        <td>
                            <div class="zui-table-cell flex-center  cell-s">
                                <i class="iconfont icon-iocn12 icon-c75 icon-font20 padd-t-2" data-title="接受" @click="accept($index)"></i>
                                <i class="iconfont icon-iocn29 icon-c75 icon-font20 padd-t-2" data-title="拒绝" @click="refuse($index)"></i>
                                <i class="iconfont icon-iocn46 icon-c75 icon-font20 padd-t-2" data-title="编辑" @click="newOpenPage()"></i>
                                <i class="iconfont icon-iocn45 icon-c75 icon-font20 padd-t-2" data-title="审核" @click="newOpenPage()"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
            </vue-scroll>
        </div>

        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr >
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in 20"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-s">
                            <div class="zui-table-cell flex-center  cell-s">
                                <i class="iconfont icon-iocn12 icon-c75 icon-font20 padd-t-2" data-title="接受" @click="accept($index)"></i>
                                <i class="iconfont icon-iocn29 icon-c75 icon-font20 padd-t-2" data-title="拒绝" @click="refuse($index)"></i>
                                <i class="iconfont icon-iocn46 icon-c75 icon-font20 padd-t-2" data-title="编辑" @click="newOpenPage()"></i>
                                <i class="iconfont icon-iocn45 icon-c75 icon-font20 padd-t-2" data-title="审核" @click="newOpenPage()"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
    <div class="side-form  pop-width"  :class="{'ng-hide':type}" v-cloak id="brzcList1" role="form">
        <div class="fyxm-side-top flex-between">
            <span>发起死亡讨论</span>
            <span class="fr closex ti-close" @click="close"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list1 flex-start">
                <li>
                    <i>讨论患者</i>
                    <select-input  @change-data="resultChange" :not_empty="false"
                                   :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                                   :name="'popContent.jzbz'">
                    </select-input>
                </li>
                <li>
                    <i>讨论主持人</i>
                    <div class="flex-container">
                        <div class="HeadPortrait" v-for="list in objAvatar" :id="list">
                            <img class="headImg" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg">
                            <p class="color-f2a654 font12">刘医生</p>
                        </div>
                        <span class="iconfont  icon-upload col6_span1" @click="tjys"></span>
                    </div>
                </li>
                <li>
                    <i>讨论参与人员</i>
                    <div class="flex-container">
                        <div class="HeadPortrait" v-for="list in objAvatar" :id="list">
                            <img class="headImg" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg">
                            <p class="color-f2a654 font12">刘医生</p>
                        </div>
                        <span class="iconfont  icon-upload col6_span1" @click="tjys"></span>
                    </div>
                </li>
                <li>
                    <i>讨论时间</i>
                    <div class="zui-date">
                        <span style="margin-top:-2px" class="datenox icon-rl" lay-key="3"></span>
                        <input type="text" class="zui-input " id="timeVal"/>
                    </div>
                </li>
                <li>
                    <i>讨论地点</i>
                    <input type="text" class="zui-input "/>
                </li>
                <li>
                    <i>临床诊断</i>
                    <input type="text" class="zui-input "/>
                </li>
                <li>
                    <i>病历摘要</i>
                    <input type="text" class="zui-input "/>
                </li>
            </ul>
        </div>
        <div class="ksys-btn zui-table-tool" style="height: 66px">
            <button v-waves class="root-btn btn-parmary-d9" @click="close">取消</button>
            <button v-waves class="root-btn btn-parmary" @click="submit">确定</button>
        </div>
    </div>
    <div class="side-form  pop-width"  :class="{'ng-hide':type}" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top flex-between">
            <span>添加讨论医生</span>
            <span class="fr closex ti-close" @click="close"></span>
        </div>
        <div class="tong-search">
            <div class="top-form">
                <label class="top-label font14">科室</label>
                <select-input style="width: 122px;" @change-data="resultChange" :not_empty="false"
                              :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                              :name="'popContent.jzbz'">
                </select-input>
            </div>
        </div>
        <div class="ksys-side" style="padding:0 8px 0 9px">
            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table" >
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <!--<input-checkbox @result="reCheckBox" :list="'jsonList'"-->
                                <!--:type="'all'" :val="isCheckAll">-->
                                <!--</input-checkbox>-->
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>医生姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>科室</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>职称</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" v-for="(item, $index) in 25"  @dblclick="edit($index)">
                            <td>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td ><div class="zui-table-cell cell-s"><span>周立军</span></div></td>
                            <td ><div class="zui-table-cell cell-m"><span>全科</span></div></td>
                            <td ><div class="zui-table-cell cell-s"><span>科主任</span></div></td>
                        </tr>
                        </tbody>
                    </table>
                    <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
                </div>
            </div>
        </div>
        <div class="ksys-btn zui-table-tool" style="height: 66px">
            <button v-waves class="root-btn btn-parmary-d9" @click="close()">取消</button>
            <button v-waves class="root-btn btn-parmary" @click="submit">确定</button>
        </div>
    </div>
</div>
</body>
<script src="/newzui/currentCSS/js/vue/vuescroll.min.js"></script>
<script  src="swtl.js"></script>
</html>
