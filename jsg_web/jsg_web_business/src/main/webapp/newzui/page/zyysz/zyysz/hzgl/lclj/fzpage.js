var fzpage = new Vue({
    el: '.fzpage',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ischeck: false,
        printHtmlShow: false,
        forceUpdate: false,
        num: 0,
        isactiveClass: null,
        isactiveheaderClass: null,
        ismenu: true,
        ischecked: [
            {
                gz: [],
                zdyz: [],
                hlgz: [],
                bqby: [],
                ischeckedAll: '',
            }
        ],
        zxjlchecked: {
            gz: [],
            zdyz: [],
            hlgz: [],
            bqby: [],
        },
        contextmenuone: [
            {name: '左方新增阶段', icon: '/newzui/pub/image/zj.png', type: 'left'},
            {name: '右方新增阶段', icon: '/newzui/pub/image/zj.png', type: 'right'},
            {name: '编辑', icon: '/newzui/pub/image/bj.png', type: 'edit'},
            {name: '删除', icon: '/newzui/pub/image/sc.png', type: 'delete'},
        ],
        contextmenutwo: [
            {name: '上方新增项目', icon: '/newzui/pub/image/zj.png', type: 'top'},
            {name: '下方新增项目', icon: '/newzui/pub/image/zj.png', type: 'bottom'},
            {name: '编辑', icon: '/newzui/pub/image/bj.png', type: 'editChild'},
            {name: '删除整行', icon: '/newzui/pub/image/sc.png', type: 'deleteChild'},
        ],
        optionDefault: [{
            name: '住院第一天',
            id: '',
            csactive: false,
            gz: [{type: false, title: '无', csactive: false, id: ''}],
            zdyz: [{type: false, title: '无', csactive: false, id: ''}],
            hlgz: [{type: false, title: '无', csactive: false, id: ''}],
            bqby: [{type: false, title: '无', csactive: false, id: ''}],
        }],
        PrintOptionList:[],
        optionList: [
            {
                name: '住院第一天',
                csactive: false,
                gz: [
                    {type: false, title: '书写病历', csactive: false},
                    {type: true, title: '书写病历122', csactive: false},
                    {type: true, title: '书写病历1', csactive: false},
                    {type: true, title: '住院第一天', csactive: false},
                    {type: true, title: '住院第一dsdsd天', csactive: false},
                ],
                zdyz: [
                    {type: true, title: '术后长期', csactive: false},
                    {type: true, title: '术后长期', csactive: false}
                ],
                hlgz: [
                    {type: false, title: '普外科护理常规', csactive: false},
                ],
                bqby: [
                    {type: true, title: '一级护理', csactive: false},
                    {type: true, title: '一级qqqq护理', csactive: false},
                    {type: true, title: '一级qqqq一级护一级护理住院第二天理住院第二天护理', csactive: false},
                ],
            },
            {
                name: '住院第二天',
                gz: [
                    {type: true, title: '书写病历住院第二天', csactive: false},
                    {type: true, title: '书写病历住院第二天实打实打算书写病历住院第二天实打实打算', csactive: false},
                ],
                zdyz: [
                    {type: true, title: '术后长期住院第二天', csactive: false},
                ],
                hlgz: [
                    {type: false, title: '普外科护理常规住院第二天', csactive: false},
                ],
                bqby: [
                    {type: true, title: '一级护理住院第一级护理住院第二天一级护理住院第二天二天', csactive: false},
                ],
            },

        ],
        title: '',
        sydx: '',
        sm: '',
        bzts: '',
        popContent: {},
        gzElemArr:[],
        yzElemArr:[],
        HlElemArr:[],
        JlElemArr:[],
    },
    mounted: function () {
        this.getData();
        this.getLjbzTree();
    },
    methods: {
        cssAdd:function(index,$index,cs){
            return ''+cs+index+' '+cs+$index+''
        },
        classFun:function(index){
            return index % 4 !=0 ? true : false
        },

        getData: function () {
            var zyh = sessionStorage.getItem("lcljReportZyh");
            var hzrjObj = JSON.parse(sessionStorage.getItem("hzrjObj" + zyh));
            var lcljReportJdRange = JSON.parse(sessionStorage.getItem("lcljReportJdRange" + zyh));
            this.popContent = hzrjObj;
            let objParm = {
                yljgbm: jgbm,
                ryljbm: hzrjObj.ryzljbm
            };
            this.postAjaxT("/cpw-api/LcljWhBdmb/getBzLjDetail", JSON.stringify(objParm), function (result) {
                if (result.status === 200) {
                    if (result.data.datas.length > 0) {
                        fzpage.optionList = [];
                        for (let i = 0; i < result.data.datas.length; i++) {
                            //计算阶段住院日
                            var ksrq = lcljReportJdRange[i][0].rq;
                            var jsrq = lcljReportJdRange[i][(lcljReportJdRange[i].length - 1) > 0 ? (lcljReportJdRange[i].length - 1) : 0].rq;
                            if (ksrq == jsrq) {
                                result.data.datas[i].name = result.data.datas[i].name + ' (' + ksrq + ')';
                            } else {
                                result.data.datas[i].name = result.data.datas[i].name + ' (' + ksrq + '-' + jsrq + ')';
                            }
                            //主要诊疗工作
                            if (result.data.datas[i].gz.length <= 0) {
                                result.data.datas[i].gz = [{type: false, title: '无', csactive: false, id: ''}];
                            }
                            // 重点医嘱
                            if (result.data.datas[i].zdyz.length <= 0) {
                                result.data.datas[i].zdyz = [{type: false, title: '无', csactive: false, id: ''}];
                            }
                            // 主要护理工作
                            if (result.data.datas[i].hlgz.length <= 0) {
                                result.data.datas[i].hlgz = [{type: false, title: '无', csactive: false, id: ''}];
                            }
                            // 病情变异
                            if (result.data.datas[i].bqby.length <= 0) {
                                result.data.datas[i].bqby = [{type: false, title: '无', csactive: false, id: ''}];
                            }
                        }
                        fzpage.optionList = JSON.parse(JSON.stringify(result.data.datas));

                        fzpage.getBdzxjl();
                    } else {
                        fzpage.optionList = [];
                        fzpage.optionList = fzpage.optionDefault;
                    }
                } else {
                    fzpage.optionList = fzpage.optionDefault;
                }
            }, function () {
                malert("临床路径服务错误，请联系管理员！", "top", "defeadted");
            }, function (e, s) {
                if (s == 'timeout') {
                    malert("临床路径服务错误，请联系管理员！", "top", "defeadted");
                }
            }, 10000);
        },

        getLjbzTree: function () {
            var zyh = sessionStorage.getItem("lcljReportZyh");
            var ljbzTree = JSON.parse(sessionStorage.getItem("ljbzTreeObj" + zyh));
            this.title = ljbzTree.ryljmc;
            this.sydx = ljbzTree.sydx;
            this.sm = ljbzTree.sm;
            this.bzts = ljbzTree.bzts;
        },
        //表单执行记录
        getBdzxjl: function () {
            var zyh = sessionStorage.getItem("lcljReportZyh");
            var hzrjObj = JSON.parse(sessionStorage.getItem("hzrjObj" + zyh));
            var zxjlParam = {
                zyh: zyh,
                ryljbm: hzrjObj.ryljbm,
                ryzljbm: hzrjObj.ryzljbm,
                yljgbm: jgbm,
                ljdjid: hzrjObj.ljdjid,
            };
            var that = this;
            this.postAjaxT("/cpw-api/LcljYwZxjl/queryReport", JSON.stringify(zxjlParam),
                function (data) {
                    var jdList = data.data;
                    for (var index = 0; index < jdList.length; index++) {
                        var opObj = JSON.parse(JSON.stringify(fzpage.optionList[index]));
                        //诊疗工作
                        var gzList = opObj.gz;
                        loop:
                            for (var j = 0; j < gzList.length; j++) {
                                if (data.status == 200 && jdList[index].gz.length > 0) {
                                    for (var i = 0; i < jdList[index].gz.length; i++) {
                                        if (gzList[j].id == jdList[index].gz[i].bdxmbm) {
                                            opObj.gz[j].isChecked = true;
                                            opObj.gz[j].iszx = true;
                                            continue loop;
                                        }
                                    }
                                }
                            }
                        //重点医嘱
                        if (data.status == 200 && jdList[index].zdyz.length > 0) {
                            var zdyzList = [];
                            loop:
                                for (var i = 0; i < jdList[index].zdyz.length; i++) {
                                    var zdyz = {
                                        csactive: false,
                                        id: jdList[index].zdyz[i].yzxmbm,
                                        title: jdList[index].zdyz[i].yzxmmc,
                                        type: false,
                                    }
                                    zdyzList.push(zdyz);
                                    continue loop;
                                }
                            opObj.zdyz = zdyzList;
                        }
                        //护理工作
                        var hlgzList = opObj.hlgz;
                        loop:
                            for (var j = 0; j < hlgzList.length; j++) {
                                if (data.status == 200 && jdList[index].hlgz.length > 0) {
                                    for (var i = 0; i < jdList[index].hlgz.length; i++) {
                                        if (hlgzList[j].id == jdList[index].hlgz[i].bdxmbm) {
                                            opObj.hlgz[j].isChecked = true;
                                            continue loop;
                                        }
                                    }
                                }
                            }
                        //变异情况
                        var bqbyList = opObj.bqby;
                        loop:
                            for (var j = 0; j < bqbyList.length; j++) {
                                if (data.status == 200 && jdList[index].bqby.length > 0) {
                                    for (var i = 0; i < jdList[index].bqby.length; i++) {
                                        if (bqbyList[j].id == jdList[index].bqby[i].bdxmbm) {
                                            opObj.bqby[j].isChecked = true;
                                            continue loop;
                                        }
                                    }
                                }
                            }
                        that.optionList[index] = JSON.parse(JSON.stringify(opObj));
                    }
                    fzpage.PrintOptionList=fzpage.arrTrans(3,fzpage.optionList)
                    that.forceUpdate = false;
                    that.$nextTick(function () {
                        that.forceUpdate = true;
                    });
                    that.$forceUpdate();
                }, function (e) {
                    malert("临床路径服务错误，请联系管理员！", "top", "defeadted");
                });
        },
        closeAndOpenPage: function () {
            this.topClosePage('page/zyysz/zyysz/hzgl/hzzx/userPage/fzpage.html', 'page/zyysz/zyysz/hzgl/hzzx/hzzx.html');
            var str = JSON.parse(sessionStorage.getItem('userPage' + this.getQueryString('zyh')));
            str[1] = 4;
            str[0] = 'userPage/lclj';
            str[3] = new Date();
            sessionStorage.setItem('userPage' + this.getQueryString('zyh'), JSON.stringify(str))
        },
        menuActive: function (item) {
            if (item[0] == true) {
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-header').removeClass('active');
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-border').removeClass('active');
                $(item[4].currentTarget).addClass('active')
            } else if (item[0] == false) {
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-header').removeClass('active');
                $(item[4].currentTarget).parents('.jieduan-box').find('.item-border').removeClass('active');
                $(item[4].currentTarget).addClass('active')
            }

        },
        docheck: function () {
            this.ischeck = !this.ischeck;
            this.num = this.num + 1;
            this.optionList.forEach(function (br) {
                Vue.set(br, 'isCheckAll', fzpage.ischeck);
                br.gz.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
                br.zdyz.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
                br.hlgz.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
                br.bqby.forEach(function (yz) {
                    Vue.set(yz, 'isChecked', fzpage.ischeck)
                });
            });
            this.optionList.forEach(function (yz) {
                Vue.set(yz, 'num', fzpage.num)
            });
            this.$forceUpdate();
        },
        checkSelectSh: function (xmIndex, dayIndex, type, childText) {
            this.num = this.num + 1;
            if (type) {
                var isCheckAll = this.optionList[xmIndex].isCheckAll ? false : true;
                yzshInfo = this.optionList[xmIndex];
                this.ischeck = isCheckAll;
                // this.optionList[parentIndex].isCheckAll = isCheckAll;
                Vue.set(this.optionList[xmIndex], 'isCheckAll', isCheckAll);
                // this.ischeck = isCheckAll;
                for (var i = 0; i < yzshInfo.gz.length; i++) {
                    this.optionList[xmIndex].gz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.zdyz.length; i++) {
                    this.optionList[xmIndex].zdyz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.hlgz.length; i++) {
                    this.optionList[xmIndex].hlgz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.bqby.length; i++) {
                    this.optionList[xmIndex].bqby[i].isChecked = isCheckAll;
                }
            } else {
                var yzStatus = !this.optionList[dayIndex][childText][xmIndex].isChecked;
                // this.optionList[dayIndex][childText][xmIndex].isChecked = yzStatus;
                this.$set(this.optionList[dayIndex][childText][xmIndex], 'isChecked', yzStatus);
                if (yzStatus) {
                    var yzIsOverCk = true;
                    for (var t = 0; t < this.optionList[dayIndex].gz.length; t++) {
                        if (!this.optionList[dayIndex]['gz'][t].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var u = 0; u < this.optionList[dayIndex].zdyz.length; u++) {
                        if (!this.optionList[dayIndex]['zdyz'][u].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var y = 0; y < this.optionList[dayIndex].hlgz.length; y++) {
                        if (!this.optionList[dayIndex]['hlgz'][y].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var r = 0; r < this.optionList[dayIndex].bqby.length; r++) {
                        if (!this.optionList[dayIndex]['bqby'][r].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    // this.optionList[dayIndex].isCheckAll = yzIsOverCk;
                    this.$set(this.optionList[dayIndex], 'isCheckAll', yzIsOverCk);
                    this.ischeck = yzIsOverCk;
                } else {
                    this.optionList[dayIndex].isCheckAll = false;
                    this.ischeck = false;
                }
            }
            this.optionList.forEach(function (yz) {
                Vue.set(yz, 'num', fzpage.num)
            });
            this.$forceUpdate();
        },
        checked: function (hsindex, type, data) {
            var that = this;
            this.isckShow = false;
            var yzIsOverCk = true;
            if (type == 'all') {
                this.ischecked.ischeckedAll = !this.ischecked.ischeckedAll;
                for (var index = 0; index < data.gz; index++) {
                    that.ischecked.gz[index] = this.ischecked.ischeckedAll;
                }
                for (var index = 0; index < data.zdyz; index++) {
                    that.ischecked.zdyz[index] = this.ischecked.ischeckedAll;
                }
                for (var index = 0; index < data.hlgz; index++) {
                    that.ischecked.hlgz[index] = this.ischecked.ischeckedAll;
                }
                for (var index = 0; index < data.bqby; index++) {
                    that.ischecked.bqby[index] = this.ischecked.ischeckedAll;
                }
            } else {
                Vue.set(this.ischecked[type], hsindex, !this.ischecked[type][hsindex]);
                for (var index = 0; index < data.gz; index++) {
                    if (!that.ischecked.gz[index]) {
                        yzIsOverCk = false
                    }
                }
                for (var j = 0; j < data.zdyz.j; index++) {
                    if (!that.ischecked.zdyz[j]) {
                        yzIsOverCk = false
                    }
                }
                for (var t = 0; t < data.hlgz; t++) {
                    if (!that.ischecked.hlgz[t]) {
                        yzIsOverCk = false
                    }
                }
                for (var u = 0; u < data.bqby; u++) {
                    if (!that.ischecked.bqby[u]) {
                        yzIsOverCk = false
                    }
                }
                console.log(that.ischecked);
                this.ischecked.ischeckedAll = yzIsOverCk;
                this.ischeck = yzIsOverCk
            }
            this.$forceUpdate();
            // setTimeout(function () {
            this.$nextTick(function () {
                panel.isckShow = true
            })
            // },0)
        },
        menu: function (item) {
            this.ismenu = false;
            var str = {
                date: '住院第4天',
                gz: [
                    {type: true, title: ''}
                ],
                zdyz: [
                    {type: true, title: ''}
                ],
                hlgz: [
                    {type: false, title: ''}
                ],
                bqby: [
                    {type: true, title: ''}
                ],
            };
            switch (item[0].type) {
                case 'left':
                    this.optionList.splice(item[3] + 1, 0, this.optionList[item[3]]);
                    break;
                case 'right':
                    this.optionList.splice(item[3] == 0 ? 0 : item[3] - 1, 0, this.optionList[item[3]]);
                    break;
                case 'delete':
                    this.optionList.splice(item[3], 1, this.optionList[item[3]]);
                    break;
                case 'edit':
                    this.optionList.splice(item[3], 1, this.optionList[item[3]]);
                    break;
                case 'top':
                    this.optionList[item[3]][item[4]].splice(item[2] == 0 ? 0 : item[2], 0, this.optionList[item[3]][item[4]][item[2]]);
                    break;
                case 'bottom':
                    this.optionList[item[3]][item[4]].splice(item[2] + 1, 0, this.optionList[item[3]][item[4]][item[2]]);
                    break;
                case 'editChild':
                    this.optionList[item[3]][item[4]].splice(this.optionList[item[3]][item[4]][item[2]], 1, this.optionList[item[3]][item[4]]);
                    break;
                case 'deleteChild':
                    this.optionList[item[3]][item[4]].splice(this.optionList[item[3]][item[4]][item[2]], 1, this.optionList[item[3]][item[4]][item[2]]);
                    break;
            }
            this.$nextTick(function () {
                this.ismenu = true
            })
        },
        qtflj: function () {
            brzcList.index = 0
        },
        getAndSetData(){
            for (var i = 0; i <fzpage.PrintOptionList.length ; i++) {
                this.gzElemArr=[],this.yzElemArr=[],this.HlElemArr=[],this.JlElemArr=[];
                for (var j = 0; j <fzpage.PrintOptionList[i].length ; j++) {
                        this.gzElemArr.push($('.printChildGz'+j).height());
                        this.yzElemArr.push($('.printChildYz'+j).height());
                        this.HlElemArr.push($('.printChildHl'+j).height());
                        this.JlElemArr.push($('.printChildJl'+j).height());
                }
                $('.printChildGz'+i).height(Math.max.apply(null,this.gzElemArr))
                $('.printChildYz'+i).height(Math.max.apply(null,this.yzElemArr))
                $('.printChildHl'+i).height(Math.max.apply(null,this.HlElemArr))
                $('.printChildJl'+i).height(Math.max.apply(null,this.JlElemArr))
            }
        },
        doZjjl: function () {
            this.printHtmlShow = true;
            this.$nextTick(function () {
                this.getAndSetData()
                setTimeout(function () {
                    window.print()
                    fzpage.printHtmlShow = false;
                }, 50)
            })

        }
    },
});
var brzcList = new Vue({
    el: '#brzcList',
    data: {
        index: 1,
        treeData: [
            {
                id: "001",
                name: "混合痔临床路径",
                zljList: [
                    {id: "001001", name: "路径表单分路径"},
                    {id: "001002", name: "路径表单分路径2"},
                    {id: "001003", name: "路径表单分路径3"}
                ]
            },
            {
                id: "002",
                name: "药库管理",
                zljList: [
                    {id: "002001", name: "库房业务"},
                    {id: "002002", name: "库房业务1"},
                    {id: "002003", name: "库房业务2"},
                ]
            },
            {
                id: "003",
                name: "药房管理",
                zljList: [
                    {id: "003001", name: "药房业务"},
                    {id: "003002", name: "药房业务2"},
                    {id: "003003", name: "药房业务3"}
                ]
            },
            {
                id: "004",
                name: "门诊挂号",
                zljList: [{
                    id: "004001",
                    name: "医院业务"
                }]
            },
            {
                id: "005",
                name: "门诊收费",
                zljList: [
                    {id: "005001", name: "收费结算"},
                    {id: "005002", name: "收费结算2"},
                    {id: "005003", name: "收费结算3"}
                ]
            },
            {
                id: "006",
                name: "门诊医生站",
                zljList: [
                    {id: "006001", name: "诊疗管理"},
                    {id: "006002", name: "诊疗管理2"},
                    {id: "006003", name: "诊疗管理3"},
                ]
            },
            {
                id: "007",
                name: "住院管理",
                zljList: [
                    {id: "007001", name: "入出院管理"},
                    {id: "007002", name: "入出院管理2"},
                    {id: "007003", name: "入出院管理3"},
                ]
            }
        ]
    },
    methods: {

        checkedVal: function (val) {
        },
        closes: function () {
            this.index = 1
        },
        popShow: function () {
            pop.isShow = true
        }
    },
    created: function () {

    }
});
var pop = new Vue({
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    el: '.pop',
    data: {
        isShow: false,
        popContent: {},
    },
    methods: {
        Wf_save: function () {

        }
    },
});
laydate.render({
    elem: '#time'
    , trigger: 'click'
    , theme: '#1ab394'
    , done: function (value, data) {
        pop.popContent.time = value;
    }
});
