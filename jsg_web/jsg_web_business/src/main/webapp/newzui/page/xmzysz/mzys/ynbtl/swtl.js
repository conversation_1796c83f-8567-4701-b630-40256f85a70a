var panel=new Vue({
    el:'.panel',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        popContent:{}
    },
    create :function() {

    },
    methods:{
        fqtl:function () {
            brzcList1.type=false
        },
        getData:function(){
            var dg = {"page": 1, "rows": 20, "sort": "", "order": "asc"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1BqglYnbtl&types=query&dg=" +
                JSON.stringify(dg), function (json) {
                if (json.a == 0) {
                    hzList.jsonList = json.d.list;
                } else {
                    malert(json.c+",疑难病讨论信息列表查询失败",'','defeadted');
                    return false;
                }
            });
        },
    },
})
panel.getData();//初始化列表
var hzList=new Vue({
    el:'.hzList',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat,scrollOps],
    data:{
        popContent:{},
        jsonList:[],
        defaltObj:{
            title:'死亡讨论邀请',
            cs:'background-f4b26b color-8e9694',
            cb:'拒绝',
            sb:'接受'
        },
        refuseObj:{
            title:'拒绝原因',
        },
    },
    create:function(){

    },
    methods:{
        handleScroll:function(vertical, horizontal, nativeEvent) {
            if(horizontal.directionX==='left' || horizontal.directionX==='right'){
                this.$refs.tableHeader.style.marginLeft='-'+horizontal.scrollLeft+'px'
            }
        },
        newOpenPage:function(index){
            this.topNewPage('疑难病讨论','page/zyysz/bqgl/swtl/swtlUser/hzgl.html'); // 为了方便维护都在一个目录下面
            sessionStorage.setItem('ynbtlDetail',JSON.stringify(hzList.jsonList[index]));
        },
        accept:function(index){
            common.openConfirm('确定接受患者：<span class="color-green">'+"李浩然"+'</span>的死亡讨论吗？',function () {
                hzList.popContent.status = '2';
                hzList.popContent.relId = hzList.jsonList[index].relId;
                hzList.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=operate',
                    JSON.stringify(hzList.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('接收成功！','','success');
                        panel.getData();
                    } else {
                        malert('接收失败！','','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            },function () {
                common.openConfirm('<textarea placeholder="请输入拒绝原因" oninput="getJJYY(this)" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>',function () {
                    hzList.popContent.status = '3';
                    hzList.popContent.relId = hzList.jsonList[index].relId;
                    hzList.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=operate',
                        JSON.stringify(hzList.popContent)).then(function (data) {
                        if (data.body.a == 0) {
                            malert('拒绝成功！','','success');
                            panel.getData();
                        } else {
                            malert('拒绝失败！','','defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });
                },function () {

                },hzList.refuseObj)
            },this.defaltObj)
        },
        refuse:function(index){
            common.openConfirm('确定接受患者：<span class="color-green">'+"李浩然"+'</span>的死亡讨论吗？',function () {
                hzList.popContent.status = '2';
                hzList.popContent.relId = hzList.jsonList[index].relId;
                hzList.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=operate',
                    JSON.stringify(hzList.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('接收成功！','','success');
                        panel.getData();
                    } else {
                        malert('接收失败！','','defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            },function () {
                common.openConfirm('<textarea placeholder="请输入拒绝原因" oninput="getJJYY(this)" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>',function () {
                    hzList.popContent.status = '3';
                    hzList.popContent.relId = hzList.jsonList[index].relId;
                    hzList.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtlCyry&types=operate',
                        JSON.stringify(hzList.popContent)).then(function (data) {
                        if (data.body.a == 0) {
                            malert('拒绝成功！','','success');
                            panel.getData();
                        } else {
                            malert('拒绝失败！','','defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });
                },function () {

                },hzList.refuseObj)
            },this.defaltObj)
        },
    },
})
var brzcList=new Vue({
    el:'#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        isMulti:'one',
        type:true,
        popContent:{},
        jsonList:[],
        ryList:[]
    },
    create:function(){

    },
    methods:{
        fqtl:function () {

        },
        getData:function(){

        },
        submit:function () {
            var List = [];//选中的数据列
            if (this.isChecked.length > 0) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    var obj = {};
                    if (this.isChecked[i] == true) {
                        obj.cyrid = brzcList.ryList[i].rybm;
                        obj.yljgbm = brzcList.ryList[i].yljgbm;
                        obj.ksbm = brzcList.ryList[i].ksbm;
                        obj.ryxm = brzcList.ryList[i].ryxm;
                        List.push(obj);//将选中的元素，放到List
                    }
                }
            }
            if(brzcList.isMulti == 'one')//单选选主持人
            {
                brzcList1.zcrAvatar = List;
            }
            else {//多选选参与医生
                brzcList1.objAvatar = List;
            }
            this.type=true
        },
        close:function () {
            this.type=true
        }
    },
})
var brzcList1=new Vue({
    el:'#brzcList1',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    components: {
        'search-table': searchTable
    },
    data:{
        type:true,
        zcrAvatar:[],//主持人
        objAvatar:[],//参与医生
        ifClick: true,
        count:0,
        dgparm: {rows: 20000},
        popContent: {
            ywType:'1',
            tlType:'1'
        },
        selSearch: -1,
        searchCon: [],
        ryList: [],
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {'brxb': dic_transform.data.brxb_tran},
        them: {'患者姓名': 'brxm', '性别': 'brxb', '家庭地址': 'jzdmc'},
        ykth:null,
    },
    create:function(){

    },
    mounted:function(){
        this.popContent.tlsj = this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    brzcList1.popContent.tlsj = value;
                } else {
                    brzcList1.popContent.tlsj = '';
                }
            }
        });
    },
    methods:{
        tjys:function(flag){
            brzcList.isChecked = [];
            if(flag)
            {
                brzcList.isMulti = "some";
            }
            else
            {
                brzcList.isMulti = "one";
            }
            brzcList.type=false;
            var dg = {"page": 1, "rows": 20000, "sort": "", "order": "asc"};
            var bean = {"ysbz": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" +
                JSON.stringify(dg) + "&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    brzcList.ryList = json.d.list;
                } else {
                    malert(json.c+",医生列表查询失败",'','defeadted');
                    return false;
                }
            });
        },
        fqtl:function () {
        },
        close:function(){
            this.type=true
        },
        submit:function(){
            brzcList1.popContent['zcrId'] = brzcList1.zcrAvatar[0].cyrid;
            brzcList1.popContent.list = brzcList1.objAvatar;
            this.type=true;
            count(brzcList1.popContent.brid ,function(){
                if(brzcList1.count>0)
                {
                    malert("该患者已有相关讨论正在进行中。。。",'','defeadted');
                    return;
                }
                else {
                    brzcList1.$http.post('/actionDispatcher.do?reqUrl=New1BqglYnbtl&types=save',
                        JSON.stringify(brzcList1.popContent)).then(function (data) {
                        if (data.body.a == 0) {
                            malert('发起讨论成功！','','success');
                            panel.getData();
                        } else {
                            malert('发起讨论失败！','','defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            });
        },

        getData:function(){
            panel.getData();
        },
        //下拉查询讨论患者
        searching: function (add, type, val) {  // 搜索调用API的方法，add为true就表示请求下一页、为null就为请求第一页
            this.popContent[type] = val;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.popContent[type] == undefined || this.popContent[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.popContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
            $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrzc&types=queryCard&parm=" + JSON.stringify(str_param), function (json) {
                if (json.a == 0) {
                    var date = null;
                    if (add) {
                        // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                        for (var i = 0; i < json.d.list.length; i++) {
                            date = new Date(json.d.list[i]['csrq']);
                            json.d.list[i]['brnl'] = brzcList1.datetoage(brzcList1.fDate(date));
                            brzcList1.searchCon.push(json.d.list[i]);
                        }
                    } else {
                        for (var i = 0; i < json.d.list.length; i++) {
                            date = new Date(json.d.list[i]['csrq']);
                            json.d.list[i]['brnl'] = brzcList1.datetoage(brzcList1.fDate(date));
                            brzcList1.searchCon.push(json.d.list[i]);
                        }
                        brzcList1.searchCon = json.d.list;
                    }

                    brzcList1.page.total = json.d.total;
                    brzcList1.selSearch = 0;
                    if (json.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                } else {
                    malert("查询失败  " + json.c,'top','defeadted');
                }
            })
        },
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, 'text', this.popContent['text']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                count(item['brid'],function(){
                    if(brzcList1.count>0)
                    {
                        malert("该患者已有相关讨论正在进行中。。。",'','defeadted');
                        $(".selectGroup").hide();
                        return;
                    }
                    else {
                        Vue.set(brzcList1.popContent , 'brid' , item['brid']);
                        Vue.set(brzcList1.popContent , 'brxm' ,item['brxm']);
                        $(".selectGroup").hide();
                    }
                });
            }
        },
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == "text") {
                    Vue.set(this.popContent, 'text', this.popContent['brxm']);
                    Vue.set(this.popContent, 'brid', this.popContent['brid']);
                }
            }
        },
    },
});

var orignalSetItem = sessionStorage.setItem;
sessionStorage.setItem = function (key, newValue) {
    var setItemEvent = new Event('setItemEvent');
    setItemEvent.newValue = newValue;
    window.dispatchEvent(setItemEvent);
    orignalSetItem.apply(this, arguments);
};
window.addEventListener('storage',function (e) {
    if(e.key=='status'){
        panel.getData();
    }
});

function getJJYY(val){
    hzList.popContent.jjyy = $(val).val();
}
function count(brid,goON)
{
    brzcList1.popContent.brid = brid;
    $.getJSON("/actionDispatcher.do?reqUrl=New1BqglYnbtl&types=count" +
        "&json=" + JSON.stringify(brzcList1.popContent), function (json) {
        if (json.a == 0) {
            brzcList1.count = json.d;
            goON();
        } else {
            malert(json.c+",计算正在进行的讨论失败！",'','defeadted');
        }
    });
}