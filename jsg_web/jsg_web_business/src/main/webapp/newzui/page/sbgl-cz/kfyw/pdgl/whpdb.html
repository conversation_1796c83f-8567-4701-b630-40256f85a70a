<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="whpdb">
    <div class="toolMenu toolMenu_1" style="display: block;">
        <button @click="del"><span class="fa fa-scissors"></span>作废</button>
        <button @click="showPdb"><span class="fa fa-scissors"></span>刷新</button>
    </div>

    <div class="enter_tem1 enter_djList">
        <div class="enter_tem1_title">单据列表</div>
        <div class="table_tem2" style="height: calc(100% - 130px);">
            <table>
                <tr>
                    <th>盘点单号</th>
                    <th>盘点日期</th>
                    <th>制单人</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index),showDetail($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td v-text="item.pdpzh"></td>
                    <td v-text="item.pdrq"></td>
                    <td v-text="item.zdy"></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="enter_tem1 enter_djDetail">
        <div class="enter_tem1_title">单据细节</div>
        <h2 style="text-align: center">未核盘点表</h2>
        <div class="table_tem2" style="height: calc(100% - 190px)">
            <table>
                <tr>
                    <th>物资编码</th>
                    <th>物资名称</th>
                    <th>规格</th>
                    <th>物资批号</th>
                    <th>库存数量</th>
                    <th>实存数量</th>
                    <th>单位</th>
                    <th>生产批号</th>
                    <th>有效期至</th>
                    <th>库房单位</th>
                    <th>分装比例</th>
                    <th>产地</th>
                    <th>供货单位</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td v-text="item.wzbm"></td>
                    <td v-text="item.wzmc"></td>
                    <td v-text="item.wzgg"></td>
                    <td v-text="item.xtph"></td>
                    <td v-text="item.kcsl"></td>
                    <td v-text="item.scsl"></td>
                    <td v-text="item.yfdw"></td>
                    <td v-text="item.scph"></td>
                    <td v-text="item.yxqz"></td>
                    <td v-text="item.kfdw"></td>
                    <td v-text="item.fzbl"></td>
                    <td v-text="item.cdbm"></td>
                    <td v-text="item.ghdw"></td>
                </tr>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript" src="whpdb.js"></script>
