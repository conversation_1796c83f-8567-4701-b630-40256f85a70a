<link rel="stylesheet" href="loading-page/hzlb.css">
<div id="hzlb">
    <div class="panel">
        <div class="tong-top flex-jus-sb">
            <div>
                <button v-waves class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData()">刷新</button>
                <button v-waves class="tong-btn btn-parmary icon-ff" @click="caozuo('kjcz')">快捷操作</button>
                <button  v-waves class="tong-btn btn-parmary paddr-r5 icon-ff" @click="caozuo('shenhe')"><i class="icon-width icon-sh-b padd-l-25"></i>批量审核</button>
                <button v-waves class="tong-btn btn-parmary-b paddr-r5 icon-ff" @click="caozuo('zhixing')"><i class="icon-width icon-zhixing-l padd-l-25"></i>批量执行</button>
                <button v-waves class="tong-btn btn-parmary-b paddr-r5 icon-ff" @click="caozuo('shenling')"><i class="icon-width icon-lingyao-l padd-l-25"></i>批量申领</button>
                <button v-waves class="tong-btn btn-parmary-b paddr-r5 icon-ff" @click="caozuo('tzsh')"><i class="icon-width icon-tzfter"></i>批量停嘱审核</button>
                <button v-waves class="tong-btn btn-parmary-b icon-ff" @click="cydj()">采样登记</button>
                <button v-waves class="tong-btn btn-parmary-b icon-ff" @click="jkacOpen()">接科安床</button>
				<!-- <button v-waves class="tong-btn btn-parmary-b icon-ff" @click="csqdcsh()">测试</button>-->
            </div>
            <!--<button class="tong-btn btn-parmary-b paddr-r5 icon-ff" @click="caozuo('dayin')"><i class="paddr-r5 icon-dysq"></i>批量打印</button>-->
            <div class="text-right menu-right flex-container flex-align-c flex-jus-e">
                <span class="fa butt-hover fa-th-large" :class="{'active':num==0}" @click="show(0)"></span>
                <span class="relative fenge"></span>
                <span class="fa butt-hover fa-bars" :class="{'active':num==1}" @click="show(1)"></span>
            </div>
        </div>
    </div>
    <div class="tong-search">
        <div class="flex-container flex-align-c">
            <div class="flex-container flex-align-c padd-r-10">
                <label class="whiteSpace  ft-14 padd-r-5">科室</label>
                <div class="zui-input-inline wh120">
                    <select-input @change-data="resultChange,selectCB($event,'ks'),getQxbqcyCwData()" :data-notEmpty="true" :child="pageState.ksList"
                        :index="pageState.ks" :val="pageState.ks" :name="'pageState.ks'" :search="true"></select-input>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <label class="whiteSpace margin-r-5 ft-14">病人过滤</label>
                <select-input :cs="true" @change-data="selectCB" :not_empty="false"
                              :child="brgl_tran" :index="pageState.brgl" :val="pageState.brgl"
                              :name="'pageState.brgl'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="whiteSpace padd-r-5 ft-14">在院状态</span>
                    <select-input class="wh120" @change-data="selectCBz" :data-notEmpty="true" :child="zyYzclHszType_tran"
                        :index="pageState.zyType" :val="pageState.zyType" :name="'pageState.zyType'" :search="true"></select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="whiteSpace padd-r-5 ft-14">门特过滤</span>
                <select-input class="wh120" @change-data="selectCB" :data-notEmpty="true" :child="mtbr_tran"
                              :index="pageState.mtbr" :val="pageState.mtbr" :name="'pageState.mtbr'" :search="true"></select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10" v-show="pageState.zyType == 'jscy' || pageState.zyType ==  'bqcy'">
                <span class="whiteSpace padd-r-5 ft-14">出院时间</span>
                <div class=" flex-container flex-align-c ">
                    <input class="zui-input wh150" data-select="no" v-model="pageState.beginrq" placeholder="检索时间"  id="kssj"  type="text" />
                    <span>~</span>
                    <input class="zui-input wh150" data-select="no" v-model="pageState.endrq" placeholder="检索时间"  id="kssj1"  type="text" />
                </div>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="whiteSpace padd-r-5 ft-14">排序类型</span>
                <select-input class="wh120" @change-data="selectCB" :not_empty="false"
                    :child="sort_tran" :index="pageState.sort" :val="pageState.sort"
                    :name="'pageState.sort'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="whiteSpace padd-r-5 ft-14">排序方式</span>
                <select-input :cs="true" @change-data="selectCB" :not_empty="false"
                              :child="pxfs_tran" :index="pageState.order" :val="pageState.order"
                              :name="'pageState.order'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10" v-show="pageState.zyType == 'zy'">
                <span class="whiteSpace padd-r-5 ft-14 ">出院时间</span>
                    <input class="zui-input todate wh180 " data-select="no" @keydonw.13="getHzlb()" v-model="bqcyrq" placeholder="病区出院时间" id="bqcyrq"/>
            </div>
            
            <div class="flex-container flex-align-c">
                <span class="whiteSpace padd-r-5 ft-14 ">检索</span>
                    <input class="zui-input wh180" placeholder="床号/姓名" v-model="pageState.parm" @keyUp.enter="getData()"
                       />
            </div>
        </div>
    </div>
    <!--检索字段end-->

    <!--格子ui begin-->
    <div class="grid-box kp  bg-fff zui-table-body" ref="kp" v-cloak align="center" v-if="num==0" id="brCard">
        <div class="kpFlex"  @mousewheel="loadingData($event)">
        <div :class="{'zt-xrybr': br.jrrybz==1}" style="padding: 0 7.5px;width: 270px;display: inline-block" v-for="(br,index) in pageState.brList"
            v-if="pageState.brList.length!=0">
            <div v-if="!br.isKc" class="userWidth position" :class="{'active':index==activeIndex}" @click="activeClick(index)">
                <div class="header-text padd-l-10">
<!--                    <span class="userNameImg left">-->
<!--                        <span>-->
<!--                            <img :src="userPhoto[ br.nljd - 1 ]">-->
<!--                        </span>-->
<!--                    </span>-->
                    <div class="flex-container flex-align-b text-left padd-t-5">
                        <span class="userName  position " :data-title="br.brxm" @click="openBrInfo(br)" @dblclick="openYz(br)">{{br.brxm}}</span>
                        <span class="username-cwh" >
                            <span  class="font-20 color-wtg font-weight" >{{br.rycwbh}}床 </span>
                            <span  class="font-20 color-wtg font-weight"  v-show="br.mtbbz==1" >门特</span>
                        </span>
                    </div>
                    <p class="text-left">
						<span class="username-cwh" v-if="br.aac001 == null" :style="{color: br.aac001 == null?'red':''}">{{br.brfbmc}}&ensp;</span>
                        <span class="username-cwh" v-else>{{br.cbdqybqh}}{{xzlx_tran[br.xzlx]}}&ensp;</span>
                        <span class="margin-r-10">{{brxb_tran[br.brxb]}}</span>
                        <span class="margin-r-10">{{br.nl>0?(br.nl + nldw_tran[br.nldw]):''}}{{br.nl2?(br.nl2 + nldw_tran[br.nldw2]):''}}</span>
                        <span class="margin-r-10">{{br.zyh}}</span>
                        <span class="username-nl">{{br.zyts}}天</span>
                    </p>
                    <span  class="djzt" v-if="br.hldj!=0&&br.hldj!=null" :class="hldj_css[br.hldj]">{{hldj_tran[br.hldj]}}</span>
                </div>
                <div class="main-content">
					<p class="text-over" v-if="br.aac001" style="background-color:red;height:5px"></p>
                    <p class="text-over">{{br.ryzdmc || ""}}</p>
                    <p  style="color: red" v-if="br.gms">过敏史：{{br.gms}}</p>
                    <p class="text-over">入院时间 {{fDate(br.ryrq,'datetime')}}</p>
                    <p class="text-over">出院时间 {{fDate(br.cyrq,'datetime')}}</p>
					<p class="text-over">联系电话 {{br.sjhm}}</p>
                    <p>
                        合计：<span>{{br.fyhj}}</span>
                        </p>
                        <p>
                        	<span>预交：<span>{{br.yjhj}}</span></span>
                        </p>
                        <p v-if="br.yexm != null" style="color: blue;font-weight: 900;">
                        	<span>婴儿：<span>{{br.yexm}}</span></span>
                        </p>
                </div>
                <div class="footer-text padd-b-10 grid-box flex-container flex-align-c">
                    <div class="flex-one text-left">
                        <span v-if="br.pkhbz == '1'" class="userName-pin" data-title="贫困病人"><img src="/newzui/pub/image/pin.png"></span>
                        <span v-if="br.jrrybz == '1'" class="userName-pin xrybr" data-title="新入院病人">新</span>
                        <!--
                        <span v-if="br.hzbz == '1'" class="userName-pin hz" data-title="会诊病人">会</span>
                         -->
                        <span v-if="zyhMsg[br.zyh]" class="userName-pin r" data-title="待审核医嘱">审</span>

                        <span v-if="br.dzxshow" class="userName-pin w" data-title="未执长期医嘱">未</span>
                        <span v-if="br.bqcybz == '1' && br.zyzt == '0'" class="userName-pin j" data-title="病区出院">出</span>
                        <span v-if="br.bqcybz == '1' && br.zyzt == '1'" class="userName-pin j" data-title="结算出院">结</span>
                        <span v-if="br.yjhj+br.dbje-br.fyhj<0" class="userName-pin r" data-title="欠费">欠</span>
						<span v-if="br.bqdj == '1' " class="userName-pin r" data-title="重">重</span>
						<span v-if=" br.bqdj == '2'" class="userName-pin r" data-title="危">危</span>
                        <span v-if="br.dtzshow" class="userName-pin r" data-title="停嘱">停</span>
						<span v-if="br.dd =='1'" class="userName-pin d" data-title="跌倒">跌</span>
						<span v-if="br.zc =='1'" class="userName-pin d" data-title="坠床">坠</span>
						<span v-if="br.yc && br.yc !='0'" class="userName-pin d" data-title="压疮">压</span>
                    </div>
                    <div class="text-left margin-r-5">
                        <span class="zyys text-over">{{br.zyysxm}}&emsp;{{br.zrhsxm}}</span>
                    </div>
<!--                    :class="{'active': isActive[index]}"-->
                    <div class="cz-butt" :id="'cz-'+index"  :class="{'active': isActive[index]}" :data-id="isActive[index]"  @dblclick.stop >
                        <span class="butt" @click.stop="czClick(index, $event)">操作</span>
                        <transition name="top-bottom-fade">
                        <div class="content flex-container flex-wrap-w" v-show="isActive[index]">
                            <div v-for="(cz, $index) in czList" class="cont-butt" @click="cz.clickBc(index,br)">{{
                                cz.name }}</div>
                        </div>
                        </transition>
                    </div>
                    <!-- <span class="userName-lc" style="display: none"><img src="/newzui/pub/image/cp.png"></span> -->
                    <!-- <div class=" text-center">
                        <span class="user-footer-img ljImg" @click="caozuo('shenhe',[br])" data-title="审核医嘱"></span>
                    </div>
                    <div class=" text-center">
                        <span class="user-footer-img yzImg  " data-title="危重护理" @click="caozuo('huli',br)"></span>
                    </div>
                    <div class=" text-center">
                        <span class="user-footer-img blImg  " data-title="三测" @click="caozuo('sance',br)"></span>
                    </div>
                    <div class=" text-center caozuo">
                        <span class="user-footer-img sbImg relative more icon-more hover" onmouseover="moreMouse(this,true)"
                            onmouseout="moreMouse(this)">
                            <span class="content">
                                <span class="relative">
                                    <i class="icon-icon icon-chuyuan-b" data-title="病区出院" @click="caozuo('bqcy',[br])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-quxiaochuyuan-b" data-title="取消病区出院" @click="caozuo('qxbqcy',[br])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-lingyao-b" data-title="领药" @click="caozuo('shenling',[br])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-zhixing-b" data-title="执行" @click="caozuo('zhixing',[br])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-quxiaolingyao-b" data-title="取消领药" @click="caozuo('qxsl',[br])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-tyHsz-b" data-title="退药申请" @click="caozuo('tysq',[br])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-ghys-b" data-title="更换医生" @click="changeDoc(index)"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-tingzhu-b" data-title="停嘱审核" @click="caozuo('tzsh',[br])"></i>
                                </span>
                            </span>
                        </span>
                    </div> -->
                </div>
            </div>
            <!-- 空床 -->
            <div v-if="br.isKc" class="userWidth kongchuang" style="height: 120px;">
                <p class="chuangweihao text-left">{{br.rycwbh}}床</p>
            </div>
        </div>
        <div style="padding: 0 7.5px;width: 270px;display: inline-block" v-for="list in brk_listD" v-if="pageState.brList.length!=0"></div>
            <div v-if="pageState.brList.length!=0 && !isDoneCb " class=" ysb-green text-center" style="width: 100%;margin:30px 0">{{loadData}}</div>
        <p v-if="pageState.brList.length==0" class=" noData text-center">暂无数据...</p>
        </div>
    </div>
    <!--格子ui end-->

    <!--循环列表begin-->
    <div v-if="num==1" class="zui-table-view">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'brListCheckBox'" :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>住院号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>性别</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>年龄</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>床位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>病人类型</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>入院时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>出院时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>诊断名称</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th class="cell-xl">
                            <div class="zui-table-cell cell-xl"><span>操作</span></div>
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body no-user-select" @scroll="scrollTable($event)">
            <p v-if="!pageState.brList.length" class="noData  text-center zan-border">暂无数据...</p>
            <table v-if="pageState.brList.length" class="zui-table table-width50">
                <tbody>
                    <tr v-for="(item, $index) in pageState.brList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'some','jsonList'],$event)"
                        @dblclick="openYz(item)" :tabindex="$index" ref="list">
                        <!--@dblclick="edit($index)"双击回调-->
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBoxCB" :list="'brListCheckBox'" :type="'some'" :which="$index"
                                :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zyh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="brxb_tran[item.brxb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="item.nl+nldw_tran[item.nldw]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bxlbmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="fDate(item.ryrq,'datetime')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="fDate(item.cyrq,'datetime')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left title" v-text="item.ryzdmc || item.xyryzd"></div>
                        </td>
                        <td >
                            <div class="zui-table-cell zt cell-s" @click="ztClick($index)">
                                <span v-show="item.dshshow" class="zt-dsh" @click="caozuo('shenhe',[item])">待审核</span>
                                <span v-show="item.dzxshow" class="zt-dzx" @click="caozuo('zhixing',[item])">待执行</span>
                                <span v-show="item.dlyshow" class="zt-dly" @click="caozuo('shenling',[item])">待领药</span>
                                <span v-show="item.dtzshow" class="zt-dtz" @click="caozuo('tzsh',[item])">待停嘱</span>
                            </div>
                            <pop-page v-if="ztShow[$index]" :date="'date'" :text="'text'" :type="'type'" :lsit="objData"
                                @reslut="reslut"></pop-page>
                        </td>
                        <td class="cell-xl">
                            <div class="zui-table-cell caozuo cell-xl" over-auto style="padding: 0;">
                                <span class="relative">
                                    <i class="icon-icon icon-shenhe" data-title="审核" @click="caozuo('shenhe',[item])"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-huli" data-title="危重护理" @click="caozuo('huli',item)"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-sance" data-title="三测" @click="caozuo('sance',item)"></i>
                                </span>
                                <span class="relative">
                                    <i class="icon-icon icon-dy-h" data-title="打印" @click="caozuo('dy',item)"></i>
                                </span>
                                <span class="relative more icon-icon icon-more hover" @dblclick.stop onmouseover="moreMouse(this,true)"
                                    onmouseout="moreMouse(this)">
                                    <span class="content">
                                        <span class="relative">
                                            <i class="icon-icon icon-chuyuan-b" data-title="病区出院" @click="caozuo('bqcy',[item])"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-quxiaochuyuan-b" data-title="取消病区出院" @click="caozuo('qxbqcy',[item])"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-lingyao-b" data-title="领药" @click="caozuo('shenling',[item])"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-zhixing-b" data-title="执行" @click="caozuo('zhixing',[item])"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-quxiaolingyao-b" data-title="取消领药" @click="caozuo('qxsl',[item])"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-tyHsz-b" data-title="退药申请" @click="caozuo('tysq',[item])"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-ghys-b" data-title="更换医生" @click="changeDoc($index)"></i>
                                        </span>
                                        <span class="relative">
                                            <i class="icon-icon icon-tingzhu-b" data-title="停嘱审核" @click="caozuo('tzsh',[item])"></i>
                                        </span>
                                    </span>
                                </span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'brListCheckBox'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                        <tr v-for="(item, $index) in pageState.brList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'some','jsonList'],$event)"
                            @dblclick="openYz(item)" :tabindex="$index" ref="list">
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'brListCheckBox'" :type="'some'" :which="$index"
                                    :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                        <tr>
                            <th class="cell-xl">
                                <div class="zui-table-cell cell-xl"><span>状态</span></div>
                            </th>
                            <th class="cell-xl">
                                <div class="zui-table-cell cell-xl"><span>操作</span></div>
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                        <tr v-for="(item, $index) in pageState.brList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'some','jsonList'],$event)"
                            @dblclick="openYz(item)" :tabindex="$index" ref="list">
                            <td class="cell-xl">
                                <div class="zui-table-cell zt cell-xl" @click="ztClick($index)">
                                    <span v-show="item.dshshow" class="zt-dsh" @click="caozuo('shenhe',[item])">待审核</span>
                                    <span v-show="item.dzxshow" class="zt-dzx" @click="caozuo('zhixing',[item])">待执行</span>
                                    <span v-show="item.dlyshow" class="zt-dly" @click="caozuo('shenling',[item])">待领药</span>
                                    <span v-show="item.dtzshow" class="zt-dtz" @click="caozuo('tzsh',[item])">待停嘱</span>
                                </div>
                                <pop-page v-if="ztShow[$index]" :date="'date'" :text="'text'" :type="'type'" :lsit="objData"
                                    @reslut="reslut"></pop-page>
                            </td>
                            <td class="cell-xl">
                                <div class="zui-table-cell caozuo cell-xl" over-auto>
                                    <span class="relative">
                                        <i class="icon-icon icon-shenhe" data-title="审核" @click="caozuo('shenhe',[item])"></i>
                                    </span>
                                    <span class="relative">
                                        <i class="icon-icon icon-huli" data-title="护理" @click="caozuo('huli',item)"></i>
                                    </span>
                                    <span class="relative">
                                        <i class="icon-icon icon-sance" data-title="三测" @click="caozuo('sance',item)"></i>
                                    </span>
                                    <span class="relative">
                                        <i class="icon-icon icon-dy-h" data-title="打印" @click="caozuo('dy',item)"></i>
                                    </span>
                                    <span class="relative more icon-icon icon-more hover" @dblclick.stop onmouseover="moreMouse(this,true)"
                                        onmouseout="moreMouse(this)">
                                        <span class="content">
                                            <span class="relative">
                                                <i class="icon-icon icon-chuyuan-b" data-title="病区出院" @click="caozuo('bqcy',[item])"></i>
                                            </span>
                                            <span class="relative">
                                                <i class="icon-icon icon-lingyao-b" data-title="领药" @click="caozuo('shenling',[item])"></i>
                                            </span>
                                            <span class="relative">
                                                <i class="icon-icon icon-zhixing-b" data-title="执行" @click="caozuo('zhixing',[item])"></i>
                                            </span>
                                            <span class="relative">
                                                <i class="icon-icon icon-quxiaolingyao-b" data-title="取消领药" @click="caozuo('qxsl',[item])"></i>
                                            </span>
                                            <span class="relative">
                                                <i class="icon-icon icon-tyHsz-b" data-title="退药申请" @click="caozuo('tysq',[item])"></i>
                                            </span>
                                            <span class="relative">
                                                <i class="icon-icon icon-ghys-b" data-title="更换医生" @click="changeDoc($index)"></i>
                                            </span>
                                            <span class="relative">
                                                <i class="icon-icon icon-tingzhu-b" data-title="停嘱审核" @click="caozuo('tzsh',[item])"></i>
                                            </span>
                                        </span>
                                    </span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
<!--        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
    </div>
    <!--循环列表end-->

    <div class="action-bar fixed">
        <div class="hzts-message">
            <span @click="filterFunz()" class="box">{{zycymc}}&ensp;<span class="color-1abc9c"><span class="font-22">{{ noticeContent.zyrs || 0 }}</span>人</span></span>
            <span @click="filterFun(1,'jrrybz')" class="box">今日入院&ensp;<span class="color-1abc9c"><span class="font-22">{{ noticeContent.jrry || 0 }}</span>人</span></span>
            <span @click="filterFun(1,'hldj')" class="box">特级&ensp;<span class="color-fe3f3f"><span class="font-22">{{ noticeContent.tjhl || 0 }}</span>人</span></span>
            <span @click="filterFun(2,'hldj')" class="box">一级&ensp;<span class="color-fa6969"><span class="font-22">{{ noticeContent.yjhl || 0 }}</span>人</span></span>
            <span @click="filterFun(3,'hldj')" class="box">二级&ensp;<span class="color-3ba4ff"><span class="font-22">{{ noticeContent.ejhl || 0 }}</span>人</span></span>
            <span @click="filterFun(4,'hldj')" class="box">三级&ensp;<span class="color-494d50"><span class="font-22">{{ noticeContent.sjhl || 0 }}</span>人</span></span>
            <span @click="filterFun(2,'bqdj')"  class="box">病危&ensp;<span class="color-fe3f3f"><span class="font-22">{{ noticeContent.bwrs || 0 }}</span>人</span></span>
            <span @click="filterFun(1,'bqdj')"  class="box">病重&ensp;<span class="color-fe3f3f"><span class="font-22">{{ noticeContent.bzrs || 0 }}</span>人</span></span>
            <span @click="getQueryKc()" class="box">空床&ensp;<span class="color-494d50"><span class="font-22">{{ noticeContent.kc || 0 }}</span>床</span></span>
            <span v-if="zycymc !='出院'" @click="filterFunq()" class="box">欠费&ensp;<span class="color-ef904d"><span class="font-22">{{ noticeContent.qfrs || 0 }}</span>人</span></span>
        </div>
    </div>
</div>

<div class="side-form pop-width" style="padding-top: 0;" id="ghys" role="ghys" :class="{'ng-hide': !isShow}">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <div>
            <i>原主管医生</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                    :index_val="'rybm'" :val="brInfo.zyys" :name="'brInfo.zyys'" :search="true" :disable="true"></select-input>
            </div>
        </div>
        <div>
            <i>新主管医生</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                    :index_val="'rybm'" :val="doctor" :name="'doctor'" :search="true"></select-input>
            </div>
        </div>

        <div>
            <i>原责任护士</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="hsListData" :index="'ryxm'"
                              :index_val="'rybm'" :val="brInfo.zrhs" :name="'brInfo.zrhs'" :search="true" :disable="true"></select-input>
            </div>
        </div>
        <div>
            <i>新责任护士</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="hsListData" :index="'ryxm'"
                              :index_val="'rybm'" :val="nurse" :name="'nurse'" :search="true"></select-input>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveData()">确定</button>
    </div>
</div>

<!-- 病人个人信息 begin -->
<div class="side-form printHide  pop-width" :class="{'ng-hide':!isShow}" v-cloak id="brInfo" role="form">
    <div class="fyxm-side-top flex-between">
        <span>患者信息</span>
        <span class="fr closex ti-close" @click="close"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list1 useritem flex-start">
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e  flex-align-c">住院号</span>
                <span class="userValue flex-container flex-align-c" v-text="brxxContent.zyh"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">姓名</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.brxm"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">性别</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxb_tran[brxxContent.brxb]"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">年龄</span>
                <span class="userValue flex-container  flex-align-c" v-text="(brxxContent.nl + nldw_tran[brxxContent.nldw]) + (brxxContent.nl2?(brxxContent.nl2 + nldw_tran[brxxContent.nldw2]):'')"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">工作单位</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.gzdw"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">家庭住址</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.jzdmc"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">联系电话</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.lxdh"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">过敏史</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.gms"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">入院费别</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.brfbmc"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">保险</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.bxlbmc"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">入院时间</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDate(brxxContent.ryrq,'yyyy-MM-dd')"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">入科时间</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDate(brxxContent.jrrq,'yyyy-MM-dd')"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">住院天数</span>
                <span class="userValue flex-container  flex-align-c">{{brxxContent.zyts}}天</span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">入院情况</span>
                <span class="userValue flex-container  flex-align-c" v-text="ryqk_tran[brxxContent.ryqk]"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">科室</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.ryksmc"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">主管医生</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.zyysxm"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">责任护士</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.zrhsxm"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">担保日期</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDate(brxxContent.ryrq,'yyyy-MM-dd')"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">担保人</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.dbr"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">担保金额</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.dbje"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">费用合计</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDec(brxxContent.fyhj,2)"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">预交合计</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDec(brxxContent.yjhj,2)"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">账户余额</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDec(brxxContent.yjhj+brxxContent.dbje+brxxContent.jzxe-brxxContent.fyhj,2)"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">记账限额</span>
                <span class="userValue flex-container  flex-align-c" v-text="fDec(brxxContent.jzxe,2)"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">西医诊断</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.xyryzd"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">中医诊断</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.zyryzd"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">病人状况</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.brqk"></span>
            </li>
            <li class="userlist flex-container">
                <span class="userKey flex-container flex-jus-e flex-align-c">门特患者</span>
                <span class="userValue flex-container  flex-align-c" v-text="brxxContent.mtbbz=='1' ? '是':'否'"></span>
            </li>
        </ul>
    </div>
</div>
<!-- 病人个人信息 end -->

<!-- 转科换床 begin -->
<div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}"  id="zkac" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <div>
            <i>主管医生</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                    :index_val="'rybm'" :val="zkhcContent.zyys" :name="'zkhcContent.zyys'" :search="true"></select-input>
            </div>
        </div>
        <div>
            <i>原科室</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :not_empty="false" :child="Kslist" :index="'ksmc'" :index_val="'ksbm'"
                    :val="zkhcContent.ryks" :name="'zkhcContent.ryks'" :search="true" disabled="disabled"></select-input>
            </div>
        </div>
        <div>
            <i>转往科室</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :not_empty="false" :child="Kslist" :index="'ksmc'" :index_val="'ksbm'"
                    :val="zkhcContent.zrksbm" :name="'zkhcContent.zrksbm'" :search="true"></select-input>
            </div>
        </div>
        <!--
            <div>
                <i>床位</i>
                <div class="margin-top-5 margin-b-20">
                    <select-input @change-data="resultChange"
                                  :data-notEmpty="false"
                                  :child="cwList"
                                  :index="'cwbh'"
                                  :index_val="'cwid'"
                                  :val="zkhcContent.cwid"
                                  :name="'zkhcContent.cwid'"
                                  :search="true"></select-input>
                </div>
            </div>
             -->
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveZkhc()">确定</button>
    </div>
</div>
<!-- 转科换床 end -->

<!-- 迁床处理 begin -->
<div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}"  id="qccl" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <div>
            <i>病人姓名</i>
            <div class="margin-top-5 margin-b-20">
                <input type="text" class="zui-input" disabled v-model="brInfo.brxm" />
            </div>
        </div>
        <div>
            <i>主管医生</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                    :index_val="'rybm'" :val="brInfo.zyys" :name="'brInfo.zyys'" :search="true"></select-input>
            </div>
        </div>
        <div>
            <i>原床位</i>
            <div class="margin-top-5 margin-b-20">
                <input type="text" class="zui-input" disabled v-model="brInfo.rycwbh" />
            </div>
        </div>
        <div>
            <i>现床位</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="resultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                    :index_val="'cwid'" :val="qcContent.cwid" :name="'qcContent.cwid'" :search="true"></select-input>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveQccl">确定</button>
    </div>
</div>
<!-- 迁床处理 end -->


<div class="side-form  pop-width" :class="{'ng-hide':Class}" v-cloak  id="qxbqcy" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <div>
            <i>床位</i>
            <div class="margin-top-5 margin-b-20">
                <select-input @change-data="commonResultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                              :index_val="'cwid'" :val="jkacContent.cwid" :name="'jkacContent.cwid'" :search="true"></select-input>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveJkac()">确定</button>
    </div>
</div>

<!-- 跌到坠床压疮 begin -->
<div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}"  id="ddzcyc" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <div>
            <i>病人姓名</i>
            <div class="margin-top-5 margin-b-20">
                <input type="text" class="zui-input" disabled v-model="brInfo.brxm" />
            </div>
        </div>
        <div>
            <i>是否跌倒风险</i>
            <div class="margin-top-5 margin-b-20">
                <select-input :cs="true" @change-data="selectCB" :not_empty="false"
                              :child="ddzc" :index="brInfo.dd" :val="brInfo.dd"
                              :name="'pageState.dd'">
                </select-input>
            </div>
        </div>
        <div>
            <i>是否坠床风险</i>
            <div class="margin-top-5 margin-b-20">
                <select-input :cs="true" @change-data="selectCB" :not_empty="false"
                              :child="ddzc" :index="brInfo.zc" :val="brInfo.zc"
                              :name="'brInfo.zc'">
                </select-input>
            </div>
        </div>
        <div>
            <i>压疮</i>
            <div class="margin-top-5 margin-b-20">
                <select-input :cs="true" @change-data="selectCB" :not_empty="false"
                              :child="ycdj" :index="brInfo.yc" :val="brInfo.yc"
                              :name="'brInfo.yc'">
                </select-input>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveDdzcyc">确定</button>
    </div>
</div>
<!-- 迁床处理 end -->


<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script src="loading-page/hzlb.js"></script>
