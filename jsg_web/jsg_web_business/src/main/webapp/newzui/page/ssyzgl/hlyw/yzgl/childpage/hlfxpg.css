.toolMenu{
    width: 100%;
    margin: 3px 0 0 0;
    border-bottom: 4px solid #eee;
}

#jlxq{
    width:100%;
    /*height: calc(100% - 48px);*/
    padding: 3px;
    overflow: hidden;
}

.jlxq_title{
    position: absolute;
    top: -10px;
    background-color: #FFFFFF;
    font-size: 14px;
    min-width: 50px;
    text-align: center;
}

.common-css input{
    text-indent: 0;
}
.cm{
    right: 15px;
}
.line-height-12{
    line-height: 2.3;
}
#model{
    z-index: 110;
}
.bqcydj_model{
    width: auto;
    height: 320px;
    overflow: hidden;
}
.wh20{
	width:20%
}
.wh81{
	width:78%
}


.fxpg > table{
	border:1px solid black;
	width: 100%;
	table-layout:fixed;
	word-break:break-all;
}
.fxpg table th
{
	text-align:center;
	border:1px solid black;
}
.fxpg table td
{
	text-align:center;
	border:1px solid black;
}
.fxpg table input {
	width: 100%;
}

.fxpg-h2{
	font-size: x-large;
    font-weight: bolder;
    text-align: center;
}
