<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>医保清算</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
	<link href="index.css" rel="stylesheet" />
	<style>

		#pop{
			background-color: hsla(170, 100%, 4%, .5) !important;
			position: absolute;
			    top: 10%;
			    width: 70%;
			    height: 70%;
			    left: 15%;
				border: 1px solid;

		}
	</style>
</head>
<body class="skin-default">
<div class="wrapper background-f">

    <!--入院登记查询列表视图begin-->
    <div id="tableInfo" v-show="isShow" v-cloak>
        <!--入院登记功能按钮begin-->
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
                <button class="tong-btn  btn-parmary" @click="ShowMz()">新增</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="getData()">刷新</button>
				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">审核状态</span>
					<select-input class="wh122" @change-data="selectCzyBz" :not_empty="false"
								  :child="shzt_tran" :index="param.zt" :val="param.zt"
								  :name="'param.zt'">
					</select-input>
				</div>
				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">开始日期</span>
					<input class="zui-input times1" placeholder="请选择对账开始日期" id="qstimeVal" />
				</div>
				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">结束日期</span>
					<input class="zui-input times1" placeholder="请选择对账结束日期" id="qstimeVal2"/>
				</div>
				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">社保机构</span>
					<select-input class="wh122" @change-data="selectCzyBz" :not_empty="false"
								  :child="qsfzxname_tran" :index="param.qsfzxname" :val="param.qsfzxname"
								  :name="'param.qsfzxname'">
					</select-input>
				</div>
				<div class="flex-container flex-align-c  margin-l-20">
					<span class="whiteSpace margin-r-5 ft-14">清算类别</span>
					<select-input class="wh122" @change-data="selectCzyBz" :not_empty="false"
								  :child="dmmc_tran" :index="param.dmmc" :val="param.dmmc"
								  :name="'param.dmmc'">
					</select-input>
				</div>
            </div>

        </div>
        <!--循环列表begin-->
        <div class="zui-table-view padd-r-10 padd-l-10" id="brRyList">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-l"><span>医院编码</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>社保机构</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>清算类别</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>年度</span></div>
                        </th>
                        <th >
                            <div class="zui-table-cell cell-s"><span>费用总额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>个人账户</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>医保基金</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>申请人</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>申请日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>清算开始时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>清算截止日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>清算流水号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>审核状态</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table" v-if="jsonList.length!=0">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"   :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">

                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.yybm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.qsfzxname"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.dmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fysqnd"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yka055"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yka065"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yka107"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ryxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.qssqrq,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.qsksrq"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yke151"></div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ykb053"></div>
                        </td>
                        <td>

							<div class="zui-table-cell cell-s">{{shzt_tran[item.zt]}}</div>
                        </td>
						<td ><!--操作-->
						    <div class="zui-table-cell cell-s flex-container flex-align-c flex-jus-c">
						        <i class="icon-icon icon-sh margin-r-10"  v-if="item.zt==2" title="清算" @click="qingsuan($index)"></i>
								<i class="icon-icon icon-zf margin-r-10"  v-if="item.zt==2" title="作废" @click="huitui($index)"></i>
								<i class="icon-icon icon-th margin-r-10"  v-if="item.zt==0" title="回退" @click="huitui($index)"></i>
								<i class=" wb-print"   title="打印" @click="printFun($index)"></i>
						    </div>
						</td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
            </div>
			
			<!--左侧固定-->
			<div class="zui-table-fixed table-fixed-r">
			    <div class="zui-table-header">
			        <table class="zui-table">
			            <thead>
			            <tr>
			                <th class="cell-s">
			                    <div class="zui-table-cell cell-s"><span>操作</span></div>
			                </th>
			            </tr>
			            </thead>
			        </table>
			    </div>
			    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
			        <table class="zui-table">
			            <tbody>
			            <tr v-for="(item, $index) in jsonList"   :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
			                <td ><!--操作-->
			                    <div class="zui-table-cell cell-s flex-container flex-align-c flex-jus-c">
			                        <i class="icon-icon icon-sh margin-r-10"  v-if="item.zt==2" title="清算" @click="qingsuan($index)"></i>
			                		<i class="icon-icon icon-zf margin-r-10"  v-if="item.zt==2" title="作废" @click="huitui($index)"></i>
			                		<i class="icon-icon icon-th margin-r-10"  v-if="item.zt==0" title="回退" @click="huitui($index)"></i>
			                		<i class=" wb-print"   title="打印" @click="printFun($index)"></i>
			                    </div>
			                </td>
			            </tr>
			            </tbody>
			        </table>
			    </div>
			</div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
        <!--循环列表end-->
    </div>


	<div id="pop" v-show="isShow" v-cloak>

	    <transition enter-active-class="animated zoomIn" leave-active-class="animated zoomOut">
			<div v-show="isShow" class="pop-open" style="z-index: 6666;background-color: #fff;">
	        <div class="fyxm-side-top">
	            <span>新增清算单</span>
	            <span class="fr closex ti-close" @click="closes"></span>
	        </div>
	        <div class="flex" style="padding: 13px 0;">
	        	
	        	<div class="flex flex_items  margin-l-20">
	        	    <label class="whiteSpace margin-r-5 ft-14">申请人</label>
	        	    <input class="zui-input todate wh182 " v-model="loginuserName" disabled="disabled" />
	        	</div>
	            <div class="flex flex_items  margin-l-20">
	                <label class="whiteSpace margin-r-5 ft-14">时间</label>
	                <div class="position margin-l13 flex-container flex-align-c">
	                    <i class="icon-position icon-rl"></i>
	                    <input class="zui-input times1" placeholder="请选择清算开始时间" id="timeVal" style="padding-left: 40px;"/>~
						<i class="icon-position icon-rl"></i>
						<input class="zui-input times1" placeholder="请选择清算结束时间" id="timeVal2" style="padding-left: 40px;"/>
	                </div>
	            </div>
				<div class="flex flex_items  margin-l-20">
				<div class="tong-btn  btn-parmary" @click="Receive">接收</div>
				</div>
	        </div>
	        <div class="ksys-side">
	            <div class="zui-table-view" style="height: unset;">
	                <div class="zui-table-header">
	                    <table class="zui-table table-width50">
	                        <thead>
	                        <tr>
	                            <th class="cell-m">
										<input-checkbox @result="reCheckBox" :list="'mxfyList'" :type="'all'" :val="isCheckAll">
										 </input-checkbox>
	                            </th>
	                            <th >
	                                <div class="zui-table-cell cell-s"><span>中心</span></div>
	                            </th>
	                            <th >
	                                <div class="zui-table-cell cell-l"><span>清算类别</span></div>
	                            </th>
	                            <th >
	                                <div class="zui-table-cell cell-s"><span>年度</span></div>
	                            </th>
	                            <th >
	                                <div class="zui-table-cell cell-s"><span>费用总额</span></div>
	                            </th>
	                            <th>
	                                <div class="zui-table-cell cell-s"><span>个人账户</span></div>
	                            </th>
	                            <th>
	                                <div class="zui-table-cell cell-s"><span>医保基金</span></div>
	                            </th>
								<th>
								    <div class="zui-table-cell cell-s"><span>个人现金</span></div>
								</th>
	                        </tr>
	                        </thead>
	                    </table>
	                </div>
	                <div class="zui-table-body">
	                    <table class="zui-table table-width50">
	                        <tbody>
	                        <tr v-for="(item, $index) in mxfyList" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
							<td class="cell-m">
							  <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'some'" :which="$index"
							  :val="isChecked[$index]" name="mxfyList">
							  </input-checkbox>
							                                </td>
							
							<td >
								<div class="zui-table-cell cell-s"  v-text="qsjbjg_tran[item.qsjbjg]"></div>
							</td>
							<td >
								<div class="zui-table-cell cell-l" v-text="qxlb_tran[item.qsjgandlb]"></div>
							</td>
							<td >
								<div class="zui-table-cell cell-s" v-text="item.fysqnd"></div>
							</td>
							<td>
								<div class="zui-table-cell cell-s" v-text="item.ylfze"></div>
							</td>
							<td>
								<div class="zui-table-cell cell-s" v-text="item.grzhzc"></div>
							</td>
							<td>
								<div class="zui-table-cell cell-s"  v-text="item.jjzfze"></div>
							</td>
							<td>
								<div class="zui-table-cell cell-s"  v-text="item.grxjzc"></div>
							</td>
	                        </tr>
	                        </tbody>
	                    </table>
						<p v-if="mxfyList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
	                </div>
	            </div>
	        </div>
			</div>
	    </transition>


	</div>
</body>
<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script src="index.js" type="text/javascript"></script>
</html>
