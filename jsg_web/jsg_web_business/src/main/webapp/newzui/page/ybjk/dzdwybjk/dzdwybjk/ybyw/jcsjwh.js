var socket;
var wsImpl = window.WebSocket || window.MozWebSocket;
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    components: {
        'search-table': searchTable,
    },
    data: {
        isshow:'1',
        jkyjsbz:-1,
        serachkey: '',
        wsczje: 0,
        jbbmList: [],
        bxbmList: [],
        jbbm: '',
        jbmc: '',
        dwybUrl: '',
        ydbzList:{
            "0":"本地",
            "1":"异地"
        },
        ybjsList: [],
        jshid: '',
        dwlxzInit: 0,
        updateDataStat:0,
        isConnected: false,
        connectionSucceedMessage: '医保连接正常...',
        connectionErrorMessage: '医保连接异常，请重新进入本页面...',
        requestParameters: {},
        num: 0,
        totlePage: 0,
        pageNo: 1,
        pageSize: 50,
        childNum: 0,
        ghKsList: [],
        brFbList: [],
        bxLbList: [],
        zyksList: [],
        zyysList: [],
        mzksList: [],
        zyBridScList: [],
        searchCon: [],
        brxxContent: {sbjgbh:'62290002',ydbz:'0'},
        brfyList_dsc: [],
        json: {},
        selSearch: -1,
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        zybrthem: {
            '姓名': 'brxm',
            '性别': 'brxb',
            '出生日期': 'csrq',
            '身份证号码': 'sfzjhm',
            '手机号码': 'sjhm',
            '住院医生': 'zyysxm',
            '住院科室': 'ryksmc'
        },
        them_tran: {
            'brxb': dic_transform.data.brxb_tran,
            'ismzry': dic_transform.data.istrue_tran
        },
        userInfo: {},
        //费用清单
        popContent:{},
        fyqdContent: {},
    },
    updated: function () {
        changeWin()
    },
    created: function () {

        this.readyData({"zyks": "1"}, "ksbm", "zyksList");
        this.readyData({"ghks": "1"}, "ksbm", "mzksList");
        this.readyData(false, "brfb", "brFbList");
        this.readyData(false, "bxlb", "bxLbList");
        this.getUserInfo();
    },
    mounted: function () {
        this.getbxlb();
        this.openConnection();
        this.readyData({"ysbz": "1", "ksbm": ksbm}, "rybm", "zyysList");
    },
    methods: {
        initJbbm: function () {
            //var str = "http://127.0.0.1:9001/interface/zy/";
            //str = str.match(/(\w+):\/\/([\w.|\:]+)/);
            //console.log(str);
            //console.log(str[0]);
            //this.dwybUrl = str[0];
            $.getJSON(wrapper.dwybUrl + "/interface/zy/jbbm/page?pageNo=1&pageSize=50", function (json) {
                if (json.code == 1) {

                    wrapper.jbbmList = json.data;
                    wrapper.pageSize = 50;
                    wrapper.totlePage = json.totalCount;
                }
            });
        },
        //公用查询
        readyData: function (req, types, listName) {
            if (!req) {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types, function (json) {
                    if (json.a == 0)
                        wrapper[listName] = json.d.list;
                    else
                        malert(types + "查询失败", 'top', 'defeadted');
                });
            } else {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types + "&json=" + JSON.stringify(req), function (json) {
                    if (json.a == 0)
                        wrapper[listName] = json.d.list;
                    else
                        malert(types + "查询失败", 'top', 'defeadted');
                });
            }
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                });
        },
        openConnection: function () {
            console.log("初始化..............");
            var host = "ws://localhost:14444/" + jgbm;
            if (socket == null || socket == undefined) {
                socket = new wsImpl(host);
                console.log("socket连接成功！");
            }
            try {
                socket.onerror = function () {
                    socket.close();
                    wrapper.isConnected = false;
                    console.log("发生错误！");
                };
                socket.onopen = function () {
                    wrapper.isConnected = true;
                    console.log("连接开启！");
                };
                socket.onclose = function () {
                    socket.close();
                    wrapper.isConnected = false;
                    console.log("连接关闭！");
                };
                socket.onmessage = function (evt) {
                    var outParm = eval('(' + evt.data + ')');
                    console.log(outParm);
                    handleMessage(outParm);
                    console.log("消息传送正常！");


                };
            } catch (ex) {
                console.log("异常！");
                socket.close();
            }


        },
        sfrz: function () {//身份认证
            common.openloading(".hzList ");
            if (wrapper.dwlxzInit == 0) {
                var prm = {
                    init: {
                        yybm: '225001',
                        gzrybh: '0001',
                        pwd: '1234',
                    },
                    yljgbm: jgbm
                };

                socket.send(JSON.stringify(prm));
                wrapper.dwlxzInit = 1;
            }


            if (!wrapper.brxxContent.brxm || !wrapper.brxxContent.zyh) {
                malert("请先检索并选择要操作的病人!", "bottom", "defeadted");
                return;
            }
            //如果填写就默认为无卡人员
            wrapper.requestParameters = {};//先清空请求对象
            if (wrapper.brxxContent.grbh && wrapper.brxxContent.sbjgbh) {
                var prm = {
                    grbh: wrapper.brxxContent.grbh,
                    xm: wrapper.brxxContent.brxm,
                    yltclb: wrapper.brxxContent.yltclb,
                    sbjgbh: wrapper.brxxContent.sbjgbh,
                    xzbz: wrapper.brxxContent.xzbz,
                };
                wrapper.requestParameters.query_person_info = prm;
            } else {//否则读卡
                var prm = {
                    yltclb: wrapper.brxxContent.yltclb,
                    jymmbz: '0',
                    // readertype: '',


                };
                wrapper.requestParameters.read_card = prm;
            }

            socket.send(JSON.stringify(wrapper.requestParameters));
        },

        //入院登记
        rydj: function () {
            if (!wrapper.brxxContent.zyh) {
                malert("请先检索并选择要操作的病人!", "bottom", "defeadted");
                return;
            }
            if (!wrapper.brxxContent.grbh) {
                malert("请先进行身份认证!", "bottom", "defeadted");
                return;
            }
            if (!ry_DataValid()) {
                return;
            }
            //先清空请求参数对象
            wrapper.requestParameters = {};
            //入院诊断  疾病编码
           wrapper.brxxContent.ryzd = "";
            //再赋值
            wrapper.requestParameters.save_zydj = wrapper.brxxContent;
            socket.send(JSON.stringify(wrapper.requestParameters));
        },

        //取消入院登记
        qxrydj: function () {

            if (!wrapper.brxxContent.zyh) {
                malert("请先检索并选择要操作的病人!", "bottom", "defeadted");
                return;
            }
            if (wrapper.ybjsList.jsbz==1) {
                malert("已经结算,请先取消结算!", "bottom", "defeadted");
                return;
            }
            // if (!wrapper.brxxContent.grbh) {
            //     malert("该病人未办理入院登记,请先入院登记!", "bottom", "defeadted");
            //     return;
            // }
            this.uploadDataCansel();

            //先清空请求参数对象
            wrapper.requestParameters = {};
            //再赋值
            wrapper.requestParameters.destroy_zydj = wrapper.brxxContent.zyh;
            common.openloading(".zhList");
            socket.send(JSON.stringify(wrapper.requestParameters));
        },
        //费用上传
        scfy: function () {
            if (wrapper.updateDataStat!=0){
                malert("上传中　或　已经上传　费用!请等待　或　取消上传", "bottom", "defeadted");
                return;
            }


            if (!wrapper.zyBridScList || wrapper.zyBridScList.length <= 0) {
                malert("无可上传的费用!", "bottom", "defeadted");
                return;
            }
            if (!wrapper.isChecked || wrapper.isChecked.length <= 0) {
                malert("请选择要上传的费用!", "bottom", "defeadted");
                return;
            }

            if (wrapper.brxxContent.sbjgbh == undefined || wrapper.brxxContent.sbjgbh == null || wrapper.brxxContent.sbjgbh == "") {
                malert( "【社保机构编号】不能为空！", "bottom", "defeadted");
                return;
            }




            var brfyList_dsc = [];
            var hisparam_list =[];

            for (var i = 0; i < wrapper.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    if (wrapper.zyBridScList[i]) {
                        if (wrapper.zyBridScList[i].mxfyxmbm== undefined || wrapper.zyBridScList[i].mxfyxmbm == null || wrapper.zyBridScList[i].mxfyxmbm== ""){
                            malert("mxfyxmbm 项目编码为空,请检查后重试!第"+(i+1)+"条", "bottom", "defeadted")
                            return;
                        }

                        // if (wrapper.zyBridScList[i].bxxmbm== undefined || wrapper.zyBridScList[i].bxxmbm == null || wrapper.zyBridScList[i].bxxmbm== ""){
                        //     malert("bxxmbm 保险编码为空,请与地纬对码后重试!第"+(i+1)+"条", "bottom", "defeadted")
                        //     return;
                        // }


                        brfyList_dsc.push({
                            yyxmbm: wrapper.zyBridScList[i].mxfyxmbm,
                            yyxmmc: wrapper.zyBridScList[i].mxfyxmmc,
                            dj: wrapper.zyBridScList[i].fydj,
                            sl: wrapper.zyBridScList[i].fysl,
                            bzsl: 1,
                            zje: wrapper.zyBridScList[i].fyje,
                            gg: wrapper.zyBridScList[i].fygg,
                            fyfssj: wrapper.zyBridScList[i].sfrq,
                            zxksbm: wrapper.zyBridScList[i].zxks,
                            kdksbm: wrapper.zyBridScList[i].ysks,
                            sbjgbh:wrapper.brxxContent.sbjgbh,
                            ysbm:wrapper.zyBridScList[i].zyysbm,
                            date: wrapper.zyBridScList[i].sfrq,
                            blh:wrapper.brxxContent.zyh,
                            sbjgbh:wrapper.brxxContent.sbjgbh,
                            yzlsh:wrapper.zyBridScList[i].fyid
                        });


                        hisparam_list.push(  {
                            mxfyxmbm: wrapper.zyBridScList[i].mxfyxmbm,
                            mxfyxmmc: wrapper.zyBridScList[i].mxfyxmmc,
                            // yzlx =  wrapper.brfyList[i].yzlx;
                            cfh: wrapper.zyBridScList[i].cfh,
                            scid: wrapper.zyBridScList[i].fyid,
                            dj: wrapper.zyBridScList[i].fydj,
                            sl: wrapper.zyBridScList[i].fysl,
                            je: wrapper.zyBridScList[i].fyje,
                            bxxmbm:wrapper.zyBridScList[i].mxfyxmbm,
                            bxxmmc:wrapper.zyBridScList[i].mxfyxmmc,
                            ly:wrapper.zyBridScList[i].ly,
                            fyid:wrapper.zyBridScList[i].fyid

                        });
                    }
                }
            }

            var param={}
            common.openloading(".hzList");
            console.log("list--->",brfyList_dsc)
            if (brfyList_dsc.length <= 0) {
                malert("无可上传的费用!", "bottom", "defeadted");
                return;
            }else {
                param= {
                    put_fymx_zy: brfyList_dsc
                }
            }

            wrapper.updateDataStat=2;
            wrapper.$http.post( wrapper.dwybUrl + "/interface/zy/bxscmx/insertAll?zyh=" + wrapper.brxxContent.zyh + "&yljgbm=" + jgbm +"&bxlbbm="+ wrapper.brxxContent.bxlbbm,
                JSON.stringify(hisparam_list)).then(function (data) {
                if (data.body.code == '1') {
                    //wrapper.brxxContent = {};
                    // malert("费用上传成功!");
                    socket.send(JSON.stringify(param));
                } else {
                    malert(data.msg, "bottom", "defeadted");
                    return;
                }


            }, function (error) {
                malert(data.body.msg);
                common.closeLoading();
                return;
            });


            // var postParm = '{"list":' + JSON.stringify(brfyList_dsc) + '}';
            //
            // wrapper.$http.post("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + wrapper.bxurl + "&bxlbbm=" + wrapper.bxlbbm + "&types=zyyw&method=fysc",
            //     postParm).then(function (hisscjson) {
            //     if (hisscjson.body.a == '0') {
            //         malert("费用上传成功！");
            //         wscjl.jsonList = [];//置空
            //         wscjl.getData();
            //         wscjl.ifClick = true;
            //         common.closeLoading();
            //     } else {
            //         malert(hisscjson.body.c);
            //         common.closeLoading();
            //         return;
            //     }
            // }, function (error) {
            //     malert(hisscjson.body.c);
            //     common.closeLoading();
            //     return;
            // });

        },
        //预结算
        settlePre: function () {
            // if(wrapper.brxxContent.cyrq==null ||wrapper.brxxContent.cyrq==undefined|| wrapper.brxxContent.cyrq==""){
            //     malert("出院日期!未填写" , "bottom", "defeadted");
            //
            //     return;
            // }

            if(wrapper.jbbm==null ||wrapper.jbbm==undefined|| wrapper.jbbm==""){
                malert("疾病编码!未填写" , "bottom", "defeadted");
                return;
            }

            // if(wrapper.brxxContent.cyfs==null ||wrapper.brxxContent.cyfs==undefined|| wrapper.brxxContent.cyfs==""){
            //     malert("出院方式!未填写"  , "bottom", "defeadted");
            //     return;
            // }
            //
            //
            // if(!wrapper.brxxContent.zlfa){
            //     malert("治疗方式!未填写"  , "bottom", "defeadted");
            //     return;
            // }


            common.openloading(".hzList");
            $.getJSON(wrapper.dwybUrl + "/interface/zy/rydj/find?zyh=" + wrapper.brxxContent.zyh + "&yljgbm=" + jgbm, function (json) {
                if (json.code == 1) {

                    wrapper.brxxContent.bqcybz = json.data.bqcybz
                    //bqcybz==1 代表出院
                    if (wrapper.brxxContent.bqcybz == "1") {
                        //jkyjsbz=1:预结算；jkyjsbz=0:正常结算。
                        // var parma = {
                        //     "settle_zy": {jkyjsbz: 1}
                        // }

                        var param= {
                           settle_zy: {
                               jkyjsbz: 1,
                               blh:wrapper.brxxContent.zyh,
                               settle_zy_init: {
                                   cyrq: wrapper.brxxContent.cyrq,
                                   cyzd: wrapper.jbmc,
                                   cyfs: wrapper.brxxContent.cyfs,
                                   zlfs: wrapper.brxxContent.zlfa,
                                   dkbz:0
                               }
                           }
                        }


                        socket.send(JSON.stringify(param));

                        wrapper.jkyjsbz=1;
                    } else {
                        malert("出院状态!未出院" + json.msg, "bottom", "defeated")
                    }

                    // if (json.d.list.length > 0) {
                    //     wrapper.bxlbbm = json.d.list[0].bxlbbm;
                    //     wrapper.bxurl = json.d.list[0].url;
                    // }
                    // } else {
                    //     malert("保险类别查询失败!" + json.c,"bottom","defeated")
                } else {
                    malert("出院状态查询失败!" + json.msg, "bottom", "defeated")
                }
            });


        },
        //正式结算
        settleJs: function () {
            // if(wrapper.brxxContent.cyrq==null ||wrapper.brxxContent.cyrq==undefined|| wrapper.brxxContent.cyrq==""){
            //     malert("出院日期!未填写" , "bottom", "defeadted");
            //
            //     return;
            // }

            if(wrapper.jbbm==null ||wrapper.jbbm==undefined|| wrapper.jbbm==""){
                malert("疾病编码!未填写" , "bottom", "defeadted");
                return;
            }


            // if(wrapper.brxxContent.cyfs==null ||wrapper.brxxContent.cyfs==undefined|| wrapper.brxxContent.cyfs==""){
            //     malert("出院方式!未填写"  , "bottom", "defeadted");
            //     return;
            // }
            //
            //
            // if(wrapper.brxxContent.zlfa==null ||wrapper.brxxContent.zlfa==undefined|| wrapper.brxxContent.zlfa==""){
            //     malert("治疗方式!未填写"  , "bottom", "defeadted");
            //     return;
            // }


            $.getJSON(wrapper.dwybUrl + "/interface/zy/rydj/find?zyh=" + wrapper.brxxContent.zyh + "&yljgbm=" + jgbm, function (json) {
                if (json.code == 1) {

                    wrapper.brxxContent.bqcybz = json.data.bqcybz
                    //bqcybz==1 代表出院
                    if (wrapper.brxxContent.bqcybz == "1") {
                        //jkyjsbz=1:预结算；jkyjsbz=0:正常结算。

                        var param= {
                            settle_zy: {
                                jkyjsbz: 0,
                                blh:wrapper.brxxContent.zyh,
                                settle_zy_init: {
                                    cyrq: wrapper.brxxContent.cyrq,
                                    cyzd: wrapper.jbmc,
                                    cyfs: wrapper.brxxContent.cyfs,
                                    zlfs: wrapper.brxxContent.zlfa,
                                    dkbz:0
                                }
                            }
                        }


                        socket.send(JSON.stringify(param));

                        wrapper.jkyjsbz=0;
                    } else {
                        malert("出院状态!未出院", "bottom", "defeated")
                    }

                    // if (json.d.list.length > 0) {
                    //     wrapper.bxlbbm = json.d.list[0].bxlbbm;
                    //     wrapper.bxurl = json.d.list[0].url;
                    // }
                    // } else {
                    //     malert("保险类别查询失败!" + json.c,"bottom","defeated")
                } else {
                    malert("出院状态查询失败!" + json.msg, "bottom", "defeated")
                }
            });


        },
        //取消结算
        settleCansel: function () {
            common.openloading(".hzList");
            $.getJSON(wrapper.dwybUrl + "/interface/zy/bxzfjl/find?zyh=" + wrapper.brxxContent.zyh + "&yljgbm=" + jgbm, function (json) {
                if (json.code == 1) {
                    //data>0 代表已经出院
                    if (json.data > 0) {
                        var parma = {
                            "destroy_zyjs": {
                                blh: wrapper.brxxContent.zyh,
                                jshid: wrapper.brxxContent.jshid,
                                destroy_cy: 1    //取消出院

                            }

                        }

                    } else {
                        var parma = {
                            "destroy_zyjs": {
                                blh: wrapper.brxxContent.zyh,
                                jshid: wrapper.brxxContent.jshid,
                                destroy_cy: 0
                            }

                        }
                    }
                    socket.send(JSON.stringify(parma));
                    // if (json.d.list.length > 0) {
                    //     wrapper.bxlbbm = json.d.list[0].bxlbbm;
                    //     wrapper.bxurl = json.d.list[0].url;
                    // }
                    // } else {
                    //     malert("保险类别查询失败!" + json.c,"bottom","defeated")
                } else {
                    malert("取消结算失败!" + json.msg, "bottom", "defeated")
                }
            });


            //退回最后次 结算，再调用会依次取消  如果已经正式结算 先要取消出院


        },
        tabBg: function (index) {
            wrapper.isshow = null;
            console.log("--->",wrapper.brxxContent)
            this.num = index;
            if (index == 1){
                if (wrapper.childNum == 0) {
                    wrapper.getSc(0);
                } else {
                    wrapper.getSc(1);
                }
            }else if(index == 2){
                wrapper.isshow='2';
            }else if(index == 3){//费用清单
                $("#loadingPage001").load("fyqd.html", '', function () {
                }).fadeIn(300)
            }
        },

        printFyqd: function () {
            window.print();
        },
        childTabBg: function (index) {
            wrapper.childNum = index
            if (wrapper.childNum == 0) {

                wrapper.getSc(0);
            } else {
                wrapper.getSc(1);
            }
        },
        printDj: function () {
            //打印补偿单
            var param={
                jshid: wrapper.brxxContent.jshid,
                djlx:'JSD'   //要打印的单据类型（‘FP’：发票必选，‘JSD’：结算单可选，‘GRZH’：打印个人账户可选）
            }
            //先清空请求参数对象
            wrapper.requestParameters = {};
            wrapper.requestParameters.print_dj=param
            socket.send(JSON.stringify(wrapper.requestParameters));


        },
        uploadDataCansel: function () {
            //取消上传  目前采用全删除模式
            if ( wrapper.brxxContent.zyh!= null &&  wrapper.brxxContent.zyh.length > 0) {
                var canselup = {
                    "delete_all_fypd": {
                        "blh":wrapper.brxxContent.zyh
                    }
                }

                socket.send(JSON.stringify(canselup));
            }
        },
        getData: function () {
            $.getJSON(wrapper.dwybUrl+'/interface/zy/jbbm/page?pageNo=' + wrapper.page +
                        '&pageSize=50',
                        function (json) {
                            if (json.code == 1) {

                                wrapper.jbbmList = json.data;
                                wrapper.totlePage = json.totalCount;
                                // wapse.selSearch = 0;
                                // load.isShow = false;
                            }
                        });

        },

        getSc: function (id) {
            $.getJSON(wrapper.dwybUrl + "/interface/zy/bridfy/fy?zyh=" + wrapper.brxxContent.zyh + "&yljgbm=" + jgbm + "&ifsc=" + id+"&bxlbbm="+wrapper.brxxContent.bxlbbm, function (json) {
                if (json.code == 1) {
                    wrapper.zyBridScList = json.data;
                    wrapper.wsczje = json.printName
                    // if (json.d.list.length > 0) {
                    //     wrapper.bxlbbm = json.d.list[0].bxlbbm;
                    //     wrapper.bxurl = json.d.list[0].url;
                    // }
                    // } else {
                    //     malert("保险类别查询失败!" + json.c,"bottom","defeated")
                    common.closeLoading();
                    wrapper.$forceUpdate()
                } else {
                    common.closeLoading();
                    malert(json.msg,"bottom","defeated")
                }
            });
        },
        getFind: function () {
            if (wrapper.serachkey != null && wrapper.serachkey.length > 0) {
                $.getJSON(wrapper.dwybUrl + "/interface/zy/jbbm/keyPage?key=" + wrapper.serachkey, function (json) {
                    if (json.code == 1) {

                        wrapper.jbbmList = json.data;
                        wrapper.totlePage = json.totalCount;
                        // if (json.d.list.length > 0) {
                        //     wrapper.bxlbbm = json.d.list[0].bxlbbm;
                        //     wrapper.bxurl = json.d.list[0].url;
                        // }
                        // } else {
                        //     malert("保险类别查询失败!" + json.c,"bottom","defeated")
                    }
                });
            }else {
                $.getJSON(wrapper.dwybUrl+'/interface/zy/jbbm/page?pageNo=' + wrapper.page +
                    '&pageSize=50',function (json) {
                    if (json.code == 1) {

                        wrapper.jbbmList = json.data;
                        wrapper.totlePage = json.totalCount;
                        // if (json.d.list.length > 0) {
                        //     wrapper.bxlbbm = json.d.list[0].bxlbbm;
                        //     wrapper.bxurl = json.d.list[0].url;
                        // }
                        // } else {
                        //     malert("保险类别查询失败!" + json.c,"bottom","defeated")
                    }
                });

            }
        },

        getSelect: function (item) {

            wrapper.jbbm = item.jbbm;
            wrapper.jbmc = item.jbmc;
        },


        // goPage: function (val) {
        //     var isRequest = this.pageType(val);
        //
        //
        //    if (isRequest) {
        //
        //        warpper.pageNo=val
        //         // load.showLoading("加载中！");
        //         $.getJSON(wrapper.dwybUrl+'/interface/zy/jbbm/page?pageNo=' + wrapper.pageNo +
        //             '&pageSize=50',
        //             function (json) {
        //                 if (json.code == 1) {
        //
        //                     wrapper.jbbmList = json.data;
        //                     wrapper.totlePage = json.totalCount;
        //                     // wapse.selSearch = 0;
        //                     // load.isShow = false;
        //                 }
        //             });
        //     }
        // },

        // 请求保险类别
        getbxlb: function () {
            var param = {bxjk: "011"};
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        wrapper.bxlbbm = json.d.list[0].bxlbbm;
                        wrapper.bxurl = json.d.list[0].url;
                        var strUrl = wrapper.bxurl.match(/(\w+):\/\/([\w.|\:]+)/);
                        wrapper.dwybUrl = strUrl[0];
                        wrapper.initJbbm();//初始化数据
                        wrapper.getBxbm(); //初始化保险编码
                    }
                } else {
                    malert("保险类别查询失败!" + json.c, "bottom", "defeated")
                }
            });
        },
        getBxbm: function () {
            $.getJSON(wrapper.dwybUrl + "/interface/zy/brid/bxbm", function (json) {
                if (json.code == 1) {
                    wrapper.bxbmList = json.data;
                    wrapper.brxxContent.sbjgbh = '51172205';//默认宣汉居民
                } else {
                    malert(json.msg,"bottom","defeated")
                }
            });
        },
        dimdown:function(){
            var parma = {
                "destroy_zyjs": {
                    jshid: wrapper.jshid,
                }

            }
            socket.send(JSON.stringify(parma));
        },


        getZybrList: function () {
        },
        //病人基本信息下拉检索
        changeDown: function (event, type, content, searchCon) {
            var searchVal = event.target.value;
            this.nextFocus(event, '', true);
            // if (this[searchCon][this.selSearch] == undefined) return;
            // this.keyCodeFunction(event, content, searchCon);
            //10位数年4+00000+录入数
            //10位数年4+00000+录入数
            var newSearch = searchVal
            if (searchVal.length < 6) {
                var oDate = new Date();
                //var s= 6-this.search.length;
                newSearch = oDate.getFullYear() + (Array(7 - searchVal.length).join(0) + searchVal)
                wrapper.brxxContent.zyh = newSearch
            }
            var dim = []
            $.getJSON(wrapper.dwybUrl + "/interface/zy/brid/find?yljgbm=" + jgbm + "&zyh=" + wrapper.brxxContent.zyh, function (json) {
                console.log(">>>>>" + json.code);
                if (json.code == "1") {
                    //dim = JSON.parse(json.data).list;
                    wrapper.brxxContent = json.data;
                    // wrapper.brxxContent.sbjgbh='62290002';
                    wrapper.brxxContent.ydbz='0'
                    wrapper.jbbm=  json.data.ryzdbm
                    wrapper.jbmc=json.data.ryzdbm
                } else {
                    malert("数据查询异常！","bottom","defeadted")
                }

                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (type == 'zyh') {
                        /****************转换成医保字段名称****************/
                        wrapper.brxxContent.blh = wrapper.brxxContent.zyh;
                        wrapper.brxxContent.zyrq = wrapper.brxxContent.ryrq;
                        wrapper.brxxContent.qzys = wrapper.brxxContent.zyys;
                        wrapper.brxxContent.cw = wrapper.brxxContent.rycwbh;
                        wrapper.brxxContent.bxlbmc = '地纬医保';
                        wrapper.brxxContent.bxlbbm = wrapper.bxlbbm;
                        wrapper.brxxContent.ksbm = wrapper.brxxContent.ryks;
                        wrapper.brxxContent.zlfa = wrapper.brxxContent.zlfa?wrapper.brxxContent.zlfa:'A';
                        wrapper.brxxContent.zlfs = wrapper.brxxContent.zlfs?wrapper.brxxContent.zlfs:wrapper.brxxContent.zlfa;
                        wrapper.brxxContent.cyfs = wrapper.brxxContent.cyfs?wrapper.brxxContent.cyfs:"1";
                        if (!wrapper.brxxContent.grbh) {//如果未登记,那么给默认值
                            wrapper.brxxContent.bxlbbm = wrapper.bxlbbm;
                            wrapper.brxxContent.yltclb = '1';//医保统筹类别
                            wrapper.brxxContent.yltclbmx = '101';//医保统筹类别明细 默认 普通住院
                            wrapper.brxxContent.zyfs = '1';//住院方式,默认 新发生
                            wrapper.brxxContent.xzbz = 'C';//险种标志,默认 医疗
                            wrapper.brxxContent.jylb = '01';//就医类别,默认 本地就医
                            wrapper.brxxContent.sbjgbh = '51172205';//默认宣汉居民
                            wrapper.brxxContent.grbh = json.data.sfzjhm;//默认宣汉居民
                        }
                        /****************转换成医保字段名称****************/

                        wrapper.selSearch = 0;
                        //需单独赋值的
                        $(".zui-select-group").hide();
                        // this.nextFocus(event);
                    }
                }
                wrapper.ybjsList=[]
                $.getJSON(wrapper.dwybUrl + "/interface/zy/dwlxzinzy/selectJs?yljgbm=" + jgbm + "&blh=" + newSearch, function (json) {
                    console.log(">>>>>" + json.code);
                    if (json.code == "1" ) {
                        if (json.data) {
                            //dim = JSON.parse(json.data).list;
                            wrapper.ybjsList = json.data;
                            wrapper.brxxContent.jshid= json.data.jshid;
                        }
                    } else {
                        // malert("数据查询异常！")
                    }
            });
            });




            //初始化地维
            if (wrapper.dwlxzInit == 0) {
                var prm = {
                    init: {
                        yybm: '225001',
                        gzrybh: '0001',
                        pwd: '1234',
                    },
                    yljgbm: jgbm
                };

                socket.send(JSON.stringify(prm));
                wrapper.dwlxzInit = 1;
            }


        },
        //当输入值后才触发
        change: function (add, type, val) {


            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            //病人基本信息检索
            if (type == 'zyh') {
                this.brxxContent[type] = val;
                if (this.brxxContent[type] == undefined || this.brxxContent[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.brxxContent[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + wrapper.bxurl + "&bxlbbm=" + wrapper.bxlbbm + "&types=zyyw&method=getHisRydjList&parm="
                    + JSON.stringify(str_param),
                    function (data) {
                        var res = JSON.parse(data.d);
                        if (res.list.length > 0) {
                            if (add) {//不是第一页则需要追加
                                for (var i = 0; i < res.list.length; i++) {
                                    wrapper.searchCon.push(res.list[i]);
                                }
                            } else {//第一页则直接赋值
                                wrapper.searchCon = res.list;
                            }
                        }
                        wrapper.page.total = res.total;
                        wrapper.selSearch = 0;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
        },
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'zyh', this.brxxContent['zyh']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                //  wrapper.readyData({"ysbz": "1","ksbm":item.ksbm}, "rybm", "zyysList");
                wrapper.brxxContent = item;
                /****************转换成医保字段名称****************/
                wrapper.brxxContent.blh = item.zyh;
                wrapper.brxxContent.zyrq = item.ryrq;
                wrapper.brxxContent.qzys = item.rybm;
                if (!wrapper.brxxContent.grbh) {//如果未登记,那么给默认值
                    wrapper.brxxContent.bxlbbm = wrapper.bxlbbm;
                    wrapper.brxxContent.yltclb = '1';//医保统筹类别
                    wrapper.brxxContent.yltclbmx = '101';//医保统筹类别明细 默认 普通住院
                    wrapper.brxxContent.zyfs = '1';//住院方式,默认 新发生
                    wrapper.brxxContent.xzbz = 'C';//险种标志,默认 医疗
                    wrapper.brxxContent.jylb = '01';//就医类别,默认 本地就医
                }
                /****************转换成医保字段名称****************/

                $(".selectGroup").hide();
                $(".zui-select-group").hide();

            }
        },
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "yltclb":
                    Vue.set(this.brxxContent, 'yltclb', val[0]);
                    break;
                case "yltclbmx":
                    Vue.set(this.brxxContent, 'yltclbmx', val[0]);
                    break;
                case "ksbm":
                    Vue.set(this.brxxContent, 'ksbm', val[0]);
                    this.readyData({"ysbz": "1", "ksbm": val[0]}, "rybm", "zyysList");
                    break;
                case "zyfs":
                    Vue.set(this.brxxContent, 'zyfs', val[0]);
                    break;
                case "xzbz":
                    Vue.set(this.brxxContent, 'xzbz', val[0]);
                    break;
                case "jylb":
                    Vue.set(this.brxxContent, 'jylb', val[0]);
                    break;
                case "zyys":
                    Vue.set(this.brxxContent, 'zyys', val[0]);
                    break;
                case "cyfs":
                    Vue.set(this.brxxContent, 'cyfs', val[0]);
                    break;
                case "zlfa":
                    Vue.set(this.brxxContent, 'zlfa', val[0]);
                    break;
                case "bxlbbm":
                    Vue.set(this.brxxContent, 'bxlbbm', val[0]);
                    break;
                case "fbbm":
                    Vue.set(this.brxxContent, 'fbbm', val[0]);
                    break;
                case "mzks":
                    Vue.set(this.brxxContent, 'mzks', val[0]);
                    break;
            }
        },
    },
});

function ry_DataValid() {
    var i = 0;
    var errString = "";
    if (wrapper.brxxContent.blh == null || wrapper.brxxContent.blh == undefined || wrapper.brxxContent.blh == "") {
        i++;
        errString += "</br>【住院号】不能为空！";
    }
    if (wrapper.brxxContent.xm == null || wrapper.brxxContent.xm == undefined || wrapper.brxxContent.xm == "") {
        i++;
        errString += "</br>请先身份认证！";
    }
    if(wrapper.brxxContent.brxm!=wrapper.brxxContent.xm){
        i++;
        errString += "</br>患者入院姓名与医保局登记姓名不一致！";
    }
    if (wrapper.brxxContent.xb == null || wrapper.brxxContent.xb == undefined || wrapper.brxxContent.xb == "") {
        i++;
        errString += "</br>请先身份认证！";
    }
    if (wrapper.brxxContent.mzks == null || wrapper.brxxContent.mzks == undefined || wrapper.brxxContent.mzks == "") {
        i++;
        errString += "</br>【门诊科室】不能为空！";
    }

    if (wrapper.brxxContent.ryksmc == null || wrapper.brxxContent.ryksmc == undefined || wrapper.brxxContent.ryksmc == "") {
        i++;
        errString += "</br>【入院科室】不能为空！";
    }
    if (wrapper.brxxContent.zyysxm == null || wrapper.brxxContent.zyysxm == undefined || wrapper.brxxContent.zyysxm == "") {
        i++;
        errString += "</br>【住院医师】不能为空！";
    }
    if (wrapper.brxxContent.zyrq == null || wrapper.brxxContent.zyrq == undefined || wrapper.brxxContent.zyrq == "") {
        i++;
        errString += "</br>【入院日期】不能为空！";
    }
    if (wrapper.brxxContent.yltclb == null || wrapper.brxxContent.yltclb == undefined || wrapper.brxxContent.yltclb == "") {
        i++;
        errString += "</br>【医疗统筹类别】不能为空！";
    }
    if (wrapper.brxxContent.yltclbmx == null || wrapper.brxxContent.yltclbmx == undefined || wrapper.brxxContent.yltclbmx == "") {
        i++;
        errString += "</br>【医疗统筹类别明细】不能为空！";
    }
    if (wrapper.brxxContent.zyfs == null || wrapper.brxxContent.zyfs == undefined || wrapper.brxxContent.zyfs == "") {
        i++;
        errString += "</br>【住院方式】不能为空！";
    }
    if (wrapper.brxxContent.xzbz == null || wrapper.brxxContent.xzbz == undefined || wrapper.brxxContent.xzbz == "") {
        i++;
        errString += "</br>【险种标志】不能为空！";
    }
    if (wrapper.brxxContent.grbh == null || wrapper.brxxContent.grbh == undefined || wrapper.brxxContent.grbh == "") {
        i++;
        errString += "</br>【个人编号】不能为空！";
    }
    if (wrapper.brxxContent.sbjgbh == null || wrapper.brxxContent.sbjgbh == undefined || wrapper.brxxContent.sbjgbh == "") {
        i++;
        errString += "</br>【社保机构编号】不能为空！";
    }

    // if (wrapper.brxxContent.sbjgbh == null || wrapper.brxxContent.sbjgbh == undefined || wrapper.brxxContent.sbjgbh == "") {
    //     i++;
    //     errString += "</br>【社保机构编号】不能为空！";
    // }

    if (i > 0) {
        malert(errString, 'top', 'defeadted');
        return false;
    } else {
        return true;
    }
}

laydate.render({
    elem: '#csrq',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        wrapper.brxxContent.csrq = value;
    }
});
laydate.render({
    elem: '#ryrq',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        //应该在这个储存这个值
        wrapper.brxxContent.rqrq = value;
    }
});
laydate.render({
    elem: '#csrq1',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        wrapper.brxxContent.csrq = value;
    }
});
laydate.render({
    elem: '#ryrq1',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        wrapper.brxxContent.rqrq = value;
    }
});
laydate.render({
    elem: '#cyrq',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        wrapper.brxxContent.cyrq = value;
    }
});
laydate.render({
    elem: '#bqcysj',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        wrapper.brxxContent.bqcysj = value;
    }
});

function handleMessage(message) {

    console.log(message);
    var method = message.method;
    var returnCode = message.returnCode;
    var outResult = message.outResult;
    switch (method) {
        case "init"://登陆医保接口
            if (returnCode == '0') {
                wrapper.isConnected = true;
            }
            break;
        case "read_card"://身份认证(有卡)
            if (returnCode == '0') {
                common.closeLoading();
                read_card_backFun(outResult);
            } else {
                common.closeLoading();
                malert(message.msgInfo, "bottom", "defeadted");
                return;
            }
            break;
        case "query_person_info"://身份认证(无卡)
            if (returnCode == '0') {
                common.closeLoading();
                query_person_info_backFun(outResult);
            } else {
                common.closeLoading();
                malert(message.msgInfo, "bottom", "defeadted");
                return;
            }
            break;
        case "save_zydj"://入院登记
            if (returnCode == '0') {
                save_zydj(outResult);
            } else {
                common.closeLoading();
                malert(message.msgInfo, "bottom", "defeadted");
                return;
            }
            break;
        case "destroy_zydj"://取消入院登记
            if (returnCode == '0') {
                common.closeLoading();
                destroy_zydj();
            } else {
                common.closeLoading();
                malert(message.msgInfo, "bottom", "defeadted");
                return;
            }
            break;
        case "put_fymx_zy"://上传费用
            if (returnCode == '0') {
                malert("费用上传完成！","bottom","success");
                malert(message.msgInfo,"bottom","success");
                //删掉不符合上传条件和上传失败的数据
                wrapper.$http.post(wrapper.dwybUrl + "/interface/zy/bxscmx/deleteBatch?zyh="+wrapper.brxxContent.zyh +"&yljgbm="+jgbm,
                    outResult).then(function (data) {
                    if (data.body.code != '1') {
                        malert(data.msg, "bottom", "defeadted");
                    }
                    wrapper.getSc(0);
                }, function (error) {
                    malert(data.body.msg,"bottom","defeadted");
                    common.closeLoading();
                    return;
                });
                wrapper.updateDataStat=0;
            } else {

                $.getJSON( wrapper.dwybUrl + "/interface/zy/bxscmx/delete?zyh="+wrapper.brxxContent.zyh +"&yljgbm="+jgbm+"&iflsh=1", function (data) {
                    if (data.code == '1') {

                    } else {
                        malert(data.msg, "bottom", "defeadted");
                        return;
                    }
                    wrapper.getSc(0);
                });
                common.closeLoading();
                malert(message.msgInfo, "bottom", "defeadted");
                wrapper.updateDataStat=0;
                return;
            }

            break;
        case "delete_all_fypd": // 取消所有费用
            if (returnCode == '0') {
                delete_all_fypd(message);
                common.closeLoading();
            } else {
                malert(message.msgInfo, "bottom", "defeadted");
                common.closeLoading();
                return;
            }
            break;
        case "destroy_zyjs"://撤销住院结算服务

            if (returnCode == '0') {
                destroy_zyjs(message);
                common.closeLoading();
            } else {
                malert(message.msgInfo, "bottom", "defeadted");
                common.closeLoading();
                return;

            }
            break;
        case "settle_zy"://出院结算服务

            if (returnCode == '0') {
                settle_zy(message);
                common.closeLoading();
            } else {
                malert(message.msgInfo, "bottom", "defeadted");
                common.closeLoading();
                return;
            }
            break;

        case "print_dj":

            if (returnCode == '0') {
               // settle_zy(message);
               malert("打印补偿单成功!");
            } else {
                malert(message.msgInfo, "bottom", "defeadted");
                return;
            }
            break;
    }
}