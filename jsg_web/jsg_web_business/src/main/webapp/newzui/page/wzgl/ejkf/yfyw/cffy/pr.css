
    .sameStart {
        position: absolute;
        border-top: 1px solid #000000;
        border-right: 1px solid #000000;
        border-left: 0;
        border-bottom: 0;
        width: 10px !important;
        height: 50%;
        right: 50%;
        bottom: 0;
    }

    .sameEnd {
        position: absolute;
        border-top: 0;
        border-right: 1px solid #000000;
        border-left: 0;
        border-bottom: 1px solid #000000;
        width: 10px !important;
        height: 50%;
        right: 50%;
        top: 0;
    }

    .same {
        position: absolute;
        border-top: 0;
        border-right: 1px solid #000000;
        border-left: 0;
        border-bottom: 0;
        width: 10px !important;
        height: 100%;
        right: 50%;
        top: 0;
    }
    .yzd-name {
        float: left;
        height: 42px;
    }
    .yzdTitle {
        font-size: 22px;
        text-align: center;
    }
    .yzd-table table {
        border-collapse: collapse;
        margin: 0 auto;
        font-size: 14px;
    }

    .yzd-table td, .yzd-table th {
        border: 1px solid #999;
        font-weight: 500;
        height: 43px;
        width: 30px;
        position: relative;
        text-align: center;
    }

    .yzd-table td span {
        display: block;
        float: left;
        width: calc(50% - 2px);
        text-align: left;
        margin-left: 2px;
    }
    .yzd-table-blank span:first-child{
        border-bottom: 1px solid #999
    }
    .yzd-table-blank span {
        width: 100% !important;
        height: 50%;
        text-align: center !important;
    }
        @page{
            size: A4;
            margin: 0.5cm 0;
        }

    .yzd-brInfo > div, .yzd-ysInfo > div {
        font-size: 14px;
        margin-right: 20px;
    }