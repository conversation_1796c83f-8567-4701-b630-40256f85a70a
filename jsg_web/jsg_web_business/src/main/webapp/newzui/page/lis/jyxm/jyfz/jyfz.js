(function () {
    $(".zui-table-view").uitable();
    var wrapper=new Vue({
        el:'.background',
        data:{
            index:1,
            pop:{},
        },

        methods:{
            addclass:function (num) {
                this.index=num
            },
            show:function () {
                pop.isShow=true;
            },
            add:function () {
                pop.isShow=true
                pop.title='新增检验分组'
            }
        },
    });
    var jyx=new Vue({
        el:'#jyxm',
        data:{

        },
        methods:{
            show:function () {
                pop.isShow=true;
                pop.title='编辑检验分组'
            },

        },
    })
    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            title:'',
            popContent:{},
            centent:''
        },
    });
    document.onkeydown=function (ev) {
        var ev=window.event|| ev
        var key=ev.keyCode
        if(key==83&& ev.ctrlKey){
            return false
        }
    }
})()