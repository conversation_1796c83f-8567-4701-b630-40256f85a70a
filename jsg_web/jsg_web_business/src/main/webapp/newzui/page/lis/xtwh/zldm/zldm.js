    var wrapper=new Vue({
        el:'#jyxm_icon',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isTabelShow:false,
            jsShowtime:false,
            pcShow:false,
            lsShow:false,
            qsShow:false,
            isShowpopL:false,
            isShow:false,
            title:'',
            isFold: false,
            jsonList:'',
            popIndex:'',
            str:0,
            centent:'',
            totlePage:'',
            saveList:[],
            isChecked:[],
            deleteList:[],
    		param: {
    			page: 1,
    			rows: 10,
    			parm:'',
    		},
        },
        methods:{
        	//删除当前
            delNow:function () {
                pop.title='系统提示';
                pop.centent='确定删除该行内容吗？';
                pop.isShowpopL=true;
                pop.isShow=true;
            },
            //dbAdd
            dbAdd:function () {
                alert('双击添加');
            },
            //双击编辑
            dbEdit:function (data,$index) {
            	//获取检验项目
            	wapse.getJyxm();
            	wrapper.popIndex=$index
                wapse.isFold = true;
                wapse.sideTitle='选择项目';
                $("#isFold").addClass('side-form-bg');
                $("#brzcList").removeClass('ng-hide');

            },
            guolu:function () {
                isTabel.isShow=true;
            },
            Print:function () {
                window.print()
            },
            delOK:function () {
            	if(this.isChecked.length == 0){
            		pop.isShowpopL=false;
                    pop.isShow=false;
            		malert('请选择要删除的条目','top','defeadted');
            		return;
            	}
                pop.isShowpopL=true
                pop.isShow=true
                pop.title='系统提示';
                pop.centent='确定删除该科室吗？';
            },
            getData:function(){
                this.isChecked=[];
              	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhZldm&types=queryZldm&param=" + JSON.stringify(this.param), function(json) {
  					console.log(json);
  					wrapper.jsonList = json.d.list;
  					wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
  					console.log(wrapper.totlePage);
  				});
             },
             //更新诊疗代码
             updateZldm:function(){
            	 if(this.saveList.length == 0 ){
            		 malert('请填写','top','success');
            	 }else{ 		
            		 var data = '{"list":' + JSON.stringify(this.saveList) + '}';
	        		 this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhZldm&types=updateZldm',data).then(function(json) {
	        			 console.log(json.body);
	        			 if(json.body.a == 0){
            				 malert('保存成功！','center','success');
            				 wrapper.getData();
            				 wrapper.saveList=[];
            				 wrapper.isChecked=[];
            				 wrapper.deleteList=[];
            			 }
	        		 });
            	 }
             }
        },
        watch:{
       	 'str':function(){
       		if(this.saveList.length != 0 ){
       			for(i = 0 ; i < this.saveList.length ; i++){
           			//待保存的数组
           			if(this.saveList[i].zlxmbm == this.jsonList[this.popIndex].zlxmbm){
           				this.saveList[i].jyxm = this.jsonList[this.popIndex].jyxm;
           				this.saveList[i].jyxmmc = this.jsonList[this.popIndex].jyxmmc;
           				this.saveList[i].jyxmpy = this.jsonList[this.popIndex].jyxmpy;
           				//默认明细诊疗对码
           				this.saveList[i].lx = '0';
           				return;
           			}
           		}
       			this.jsonList[this.popIndex].lx = '0';
       			this.saveList.push(this.jsonList[this.popIndex]);
   			}else{
   				this.jsonList[this.popIndex].lx = '0';
   				this.saveList.push(this.jsonList[this.popIndex]);
   			}
        },
        'param.parm':function(){
        	wrapper.getData();
        }
      }
    });

    var pop=new Vue({
        el:'#pop',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            title:'',
            centent:'',
        },
        methods:{
            //确定删除
            delOk:function () {
            	if(wrapper.isChecked.length != 0){
            		for (var j = 0; j < wrapper.isChecked.length; j++) {
            			if(wrapper.isChecked[j] == true){
            				if(wrapper.jsonList[j].jyxm != null && wrapper.jsonList[j].jyxm!= ''){
            					wrapper.deleteList.push(wrapper.jsonList[j]);
            				}
            			}
					}
            	}
            	if(wrapper.deleteList.length == 0 ){
            		pop.isShowpopL=false;
                    pop.isShow=false;
                    malert('无此数据！','top','success');
                    return;
            	}
            	//删除接口
	             var data = '{"list":' + JSON.stringify(wrapper.deleteList) + '}';
	       		 this.$http.post('/actionDispatcher.do?reqUrl=LisXtwhZldm&types=deleteZldm',data).then(function(json) {
	       			 console.log(json.body);
	       			 if(json.body.a == "0"){
	       				pop.isShowpopL=false;
	                    pop.isShow=false;
	                    malert('删除成功！','top','success');
	                    wrapper.getData();
	                    wrapper.saveList=[];
	                    wrapper.isChecked=[];
	                    wrapper.deleteList=[];
	       			 }
	       		 });
            	
                
            },
            colse:function () {
                pop.isShowpopL=false;
                pop.isShow=false;
                malert('取消删除成功','top','defeadted');
            }

        }
    });
    var wapse=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isShowpopL:false,
            isShow:false,
            isTabelShow:false,
            title:'',
            sideTitle:'',
            centent:'',
            isFold: false,
            jyparam: {
            	parm:''
            },
            jyList:'',
        },
        methods:{
            // 取消
            closes: function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                // malert('111','top','defeadted');

            },
            // 确定
            confirms:function () {
                $(".side-form-bg").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                malert('222','top','success');
            },
            dbEdit:function(item){
            	wrapper.str=!wrapper.str
            	wrapper.jsonList[wrapper.popIndex].jyxm=this.jyList[item].jyxm
            	wrapper.jsonList[wrapper.popIndex].jyxmmc=this.jyList[item].jyxmmc
            	wrapper.jsonList[wrapper.popIndex].jyxmpy=this.jyList[item].jyxmpy;
            	this.closes()
            },
            getJyxm:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=LisXtwhZldm&types=queryJyxm&param=" + JSON.stringify(wapse.jyparam), function(json) {
            		console.log(json);
            		if(json.a == '0'){
            			//检验详情
                		wapse.jyList = json.d.list;
                		console.log(wapse.jyList);
            		}else{
            			malert('获取检验项目失败！','top','defeadted')
            		}
            		
            	});
            }
        },
        watch:{
        	'jyparam.parm':function(){
        		wapse.getJyxm();
        	}
        }
    });
    var isTabel=new Vue({
        el:'#isTabel',
        mixins: [dic_transform, baseFunc, tableBase],
        data:{
            isTabelShow:false,
            isShow:false,
            minishow:true,
            isShowpopL:false,
            popContent:{},
            item: 0,
            appNum:[],
            appObj:{},
        },
        created:function () {
            this.append()
        },
        methods:{
            sc: function (index) {
                this.appNum.splice(index,1)
                // for(var i=0;i<this.appNum.length;i++){
                //     if(this.appNum[i].num==index){
                //
                //     }
                // }

            },
            append: function () {
                this.item = this.item + 1
                this.appObj.num=this.item
                this.appNum.push(JSON.parse(JSON.stringify(this.appObj)))
                this.$nextTick(function () {
                    $(".zui-form .lsittext").uicomplete({
                        iskeyup: true
                    });
                })
            },
            tabshow:function () {
                this.isTabelShow=true;
                this.minishow=false;
                pop.dyShow=false;
            },
            showDom:function () {
                pop.isShowpopL=true;
                pop.isShow=true;
                pop.dyShow=false;
                pop.flag=true;
            }
        },
    })
    
    //初始化数据
    wrapper.getData();

