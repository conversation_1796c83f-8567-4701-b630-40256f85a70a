<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>结果选项</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="jgxx.css" rel="stylesheet"/>
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10" id="jyxm_icon">
<div class="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addJg">新增结果</button>

            <button class="tong-btn btn-parmary-b " @click="bc"><i class="icon-baocun paddr-r5"></i>保存</button>
            <button class="tong-btn btn-parmary-b "><i class="icon-yl paddr-r5"></i>预览</button>
            <button class="tong-btn btn-parmary-b "><i class="icon-dysq paddr-r5"></i>打印</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">搜索</label>
                    <div class="zui-input-inline margin-f-l30">
                        <input class="zui-input wh180" v-model="param.mc" placeholder="请输入关键字" type="text"/>
                    </div>

                </div>

                    <button class="zui-btn btn-primary xmzb-db margin-f-l15" @click="queryAll">查询</button>

            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th><div class="zui-table-cell cell-s"><span>结果编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结果名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>适用范围</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table" >
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jgxxList" @dblclick="toDetail(item)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td><div class="zui-table-cell cell-s" v-text="item.bm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.mc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fw"></div></td>
                    <td>
                        <div class="switch cell-s" >
                            <input :id="'checked'+$index" type="checkbox" disabled v-model="item.tybz" true-value="0" false-value="1"  />
                            <label :for="'checked'+$index"></label>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>


    </div>

    <div id="pop">
        <!--<transition name="pop-fade">-->
        <div class="pophide" :class="{'show':isShowpopL}" style="z-index: 9999"></div>
        <div class="zui-form podrag pop-width bcsz-layer " :class="{'show':isShow}" style="height: max-content;padding-bottom: 20px">
            <div class="layui-layer-title " v-text="title"></div>
            <span class="layui-layer-setwin" style="top: 0;"><i class="color-btn" @click="isShowpopL=false,isShow=false">&times;</i></span>
            <div class="layui-layer-content" >
                <div class=" layui-mad layui-height" v-text="centent">
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button class="zui-btn table_db_esc btn-default" @click="isShowpopL=false,isShow=false">取消</button>
                <button class="zui-btn btn-primary table_db_save" @click="delOk">确定</button>
            </div>
        </div>
        <!--</transition>-->
    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide" style="width:320px;padding-top: 0;"  id="brzcList" role="form">
    <div class="tab-message">
        <a v-text="sideTitle"></a>
        <a href="javascript:;" class="fr closex ti-close"  @click="closes"></a>
    </div>
    <div class="ksys-side">
        <span class="span0">
            <i>结果编码</i>
            <input type="text" v-model="pd.bm" class="zui-input border-r4" placeholder="请输入" @keydown="nextFocus($event)"/>
        </span>
        <span class="span0">
            <i>结果名称</i>
            <input type="text" v-model="pd.mc" class="zui-input border-r4" placeholder="请输入" @keydown="nextFocus($event)"/>
        </span>
        <span class="span0">
            <i>适用范围</i>
            <input type="text" v-model="pd.fw" class="zui-input border-r4" placeholder="请输入" @keydown="nextFocus($event)"/>
            <!-- <select class="zui-input">
                <option>abo血型鉴定</option>
                <option>abo血型鉴定</option>
                <option>abo血型鉴定</option>
            </select> -->
        </span>
        <span class="span0">
            <i>状态</i>
             <div class="switch" >
                 <input id="popChecked"  v-if="isXzShow" type="checkbox" v-model="checked" true-value="0" false-value="1"  />
                  <input id="popChecked"   v-if="isBjShow" type="checkbox" v-model="pd.tybz" true-value="0" false-value="1"   />
                            <label for="popChecked"></label>
                        </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" v-if="isXzShow" @click="confirms">保存</button>
        <button class="zui-btn btn-primary xmzb-db" v-if="isBjShow" @click="bj">保存</button>
    </div>
</div>



<div class="filter">
    <filter-select v-on:close="guanbi" v-on:save="baocun" v-if="isShow"></filter-select>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="jgxx.js"></script>

</body>
</html>
