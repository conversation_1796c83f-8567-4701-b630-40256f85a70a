var payNo={
    data:{
        zflxList:[],
        resObj:{},
        focus:false,
        codeNum:0,
    },
    computed:{
        codeContentFun:function (){
            if(this.jsjlContent.codeContent && this.jsjlContent.codeContent.length==18){
                this.payment()
            }else if(this.codeNum>3){
                this.ifClick = true;
                malert('你已经连续扫码错误三次，请重新确认', 'right', 'defeadted');
            }else if(this.codeNum<3){
                ++this.codeNum
            }
        },
    },
    methods:{
        blurFun:function (event){
            if(this.focus && this.jsjlContent.codeContent.length!=18){
                $('#codeContent').focus();
            }
        },
        // 页面加载时自动获取支付类型数据
        GetZflxData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx&dg=" + JSON.stringify(dg), function (json) {
                if (json.a == 0) {
                    this.zflxList = json.d.list;
                    this.jsjlContent.zflxbm = this.zflxList[0].zflxbm;
                } else {
                    malert(json.c + ",支付类型列表查询失败", 'right', 'defeadted');
                    return false;
                }
            });
        },
        saveSf:function (){
            var zflxjk=this.listGetName(this.zflxList, this.jsjlContent.zflxbm, 'zflxbm', 'zflxjk');
            if(zflxjk == '008'){
                if(this.jsjlContent.zflxbm != '27'){
                    $('#codeContent').focus();
                    this.focus=true;
                    common.openloading("","请出示付款码。。。。");
                }else {
                    this.payment()
                }
            }else if(this.jsjlContent.zflxbm == '28') {
                this.jsjlContent.qtzf= $("#ssje").val();
                this.jsjlContent.xjzf='0.0';
                this.doSaveBrghFun()
            }else {
                this.doSaveBrghFun()
            }
        },
        doSaveBrghFun:function (){
            this.jsjlContent=Object.assign(this.jsjlContent,this.resObj);
            this.successSaveAndPrint();
        },
        payment:function (){
            this.resObj={};
                var yylx=this.jsjlContent.zflxbm=='27'?'00':'02',resObj;
                var param={
                    czybm:userId,
                    czyxm:userName,
                    mzlx:this.mzlx,
                    hisGrbh:rightVue.brxxContent.ghxh,
                    bzsm:'门诊',
                    inJson: {
                        yylx:yylx,
                        fyje:String($("#ssje").val()),
                        zfcm:this.jsjlContent.codeContent,
                    },
                    yljgbm:jgbm
                }
            param.inJson=JSON.stringify(param.inJson);
                this.postAjax("http://localhost:9001/posinterface/xf",JSON.stringify(param), function (json) {
                    if (json.returnCode == 0) {
                        resObj=JSON.parse(json.outResult);
                        if(resObj.bank_code != ''){
                            malert(json.msgInfo , 'right');
                        }else {
                            malert(resObj.resp_chin , 'right', 'defeadted');
                            resObj=false;
                        }
                    } else {
                        resObj=false;
                        malert(json.c , 'right', 'defeadted');
                        return false;
                    }
                },function (){
                    common.closeLoading();
                });
            common.closeLoading();
                if(resObj){
                    if(this.jsjlContent.zflxbm == '27') {
                        this.resObj={
                            yjyrq:resObj.txndate,//原交易日期
                            orderNo:resObj.refdata,//原交易参考号
                        };
                    }else {
                        this.resObj={
                            payNo:resObj.unionMerchant ,//原交易参考号
                        };
                    }
                    this.jsjlContent.qtzf= $("#ssje").val();
                    this.jsjlContent.xjzf='0.0';
                    this.doSaveBrghFun();
                    this.focus=false;
                }else {
                    this.ifClick = true;
                    return false;
                }

        },
    },
};
var getDataObj= {
    data: {},
    methods:{
        getCsqx: function () {
            // 先获取到科室编码
            var ksparm = {
                "ylbm": 'N050012003'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                if (json.a == 0 && json.d) {
                    tableInfo.ksbm = json.d[0].ksbm;
                    // 获取参数权限
                    var parm = {
                        "ylbm": 'N050012003',
                        "ksbm": json.d[0].ksbm
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0 && json.d) {
                            for (var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                                switch (csjson.csqxbm) {
                                    case "N05001200301": // 门诊收费发票打印笔数(设置每张发票打印费用最大笔数)
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100101 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200302": // 门诊收费保存快捷键(F2)
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100102 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200303": // 门诊药品划价方式0-药房划价，1-收费划价
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100103 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200304": // 门诊收费核算科室生成方案0-按费用项目核算科室生存，1-为空时，默认医生科室
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100104 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200305": // 门诊收费是否显示未交款合计0-不显示,1=显示
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100105 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200306": // 门诊收费语音报数设置0-无,1=蓝青设备,2=上海宇驰
                                        if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            tableInfo.csqxContent.cs00500100106 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200307": // 收药品费下账方式0-收费不下库存,1=收费下库存
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100107 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200308": // 是否允许删除门诊医生站产生的费用0=不允许，1＝允许
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100108 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200309": // 收费金额是否允许为零0-不允许，1-允许
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100109 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200310": // 门诊收费录入保险支付金额0-不允许录入;1-允许录入
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100110 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200311": // 保险快捷键(F12)
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100111 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200312": // 票据打印是否提示0-不提示；1-提示（焦点默认为‘是’）; 2-1-提示（焦点默认为‘否’）
                                        if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "") {
                                            tableInfo.csqxContent.cs00500100112 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200313": // 是否按科室过滤医生0、否；1、是
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100113 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200314": // 是否使用药房排队0、否 1、是
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100114 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200315": // 门诊申请收费模式0-现金  1-医疗卡扣费
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100115 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200316": // 使用开票软件接口1-使用，0-不使用
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100116 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200317": // 门诊结算默认支付类型设置支付类型
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100117 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200320": // 是否需要上传检查检验信息0-否，1-是
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100120 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200322": // 门诊默认费别
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100122 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200323": // 门诊默认保险类别
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs00500100123 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200324": // POS支付接口
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs05001200324 = csjson.csz;
                                            popCenter1.csqxContent.cs05001200324 = csjson.csz;
                                            if(tableInfo.csqxContent.cs05001200324 && tableInfo.csqxContent.cs05001200324 == '001'){
                                                rightVue.getGztyzfCs();
                                            }
                                        }
                                        break;
                                    case "N05001200325": // 门诊上传检查检验接口方式 0 原始 1 webservice
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs05001200325 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200326": // 删除附加费是否作废费用 0-不作废；1-作废
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs05001200326 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200327": // 微信支付，这个参数主要是用来区分是那个机构，那个地区下的，方便调对应的接口
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs05001200327 = csjson.csz;
                                            popCenter1.csqxContent.cs05001200327 = csjson.csz;
                                            if(tableInfo.csqxContent.cs05001200327 && tableInfo.csqxContent.cs05001200327 == '001'){
                                                rightVue.getGztyzfCs();
                                            }
                                        }
                                        break;
                                    case "N05001200328": // 支付宝支付，这个参数主要是用来区分是那个机构，那个地区下的，方便调对应的接口
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.cs05001200328 = csjson.csz;
                                            popCenter1.csqxContent.cs05001200328 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200329": // 是否向翼展pacs传输数据
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200329 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200331": // 门诊收费三方支付方式联动
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200331 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200333": // 门诊收费使用健康卡（宣汉）0-否 1-是
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200333 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200334": // 药监平台程序地址
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200334 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200335": //大病商保,计生救助,民政优抚,精准目录补偿,0不显示，1显示
                                        if (csjson.csz) {
                                            popCenter1.csqxContent.N05001200335 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200337": //0-帆软 1-网页
                                        if (csjson.csz) {
                                            popCenter1.csqxContent.N05001200337 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200336": //限制正负费用一起收 0-否 1-是
                                        if (csjson.csz) {
                                            popCenter1.csqxContent.N05001200336 = csjson.csz;
                                            tableInfo.csqxContent.N05001200336 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200338": //是否允许整体优惠 0-否 1-是
                                        if (csjson.csz) {
                                            popCenter1.csqxContent.N05001200338 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200339": // 门诊收费使用健康卡（宣汉）0-否 1-是
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200339 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200341": // 打印补偿单格式（宣汉）0：二版样式，1:一版样式'
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200341 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200342": //门诊收费是否允许删除费用0：0：允许，1:不允许
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200342 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200343": //门诊收费处是否允许添加费用0：0：允许，1:不允许
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200343 = csjson.csz;
                                            if("1"==csjson.csz){
                                                $("#mxxm").attr("disabled", true);
                                            }else{
                                                $("#mxxm").attr("disabled", false);

                                            }
                                        }
                                        break;
                                    case "N05001200345": //博思电子发票门诊调用地址
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200345 = csjson.csz;
                                        }
                                        break;
                                    case "N05001200346": //免费核酸检测费用项目编码(剑河9801)
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200346 = csjson.csz;
                                        }
                                        break;
                                }
                            }
                            tableInfo.getData();
                            rightVue.getPjxx(); // 票据信息
                            rightVue.czyfy(); // 操作员收费情况
                            rightVue.GetBrfbData(); // 病人费别
                            rightVue.GetBxlbData(); // 保险类别
                            rightVue.GetGhksData(); // 开单科室
                            rightVue.GetGhysData(); // 门诊医生
                            rightVue.GetHsksData(); // 核算科室
                            // 是否显示操作员未交款合计
                            console.log("参数");
                            console.log(tableInfo.csqxContent.cs00500100105);
                            if (tableInfo.csqxContent.cs00500100105 == '0') {
                                $("#yjhj").hide();
                            } else {
                                $("#yjhj").show();
                            }
                        } else {
                            malert("参数权限获取失败!" + json.c, 'right', 'defeadted');
                        }
                    });

                    var parm = {
                        "ylbm": 'N050012002',
                        "ksbm": json.d[0].ksbm
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0) {
                            if (json.d.length > 0) {
                                for (var i = 0; i < json.d.length; i++) {
                                    var csjson = json.d[i];
                                    if(csjson.csqxbm == 'N05001200202'){
                                        if (csjson.csz) {
                                            tableInfo.csqxContent.N05001200202 = csjson.csz;
                                        }else{
                                            tableInfo.csqxContent.N05001200202 = 3;
                                        }
                                    }
                                }
                            }

                        } else {
                            malert("参数权限获取失败!" + json.c, 'right', 'defeadted');
                        }
                    });
                } else {
                    malert("权限科室获取失败!" + json.c, 'right', 'defeadted');
                }
            });
        },
        //获取票据信息
        getPjxx: function () {
            var str_param = {
                pjlx: "02"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=PjhInfo&types=getpj&parm=" + JSON.stringify(str_param), function (json) {
                // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                rightVue.pjxxContent = {};
                if (json.a == 0) {
                    if (json.d[0] != null) {
                        rightVue.pjxxContent.dqsyh = json.d[0].dqsyh;
                        rightVue.pjxxContent.fpzs = json.d[0].fpzs;
                        rightVue.pjxxContent.qsh = json.d[0].qsh;
                        rightVue.pjxxContent.jsh = json.d[0].jsh;
                    } else {
                        malert("没有查询到相关票据信息，请先领票!", 'right', 'defeadted');
                    }
                } else {
                    malert("获取票据信息失败!" + json.c, 'right', 'defeadted');
                }
            });
        },
        // 获取操作员收取费用合计
        czyfy: function () {
            var str_param = {
                czybm: userId
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryByCzybm&parm=" + JSON.stringify(str_param), function (json) {
                // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    rightVue.srhj = rightVue.fDec(json.d, 2); // 赋值操作
                    rightVue.getYjhj(); // 加载完成之后调用应上交
                } else {
                    malert("获取操作员费用失败!" + json.c, 'right', 'defeadted');
                }
            });
        },
        // 调用后台查询应上交的金额
        getYjhj: function () {
            var str_param = {
                sfjk: 1,
                czybm: userId
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryByCzybm&parm=" + JSON.stringify(str_param), function (json) {
                // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    if (json.d == null) {
                        json.d = 0;
                    }
                    if (rightVue.srhj == null || rightVue.srhj == "") {
                        rightVue.srhj = 0
                    }
                    rightVue.yjhj = rightVue.fDec(parseFloat(rightVue.srhj) - parseFloat(json.d), 2);
                } else {
                    malert("获取操作员费用失败!" + json.c, 'right', 'defeadted');
                }
            });
        },
        GetBrfbData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=brfb&dg=" + JSON.stringify(dg), function (json) {
                if (json.a == 0) {
                    rightVue.brfbList = json.d.list;
                } else {
                    malert(json.c + ",病人费别列表查询失败", 'right', 'defeadted');
                    return false;
                }
            });
        },
        GetBxlbData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=bxlb", function (json) {
                if (json.a == 0) {
                    rightVue.bxlbList = json.d.list;
                } else {
                    malert(json.c + ",保险类别列表查询失败", 'right', 'defeadted');
                    return false;
                }
            });
        },
        GetGhksData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            var bean = {
                "ghks": "1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" +
                JSON.stringify(dg) + "&json=" + JSON.stringify(bean),
                function (json) {
                    if (json.a == 0) {
                        rightVue.ghksList = json.d.list;
                    } else {
                        malert(json.c + ",挂号科室列表查询失败", 'right', 'defeadted');
                        return false;
                    }
                });
        },
        GetGhysData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            var bean = {
                "ysbz": "1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmGhys&dg=" +
                JSON.stringify(dg) + "&json=" + JSON.stringify(bean),
                function (json) {
                    if (json.a == 0) {
                        rightVue.mzysList = json.d.list;
                        rightVue.glmzysList = rightVue.mzysList;
                    } else {
                        malert(json.c + ",医生列表查询失败", 'right', 'defeadted');
                        return false;
                    }
                });
        },
        GetHsksData: function () {
            var dg = {
                "page": 1,
                "rows": 20000,
                "sort": "",
                "order": "asc"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(dg), function (json) {
                if (json.a == 0) {
                    rightVue.hsksList = json.d.list;
                } else {
                    malert(json.c + ",核算科室列表查询失败", 'right', 'defeadted');
                    return false;
                }
            });
        },
        getPrintName:function(){
            window.top.J_tabLeft.csqxparm.csbm = "N010024002";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0 && json.d  && json.d.length > 0) {
                    popCenter1.MzsfPrint = json.d[0].csz;
                } else {
                    // malert('获取打印机名称失败', 'right', 'defeadted');
                }
            });
        },
        getJrjs: function () {
            var parm = {
                beginrq: getTodayDateBegin(),
                endrq: getTodayDateEnd(),
                czybm: userId,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryJrjs&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    rightVue.jsxxJsonList = json.d.list;
                    var jehj = 0;
                    for (var i = 0; i < json.d.list.length; i++) {
                        jehj += json.d.list[i].xjzf
                    }
                    rightVue.jrsrhj = rightVue.fDec(jehj, 2);
                }
            });
        },
    },
}
