/**
 * Created by mash on 2017/10/2.
 */
(function () {
	var toolmenu=new Vue({
		  el: '#toolMenu',
	        mixins: [dic_transform, baseFunc, tableBase],
	        data: {
	        },
	        methods:{
	        	load:function(){
	        		cssz.load();
	        	},
	        	save:function(){
	        		cssz.save();
	        	}
	        }

	});
    var cssz = new Vue({
        el: '#cssz',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
        	jkcsList:[],
            json: {},
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            param:{},
            id:null,
            bxlbList:[],
            bxlbbm:null,
            bxurl:null
        },
        methods: {
            // 获取接口信息
            getData: function () {
            	var param = {
                		page:1,
                		rows:30,
                		sort:"yljgbm",
                		order:"asc"
                	};
            	$.getJSON("/actionDispatcher.do?reqUrl=BxInterface&url="+cssz.bxurl+"&bxlbbm="+cssz.bxlbbm+"&types=cssz&method=query&parm="+JSON.stringify(param), function (json) {
                    if(json.a == "0"){
                    	var res=eval('('+json.d+')');
                    	cssz.jkcsList = res.list;
                    }
                });
            },
            load:function(){
            	for(var i=0;i<cssz.jkcsList.length;i++){
            		if(cssz.jkcsList[i]['ddzbm']=='1'){
            			cssz.jkcsList[i]['ddzbm']=true;
            		}else{
            			cssz.jkcsList[i]['ddzbm']=false;
            		}
            		if(cssz.jkcsList[i]['dyhyxxMz']=='1'){
            			cssz.jkcsList[i]['dyhyxxMz']=true;
            		}else{
            			cssz.jkcsList[i]['dyhyxxMz']=false;
            		}
            		if(cssz.jkcsList[i]['dyhyxxZy']=='1'){
            			cssz.jkcsList[i]['dyhyxxZy']=true;
            		}else{
            			cssz.jkcsList[i]['dyhyxxZy']=false;
            		}
            		if(cssz.jkcsList[i]['dynhjsd']=='1'){
            			cssz.jkcsList[i]['dynhjsd']=true;
            		}else{
            			cssz.jkcsList[i]['dynhjsd']=false;
            		}
            		if(cssz.jkcsList[i]['dynhjsdZy']=='1'){
            			cssz.jkcsList[i]['dynhjsdZy']=true;
            		}else{
            			cssz.jkcsList[i]['dynhjsdZy']=false;
            		}
            	}
            	cssz.json = JSON.parse(JSON.stringify(cssz.jkcsList[0]));
            },
            // 保存接口信息
            save: function () {
            	if(cssz.json['ddzbm']==true){
        			cssz.json['ddzbm']='1';
        		}else{
        			cssz.json['ddzbm']='0';
        		}
            	if(cssz.json['dyhyxxMz']==true){
        			cssz.json['dyhyxxMz']='1';
        		}else{
        			cssz.json['dyhyxxMz']='0';
        		}
            	if(cssz.json['dyhyxxZy']==true){
        			cssz.json['dyhyxxZy']='1';
        		}else{
        			cssz.json['dyhyxxZy']='0';
        		}
            	if(cssz.json['dynhjsd']==true){
        			cssz.json['dynhjsd']='1';
        		}else{
        			cssz.json['dynhjsd']='0';
        		}
            	if(cssz.json['dynhjsdZy']==true){
        			cssz.json['dynhjsdZy']='1';
        		}else{
        			cssz.json['dynhjsdZy']='0';
        		}
            	cssz.json.hospitalLx='0';
            	console.log(cssz.json);
            	$.getJSON("/actionDispatcher.do?reqUrl=BxInterface&url="+cssz.bxurl+"&bxlbbm="+cssz.bxlbbm+"&types=cssz&method=save&parm="+JSON.stringify(cssz.json), function (json) {
            		if (json.a == "0") {
						malert("新增成功");
						cssz.getData();
						 setTimeout(function () {
						    	cssz.load();  //锁表头初始化
						         }, 500);
					} else {
						malert("新增失败")
					}
                });
            },
            getBxlb:function(){
            	$.getJSON("/actionDispatcher.do?reqUrl=XtwhKsryBxlb&types=query&json=" + JSON.stringify(this.param), function (json) {
                    if(json.a == "0"){
                    	cssz.bxlbList = json.d.list;
                    }
                });
            },
            getbxlb:function(){
              	var param = {bxjk:"001"};
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=XtwhKsryBxlb&types=query&json="
                    + JSON.stringify(param), function (json) {
                        if (json.a == 0){
                        	if (json.d.list.length > 0){
                        		cssz.bxlbbm = json.d.list[0].bxlbbm;
                        		cssz.bxurl = json.d.list[0].url;
                        	}
                        }else{
                        	malert("保险类别查询失败!"+json.c)
                        }
                    });
              },
        }
    });
    cssz.getBxlb();
    cssz.getbxlb();
    setTimeout(function () {
    	cssz.getData();  //锁表头初始化
         }, 200);
    setTimeout(function () {
    	cssz.load();  //锁表头初始化
         }, 1000);
})();
