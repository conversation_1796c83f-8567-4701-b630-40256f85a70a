var wrapper=new Vue({
    el: '#wrapper',
    components: {
        'search-table': searchTable
    },
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data:{
		
        pkhShow:false,
		
		cgList :[],
        searchCon:[],
        selSearch:-1,
		them: {
		    '药品编码': 'ypbm','药品名称': 'ypmc', '药品规格': 'ypgg', '产地': 'cdmc', '供货单位': 'ghdwmc',
		    '库存': 'qyzl',
		},
		them_tran: {},
        
        queryPage: {
            page: 1,
            rows: 100,
            total: null
        },
		zhuangtai: {
		    "0": "未审核",
		    "1": "已审核",
		    "2": "已作废",
		    "3": "未通过",
		},
		cglzt_tran:{
			'1':'已审核',
			'0':'未审核',
			'9':'全部',
		},
        jhlx_tran:{
            '0':'周',
            '1':'月',
            '2':'季',
            '4':'年',
        },
        table:[
			
            {text:'商品名',className:'cell-s',field:'ypspm'},
            {text:'药品来源',className:'cell-m',field:'ypflmc'},
            {text:'规格',className:'cell-m',field:'ypgg'},
			{text:'生产商',className:'cell-m',field:'ghdwmc'},
			{text:'原产地',className:'cell-m',field:'cdmc'},
            {text:'单位',className:'cell-m',field:'kfdwmc'},
            {text:'医保类型',className:'cell-m',field:'tclbmc'},
            {text:'上期数量',className:'cell-m',field:'sqcgsl'},
            {text:'库存上限',className:'cell-m',field:'kcsx'},
            {text:'库存下限',className:'cell-m',field:'kcxx'},
            {text:'库存数量',className:'cell-m',field:'qyzl'},
            {text:'库存金额',className:'cell-m',field:'qyzje'},
            {text:'上期销量',className:'cell-m',field:'sqxsl',},
            {text:'本期销量',className:'cell-m',field:'bqxsl'},
            {text:'计划数量',className:'cell-m',field:'cgsl',inType:'input'},
            {text:'上次供应商',className:'cell-m',field:'sqghdwmc'},
            {text:'说明',className:'cell-m',field:'bz',inType:'input'},
            {text:'基本药物',className:'cell-m',field:'sbjbyw'},
            {text:'批准文号',className:'cell-m',field:'pzwh'},
			
        ],
        popContent:{
			zjehj:0,
            ypbz0:'0',
            dmypbz:'0',
            jslyp:'0',
            bzff:'1',
            jhlx:'1',
            cyypfl:'',
            jxbm:[],
            zlbm:[],
            ghdw:[],
            yfbm:[],
            kfbm:[],
            jxNews:[],
            ypzlNews:[],
            ypcdNews:[],
            yfbmNews:[],
            kfbmNews:[],
        },
		tppopContent:{
			
		},
		searchpopContent:{
			
		},
        jsonList:[],
        which:0,
        ypjx:[],
        ypzl:[{
            "yplx": "2",
            "ypzlbm": "09",
            "ypzlmc": "中药颗粒"
        },{
            "yplx": "2",
            "ypzlbm": "03",
            "ypzlmc": "中药饮片"
        }],
        yfList:[{
            "yfbm": "04",
            "yflx": "1",
            "yfmc":"中药颗粒药房",
			"yljgbm": "000001"
		},{
            "yfbm": "03",
            "yflx": "1",
            "yfmc": "中药房",
            "yljgbm": "000001",

        }],
        yfkfList:[{
            "kfbm": "02",
            "kfmc": "中药库房",
			"ksbm": "103",
            "ksmc": "中药库"
		}],
        ypcd:[],
        modelText:[{text:'计划'},{text:'药品分类'},{text:'供应商'},{text:'来源药房'},{text:'来源库房'}],
		param: {
		    shzfbz:'',
		    rows: 10,
		    page: 1,
		    beginrq: null,
		    endrq: null,
		    jhdh: '',
			order:"desc"
		},
		isSh : false,
		isCk:false,
		ypKcList:[],
		ypCgHistoryList:[],
		editjhdh:'',
		tpsjcbz:'',
    },
    computed:{
        list:function (){
			var obj = this;
			obj.popContent.zjehj = 0;
            this.jsonList.forEach(function (item){
				if(item && item.ypjj){
					item.cgjj=item.ypjj;
					if(item.cgsl){
						obj.popContent.zjehj  = obj.MathAdd(obj.popContent.zjehj,obj.Mul(item.ypjj,item.cgsl));
						
					}
				}
				if(item && item.ypbm && item.ypmc){
					item.ypbmmc = '【'+item.ypbm+'】'+item.ypmc
				}
            })
        },
    },
	
    mounted:function () {
		var myDate = new Date();
		this.param.beginrq = this.fDate(new Date(), 'date') + ' 00:00:00';
		this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
		laydate.render({
		    elem: '#timeVal',
		    eventElem: '.zui-date',
		    value: this.param.beginrq,
		    type: 'datetime',
		    theme: '#1ab394',
		    done: function (value, data) {
		        wrapper.param.beginrq = value;
		        wrapper.getData();
		    }
		});
		laydate.render({
		    elem: '#timeVal1',
		    eventElem: '.zui-date',
		    value: this.param.endrq,
		    type: 'datetime',
		    theme: '#1ab394',
		    done: function (value, data) {
		        wrapper.param.endrq = value;
		        wrapper.getData();
		    }
		});
        this.getJx();
        this.getYf();
        this.getKfData();
        this.popContent.zdy = userId;
        this.popContent.zdymc = userName;
        this.popContent.cgry = userId;
        this.popContent.zdrq = this.fDate(new Date(),'datetime');
		var jhdh = sessionStorage.getItem('jhdh')
		if(jhdh){
			this.getCgmx();
			this.popContent.jhdh = jhdh;
		}
		if(sessionStorage.getItem('isCk') && sessionStorage.getItem('isCk')=='false'){
			this.isCk = false
		}else{
			this.isCk = true
		}
		if(sessionStorage.getItem('isSh') && sessionStorage.getItem('isSh')=='false'){
			this.isSh = false
		}else{
			this.isSh = true
		}
		
		
		Vue.set(this.param, 'zfbz', '9');
    },
    methods:{
		
		getCgmx:function(){
			
			var json = {
			    jhdh: sessionStorage.getItem('jhdh')
			};
			let mxff = "cgjhmxN";
			if(!this.isSh){
				mxff = "cgjhmx";
			}
			
			common.delayOpenloading({el: '.zui-table-view'});
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types='+mxff+'&json='+JSON.stringify(json), function (data) {
						        if (data.a == 0) {
						            wrapper.pkhShow=false;
						            wrapper.jsonList = data.d;
						            common.closeLoading()
						        } else {
						            malert(data.c, 'top', 'defeadted');
						            common.closeLoading()
						        }
						    }, function (error) {
						    });
		},
		tabBg: function (page) {
		    window.location.href=page + ".html";
		},
		tabBgqh:function (index){
		            this.which=index;
		},
		mouseDown:function(event){
			            
		},
		nextFocus1: function (event,name) {
			
		    var _input = $("#zttable input").not(":disabled,input[type=checkbox],input[type=date]");
		    if (event.keyCode == 13) {
		        for (var i = 0; i < _input.length; i++) {
		            if (_input.eq(i)[0] == event.currentTarget) {
						if(name =='bz'){
							if(_input.length>i+1){
								_input.eq(i + 1).focus();
							}else{
								wrapper.addRow();
							}
						}else{
							_input.eq(i + 1).focus();
						}
		                break;
		            }
		        }
		        return false
		    }
		},
		resultChange: function (val) {
				
				Vue.set(this.param, 'zfbz', val[0]);
				if(val[0] =='9'){
					Vue.set(this.param, 'shzfbz', '');
				}else{
					Vue.set(this.param, 'shzfbz', val[0]);
				}
				wrapper.getData();
		},
		
		sjcancel:function(){
			
			if(wrapper.jsonList && wrapper.jsonList.length>0 && wrapper.jsonList[0].sjcbz){
				var temp={
					sjcbz:wrapper.jsonList[0].sjcbz
				}
				
				this.postAjax('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=deleteTmp' ,JSON.stringify(temp), function (json) {
				    if (json.a == 0 ) {
				        
				    } else {
				        malert(json.c, 'top', 'defeadted');
				    }
				});
			}
			wrapper.cancel();
		},
		//取消
		cancel: function () {
		    wrapper.tabBg('cgjh');
		},
		loadNum: function () {
		    this.num = wrapper.num;
		},
		getAllSj:function(num){
			this.tjcheckNum = num;
			wrapper.ypKcList=[];
			wrapper.ypCgHistoryList =[];
			if(!wrapper.jsonList[wrapper.tjcheckNum].ypbm){
				return false;
			}
			this.getYfkc();
			this.getCgHistory();
		},
		getYfkc:function(){
			var json = {
			    ypbm: wrapper.jsonList[wrapper.tjcheckNum].ypbm
			};
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=getYfKc&json='+JSON.stringify(json), function (data) {
			        if (data.a == 0 && data.d) {
			            wrapper.ypKcList = data.d;
			        } else {
			            malert(data.c, 'top', 'defeadted');
			        }
			    }, function (error) {
			    });
		},
		getCgHistory:function(){
			var json = {
			    ypbm: wrapper.jsonList[wrapper.tjcheckNum].ypbm
			};
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=getCgHistoryList&json='+JSON.stringify(json), function (data) {
			        if (data.a == 0 && data.d) {
			            wrapper.ypCgHistoryList = data.d;
			        } else {
			            malert(data.c, 'top', 'defeadted');
			        }
			    }, function (error) {
			    });
		},
		getReCheckYf:function (val){
		    Vue.set(this.popContent,val[0][1],val[1])
		    
		},
        getReCheckAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.ypjx.length; i++) {
                    Vue.set(this.popContent.jxNews,i,this.ypjx[i].jxbm)
                }
            }else {
                this.popContent.jxNews=[];
            }
        },
        getReCheckYpAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.ypzl.length; i++) {
                    Vue.set(this.popContent.ypzlNews,i,this.ypzl[i].ypzlbm)
                }
            }else {
                this.popContent.ypzlNews=[];
            }
        },
        getReCheckGysAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.ypcd.length; i++) {
                    Vue.set(this.popContent.ypcdNews,i,this.ypcd[i].dwbm)
                }
            }else {
                this.popContent.ypcdNews=[];
            }
        },
        getReCheckYfAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.yfList.length; i++) {
                    Vue.set(this.popContent.yfbmNews,i,this.yfList[i].yfbm)
                }
            }else {
                this.popContent.yfbmNews=[];
            }
        },
        getReCheckKfAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.yfkfList.length; i++) {
                    Vue.set(this.popContent.kfbmNews,i,this.yfkfList[i].kfbm)
                }
            }else {
                this.popContent.kfbmNews=[];
            }
        },
        getReCheckOne:function (item,index,code,bm){
            if(this.popContent[code][index]){
                Vue.set(this.popContent[code],index,'')
            }else {
                Vue.set(this.popContent[code],index,item[bm])
            }
        },
        
        resultRydjChange:function (val){
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
            this.getCGData()
        },
        // getYf:function (){
        //     $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf', function (data) {
        //         if (data.a == 0) {
        //             wrapper.yfList = data.d.list;
        //             wrapper.getReCheckYfAll([['popContent','yfAll'],'1'])
        //         } else {
        //                 malert("药房获取失败!", 'top', 'defeadted');
        //             }
        //         });
        // },
        getReCheckTwo:function (val){
            if (val[0].length > 1) {
                Vue.set(this[val[0][0]], val[0][1], val[1]);
            } else {
                this[val[0][0]] = val[1];
            }
        },
        // getKfData: function () {
        //     //库房列表
        //     $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf', function (data) {
        //         if (data.a == 0) {
        //             wrapper.yfkfList = data.d.list;
        //             wrapper.getReCheckKfAll([['popContent','kfAll'],'1'])
        //         } else {
        //             malert(data.c,'top','defeadted');
        //         }
        //     });
        //     //获取列表
        // },
        getCGData:function (){
            common.openloading('','正在生成采购计划数据，请稍等')
            this.popContent.jxbm=this.popContent.jxNews.join(',');
            this.popContent.zlbm=this.popContent.ypzlNews.join(',');
            this.popContent.ghdw=this.popContent.ypcdNews.join(',');
            this.popContent.yfbm=this.popContent.yfbmNews.join(',');
			this.popContent.kfbm=this.popContent.kfbmNews.join(',');
            this.postAjax('/actionDispatcher.do?reqUrl=GetDropDown&types=scCg' ,JSON.stringify(this.popContent), function (json) {
                if (json.a == 0 ) {
                    wrapper.pkhShow=false;
					if(json.d.length != 0){
						wrapper.tpsjcbz = json.d[0].sjcbz
						wrapper.jsonList = json.d
					}
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
            common.closeLoading();
        },
        getJx:function (){
          var  pageParam = {
                'page': 1,
                'rows': 500
            }
            // 查询药品剂型
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpjx&types=query&dg=" + JSON.stringify(pageParam), function (json) {
                if(json.a== '0' && json.d){
                    wrapper.ypjx = json.d.list
                }else {
                    malert(json.c, 'top', 'defeadted');
                }
            });

            // 查询药品种类
            // $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl&dg=' + JSON.stringify(pageParam), function (json) {
            //     if(json.a== '0' && json.d) {
            //         wrapper.ypzl = json.d.list
            //     }else {
            //         malert(json.c, 'top', 'defeadted');
            //     }
            // });

            // 查询药品产地
         var   pageCdbm = {
                'page': 1,
                'rows': 5000,
                'sort': 'dwbm',
                'tybz': '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(pageCdbm), function (json) {
                if(json.a== '0' && json.d) {
                    wrapper.ypcd = json.d.list
                }
            });

        },
		sccg:function(){
			wrapper.editjhdh = '';
			wrapper.pkhShow=true;
		},
		addRow:function(){
			var cgjh = {};
			if(wrapper.jsonList.length>0){
				if(wrapper.jsonList[wrapper.jsonList.length-1].ypbm){
					Vue.set(wrapper.jsonList,wrapper.jsonList.length,cgjh)
				}
			}else{
				Vue.set(wrapper.jsonList,wrapper.jsonList.length,cgjh)
			}
			setTimeout(function () {   //延时0.1秒执行
                    var str = $('#myscrollbox > ul:last-child');
                    $(str).addClass('active');
                    $(str).siblings().removeClass('active');
					
					var div = document.getElementById('zttable');
					
					div.scrollTop = div.scrollHeight;
					var idname = 'ypmc'+''+(wrapper.jsonList.length-1).toString()
					console.log(idname)
					console.log($('#'+idname))
					$('#'+idname)[0].focus();
                }, 100);
				wrapper.mouseDown();
				
		},
		scmx: function (index) {
		    if (common.openConfirm("确认删除该条信息吗？", function () {
				
				wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=deleteTmp', JSON.stringify(wrapper.jsonList[index])).then(function (data) {
					
				        if (data.body.a == 0) {
				            wrapper.jsonList.splice(index, 1);
				        } else {
				            malert("数据保存失败" + data.body.c, 'top', 'defeadted');
				        }
				    }, function (error) {
						
				    });
				
		        
		    })){
		        return false;
		    }
		},
		
        Wf_change: function (add, index, val) {
			if(!val){
				return false;
			}
			wrapper.jsonList[index]['ypbmmc'] = val;
			var tps = wrapper.popContent.jhlx;
			wrapper.tppopContent = {};
			dqindex = index;
			if (!add) wrapper.page.page = 1;       // 设置当前页号为第一页
			// var _searchEvent = $(event.target.nextElementSibling).eq(0);
			
			
			setTimeout(function () { 
				
				    var _searchEvent = $(".selectGroup");
				    wrapper.queryPage.parm = trimStr(encodeURIComponent(val));
					wrapper.tppopContent.jhlx=tps;
                    wrapper.tppopContent.zlbm="03";
					wrapper.postAjax('/actionDispatcher.do?reqUrl=GetDropDown&types=scCg&dg=' + JSON.stringify(wrapper.queryPage) ,JSON.stringify(wrapper.tppopContent), function (data) {
					                if (data.a == 0 && data.d) {
					                    for (var n = 0; n < data.d.list.length; n++) {
											//console.log(data.d.list[n])
					                        data.d.list[n].ypbmmc = '【'+data.d.list[n].ypbm+'】'+data.d.list[n].ypmc
					                    }
										if (add) {//不是第一页则需要追加
										    wrapper.searchCon = wrapper.searchCon.concat(data.d.list)
										} else {
										    wrapper.searchCon = data.d.list;
										}
										wrapper.page.total = data.d.total;
										wrapper.selSearch = 0;
										if (data.d.list.length > 0 && !add) {
										    $(".selectGroup").hide();
										    _searchEvent.show();
										}
					                } else {
					                    malert(json.c, 'top', 'defeadted');
					                }
					            });
				
				
				
			}, 500);
        },
        changeDown: function (index, event, type, searchCon) {
            //赋值
            ypIndex = index;
            this.inputUpDown(event, wrapper.searchCon, 'selSearch');
			var _input = $("input").not(":disabled,input[type=checkbox],input[type=date]");
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				if(wrapper.searchCon[this.selSearch]){
					this.searchpopContent = wrapper.searchCon[this.selSearch]
					this.searchpopContent.sjcbz = wrapper.tpsjcbz
							this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=updateCgjhTmp', JSON.stringify(this.searchpopContent)).then(function (data) {
								
							        if (data.body.a == 0) {
							            wrapper.jsonList[index] = wrapper.searchpopContent;
										wrapper.jsonList[index].mxxh = index;
							            wrapper.$forceUpdate()
							        } else {
							            malert("数据保存失败" + data.body.c, 'top', 'defeadted');
							        }
							    }, function (error) {
									
							    });
					
					
					        
				}
                this.nextFocus(event);

                this.selSearch = -1;
                $(".selectGroup").hide();
            }
        },
        //双击选中下拉table
        selectOne1: function (item) {
            //查询下页
            if (item == null) {
                this.queryStr.page++;
                this.Wf_change(true, dqindex,this.searchpopContent['ypmc'])
                return;
            }
			item.sjcbz = wrapper.tpsjcbz
			this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=updateCgjhTmp', JSON.stringify(item)).then(function (data) {
				
			        if (data.body.a == 0) {
						wrapper.jsonList[dqindex] = item;
						wrapper.jsonList[dqindex].mxxh = dqindex;
						wrapper.selSearch = -1;
						$(".selectGroup").hide();
			        } else {
			            malert("数据保存失败" + data.body.c, 'top', 'defeadted');
			        }
			    }, function (error) {
					
			    });
			
            
        },
        saveFun:function (){
            //准备数据，包括退货单对象和退货单明细对象
			var tpJson = new Array()
			var tpi =0;
			for(var i=0;i<this.jsonList.length;i++){
					if(this.jsonList[i].ypbm){
						var tempBi = {};
						if(this.jsonList[i].bz){
							tempBi['bz'] = this.jsonList[i].bz;
						}else{
							tempBi['bz'] = '';
						}
						if(this.jsonList[i].cgsl){
							tempBi['cgsl'] = this.jsonList[i].cgsl;
						}else{
							tempBi['cgsl'] = 0;
						}
						tempBi['sjcbz'] = this.jsonList[i].sjcbz;
						tpJson.push(tempBi);
						tpi++;
					}
			}
			
			
			Vue.set(this.popContent, 'jhdh', wrapper.editjhdh);
			
			
			
            var json = {
                "list": {
                    cgjh: this.popContent,
                    cgjhmx: tpJson
                }
            };
			
			
			
			common.openloading('','正在保存采购计划数据，请稍等')
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCgjh&types=modify', JSON.stringify(json)).then(function (data) {
				common.closeLoading();
                    if (data.body.a == 0) {
                        malert("数据更新成功", 'top', 'success');
						wrapper.cancel();
                    } else {
                        malert("数据提交失败" + data.body.c, 'top', 'defeadted');
                    }
                }, function (error) {
					common.closeLoading();
                });
        },
    },
})