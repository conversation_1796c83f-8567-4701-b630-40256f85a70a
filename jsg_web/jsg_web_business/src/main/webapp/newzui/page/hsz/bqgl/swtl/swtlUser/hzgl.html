<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>死亡讨论-患者管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link href="hzgl.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
    <script type="text/javascript">
        common.openloading()
    </script>
</head>
<body class="body skin-default height background-f">
<div class="header-item over-auto">
    <header class="userNameBg printHide" v-cloak>
        <div class="flex">
            <div class="userNameImg">
                <img  src="/newzui/pub/image/maleBaby.png">
                <!--<img v-if="Brxx_List.nljd==1" src="/newzui/pub/image/maleBaby.png">-->
                <!--<img v-if="Brxx_List.nljd==2" src="/newzui/pub/image/femalebaby.png">-->
                <!--<img v-if="Brxx_List.nljd==3" src="/newzui/pub/image/Group <EMAIL>">-->
                <!--<img v-if="Brxx_List.nljd==4" src="/newzui/pub/image/Group <EMAIL>">-->
                <!--<img v-if="Brxx_List.nljd==5" src="/newzui/pub/image/juvenile.png">-->
                <!--<img v-if="Brxx_List.nljd==6" src="/newzui/pub/image/maid.png">-->
                <!--<img v-if="Brxx_List.nljd==7" src="/newzui/pub/image/youth.png">-->
                <!--<img v-if="Brxx_List.nljd==8" src="/newzui/pub/image/woman.png">-->
                <!--<img v-if="Brxx_List.nljd==9" src="/newzui/pub/image/grandpa.png">-->
                <!--<img v-if="Brxx_List.nljd==10" src="/newzui/pub/image/grandma.png">-->
                <!--<img v-if="Brxx_List.nljd==11" src="/newzui/pub/image/<EMAIL>">-->
            </div>
            <div class="text-color">
                <p class="userHeader">
                    <span class="userName">Henry-侯浩</span>
                    <span class="sex text">男</span>
                    <span class="nl text">88岁</span>
                </p>
                <div class="userCwh">
                    <span class="cwh text">挂号序号：20180629000001</span>
                    <span class="zyh text">科室：全科</span>
                    <span class="bq text">医师：潘树勇</span>
                    <span class="ks text">病种：无</span>
                    <span class="ks text">联系电话：无</span>
                    <span class="ks text">身份证：510703199403150010</span>

                </div>
                <div>
                    <p class="heaf text">更多详细信息>></p>
                </div>
            </div>
        </div>
        <div class="blRight">
            <span class="blImg imgHover" onclick="newPage()"></span>
        </div>
    </header>
    <main id="content" class="padd-l-10 padd-r-10">
        <div >
            <p class="swtl">死亡讨论<span class="iconfont icon-iocn1 font14 icon-font14 margin-l-10  color-ff5c63">未按标准填写，请重新填写！</span></p>
            <p class="swtl-hr"></p>
        </div>
        <div class="flex-container">
            <div class="width973">
                <ul class="swtl-item">
                    <li class="list VerticalLine">
                      <div class="flex-container flex-align-c">
                          <span class="icon-img-date swtl-icon"></span>
                          <span class="icon-text color-green">讨论时间</span>
                      </div>
                       <div class="padd-b-15">
                           <p class="date swtl-text-content color-333" v-if="editText">2020年12月12日 12:55:12</p>
                           <div class="zui-date relative swtl-text-content" v-show="!editText"  style="width: 360px">
                               <i class="datenox icon-rl"></i>
                               <input class="zui-input  color-333 text-indent-0" id="timeVal" style="padding-top: 0" :value="new Date()">
                           </div>
                       </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-map swtl-icon"></span>
                            <span class="icon-text color-d66b3f">讨论地点</span>
                        </div>
                        <div class="padd-b-15">
                        <p class="swtl-text-content color-333" v-if="editText">人民医院第一会议室</p>
                         <textarea v-if="!editText" rows="1" class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">人民医院第一会议室</textarea>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-ch swtl-icon"></span>
                            <span class="icon-text color-f4b26b">参加人员</span>
                        </div>
                            <div @mouseleave="hoverName()" class="flex-container margin-b-15 padd-b-15 swtl-text-content">
                                <div  class="Headimg" :class="!editText?'HeadPortrait':'HeadImg'" v-for="(list,$index) in 1" :id="list">
                                    <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                    <p class="color-757c83 font12">刘医生</p>
                                </div>
                                <span class="iconfont  icon-upload col6_span1" v-if="!editText"   @mouseenter="tipsShow=true"
                                      @mouseleave="tipsShow=false">
                                    <i v-show="tipsShow"  class="tips">添加主持人</i>
                                </span>
                                <div class="Headimg" :class="!editText?'HeadPortrait':''" v-for="(list,$index) in 6" :id="list">
                                    <p @mouseenter="hoverName(true,$index,$event)"  class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                    <p class="color-757c83 font12">刘医生</p>
                                </div>
                                <span class="iconfont  icon-upload col6_span1" v-if="!editText"   @mouseenter="tipsShow1=true"
                                      @mouseleave="tipsShow1=false">
                                    <i v-show="tipsShow1" class="tips">添加参与人</i>
                                </span>
                                <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                    <span class="djzt">科主任</span>
                                    <p class="headImg margin-t-10" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                    <p class="username text-center margin-t-5 margin-b-5 font-16">刘医生</p>
                                    <div class="flex-container flex-jus-c">
                                        <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                        <span class="color-green font12 padd-r-10">外科</span>
                                        <span class="color-757c83 font12">主任医师</span>
                                    </div>
                                </div>
                            </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-lc swtl-icon"></span>
                            <span class="icon-text color-7f5cff">临床诊断</span>
                        </div>
                        <div class="padd-b-15">
                        <p class="swtl-text-content color-333" v-if="editText">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1" class="zui-input swtl-text-content color-333 text-indent-0" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-bl swtl-icon"></span>
                            <span class="icon-text color-ff5c63">病历摘要</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1" class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                        <div class="flex-container flex-jus-e" style="width: 103.3%">
                            <button v-waves class="root-btn btn-parmary-f2a wh100" style="height: 32px" @click="accept()">拒绝邀请</button>
                            <button v-waves class="root-btn btn-parmary wh100" style="height: 32px" @click="accept()">接受邀请</button>
                            <button v-waves class="root-btn btn-parmary" @click="submit()">确定</button>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-nr swtl-icon"></span>
                            <span class="icon-text color-3f64d6">讨论内容</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1" class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-zj swtl-icon"></span>
                            <span class="icon-text color-72bc1a">讨论总结</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1"  class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-qz swtl-icon"></span>
                            <span class="icon-text color-00a7ff">现场照片</span>
                        </div>
                        <div class="padd-b-15 margin-t-5">
                            <ul class="Photo swtl-text-content flex-container">
                                <li class="PhotoImg icon-Photo iconfont" v-for="(list,index) in 8" @click="preview" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)"></li>
                            </ul>
                        </div>
                    </li>
                    <preview :preview-update="previewshow" :src="'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532503254326&di=a2175d25c9f4ccc582e3ae37531ec0a4&imgtype=0&src=http%3A%2F%2Fwww.wallcoo.com%2Fcartoon%2FKitsunenoir_Design_Illustration_V%2Fwallpapers%2F2560x1440%2Fkim-holtermand-reflections.jpg'" @preview-hide="previewHide"></preview>
                    <li class="list">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-jl swtl-icon"></span>
                            <span class="icon-text color-f4b26b">记录者/审核人</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1" class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="flex-container relative" style="width: 2.7%">
                <i class="iconfont icon-iocn46"></i>
                <span class="font14 cursor" @click="editShow" style="position: absolute;right: 0;z-index: 11111;">编辑</span>
                <i class="wtg"></i>
            </div>
        </div>
    </main>
    <footer>
    </footer>
</div>
<script type="text/javascript" src="hzgl.js"></script>
</body>
</html>
