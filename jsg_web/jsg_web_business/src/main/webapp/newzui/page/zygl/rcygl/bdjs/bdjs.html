<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>补打结算</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <script language="javascript" src="/newzui/pub/js/LodopFuncs.js"></script>
    <script type="text/javascript">
        common.openloading('html')
    </script>
    <style id="bdjs">
        .tem{
            position: relative;
            float: left;
            /* width: 300px; */
            /* height: 450px; */
            width: 800px;
            height: 500px;
            border: 1px solid green;
            margin-left: 20px;
            margin-top: 20px;
        }
        .item{
            position: absolute;
            display: inline-block;
            top: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            cursor: default;
            z-index: 100;
        }
        .drag{
            display: none;
        }
        .bqcydj_model{
            width: auto;
            padding: 20px;
            background: rgba(245, 246, 250, 1);
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
    </style>
</head>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="wrapper" v-cloak id="jyxm_icon">
    <div class="printArea printShow" id="printArea"></div>
    <div class="panel printHide">
        <div class="tong-top flex-container flex-align-c">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="remove">补打结算单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="reEBill">补开结算单</button>
            <button class="tong-btn btn-parmary-b icon-sx icon-font14 paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
        </div>
        <div class="flex-container padd-b-10 padd-t-10">
            <div class="flex-container">
                <div class="flex-container flex-align-c  margin-l-20">
                    <label class="whiteSpace margin-r-5 ft-14">时间</label>
                    <div class="flex-container  flex-align-c">
                        <input type="text" name="phone" v-model="param.beginrq" class="zui-input wh200 todate" placeholder="请选择开始日期" id="timeVal"><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " v-model="param.endrq"  placeholder="请选择结束日期" id="timeVal1" />
                    </div>
                </div>
            </div>
            <div class="flex-container">
                <div class="flex-container  margin-l-20">
                    <div class="">
                        <label class="whiteSpace ft-14 margin-r-1">检索</label>
                        <div class="zui-input-inline">
                            <input class="zui-input wh180" placeholder="住院号" type="text" v-model="param.parm" id="jsvalue" @keyup.enter="goToPage(1)"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-l-10 printHide padd-r-10">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th ><div class="zui-table-cell cell-s"><span>住院号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>病人姓名</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>发票号码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>科室名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结算操作员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>保险类别</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>费用合计</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>医疗卡支付</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>现金支付</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>其他支付</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>优惠金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结算退补</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结算欠费</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结算日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结算流水号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>是否交款</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>交款流水号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50" v-if="jsonList.length!=0">
                <tbody>
                <!--style="position: absolute"  :style="{marginTop:heightL[$index]+'px'}"-->
                <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="checkOne($index),switchIndex('activeIndex',true,$index)" ref="list">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.zyh}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.brxm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.fphm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.ksmc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.jsczyxm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s title" >{{item.bxlbmc}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.fyhj}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.ylkzf}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.xjzf}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.qtzf}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.yhje}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.jstb}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.jsqf}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.jsrq}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.fphm}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.sfjk==0 ? '否':'是'}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.jkpzh}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.bzsm}}</div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
    <!--弹出框-->
    <model :s="'确定'" :c="'取消'" @default-click="saveData" class="printHide" v-cloak  :model-show="true" @result-clear="isShow=false" @result-close="isShow=false"
           v-if="isShow" :title="'票据打印'">
        <div class="flex-container bqcydj_model flex-align-c flex-jus-c">
            <div>是否占用新的票号:</div>
            <select v-model="sfzyxh" class="wh120" style="height: 25px">
                <option value="0">否</option>
                <option value="1">是</option>
            </select>
        </div>
    </model>

</div>

<script type="text/javascript" src="bdjs.js"></script>
</body>
</html>
