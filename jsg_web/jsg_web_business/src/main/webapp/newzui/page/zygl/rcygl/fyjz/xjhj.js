//编辑列表框   popWin.cflxList
var cflx = ""; //处方类型
var ylbm = "N040030022001";
var ksbm = "";
var parm = {};
var ypbm="";
var hzlx = "0"; //患者类型 默认门诊 0-门诊,1-住院
var popWin = new Vue({
    el: '#jyxm_icon',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    components: {
        'search-table': searchTable,
        'hzsearch-table': searchTable
    },
    data: {
        hzlx:{
            '1':'住院划价',
        },
        types: '', //分页查询类型
        PfContent: {},
        CfContent: {},
        ysbmList: [], //医生
        ksbmList: [], //科室
        yfList:[],
        title:'',
        popContent : fyjz.popContent,
        adShow:false,
        ypdwList: [], //药品单位
        tclbList: [], //统筹类别
        brfbList: [], //病人费别
        ypList: [], //药品
        //cflxList:[{"cflxbm":"01","cflxmc":"西药处方"},{"cflxbm":"02","cflxmc":"中药处方"},{"cflxbm":"02","cflxmc":"成药处方"}],//处方类型
        cflxList: [], //处方类型
        yfbmList: [],
        jsonList: [],
        xjhjList:[],
        yfContent: {
            hzlx: "1"
        },
        dg: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: ""
        }, //分页信息
        json: {}, //实体参数信息
        readonly: false, //判断控件是否可用
        selSearch: -1,
        //患者
        hzsearchCon: [],
        hztotal: null,
        hzthem: mzthem,
        //药品
        searchCon: [],
        total: 0,
        them: them,
        them_tran: {
            'brxb': dic_transform.data.brxb_tran
        },
        isAddOrUpdate : 0,//0-新增 1-修改
        brfyjsonList:[],
		jzsj: getTodayDateTime(),
		cfhjsj:getTodayDateTime(),
    },
    created:function(){
        Vue.set(this.CfContent,'cfys',fyjz.ysksContent.ysbm);
        Vue.set(this.CfContent,'cfysxm',fyjz.ysksContent.ysxm);
		
		
    },
	
	mounted: function () {
		
	},
    methods: {
		
        openPop:function(){
            brzcList.open();
        },
        //双击药品
        dblclickypmc: function(item) {
            //分页查询
            if(item == null) {
                //分页信息
                popWin.dg.page++;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc' +
                    '&dg=' + JSON.stringify(popWin.dg) + '&json=' + JSON.stringify(popWin.json),
                    function(data) {
                        if(data.a == "0") { //成功
                            if(data.d.list.length != 0) {
                                for(var i = 0; i < data.d.list.length; i++) {
                                    popWin.searchCon.push(data.d.list[i]);
                                }
                            }
                        }
                    });
                return;
            }
            this.PfContent = item;
            this.PfContent.text = this.PfContent.ypmc;
            $(".selectGroup").hide();
            document.getElementById("sl").focus();
        },

        changeDown: function(event, type, content, searchCon) {
            console.log("keyup:changeDown");
            if(type=='ypmc'){
                this.keyCodeFunction(event, content, searchCon);
            }
            if(event.keyCode == 13) {
                if(type == "bah") {
                    document.getElementById('ypmc').focus();
                } else if(type == "ypmc") {
                    this.PfContent.text = this.PfContent.ypmc;
                    document.getElementById('sl').focus();
                } else if(type == "sl") {
                    document.getElementById("ypmc").focus();
                    popWin.addData();
                }
            }
        },
        //值改变时触发
        change: function(event, type, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if(type == "ypmc") { //药品下接框
                this.PfContent["text"] = val;
                popWin.dg= {};
                if(val == undefined || val == null) {
                    Vue.set(popWin.dg,'parm','');
                } else {
                    Vue.set(popWin.dg,'parm',val);
                }
                Vue.set(popWin.dg,'page',1);
                Vue.set(popWin.dg,'rows',5);
                popWin.json = {};
				
                Vue.set(popWin.json,'yfbm',popWin.yfContent.yfbm);
                Vue.set(popWin.json,'cflx',popWin.yfContent.cflx);
                // popWin.json.ksbm = CfContent;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc' +
                    '&dg=' + JSON.stringify(popWin.dg) + '&json=' + JSON.stringify(popWin.json),
                    function(data) {
                        if(data.a == "0") { //成功
                            popWin.searchCon = data.d.list;
                            popWin.total = data.d.total;
                            popWin.selSearch = 0;
                            popWin.them = them;
                            if(data.d.list.length != 0) {
                                $(".selectGroup").hide();
                                _searchEvent.show()
                            } else {
                                $(".selectGroup").hide();
                            }
                        } else {
                            malert("药品检索失败:" + data.c,'top','defeadted');
                        }
                    });
            } else if(type == "bah") { //患者信息下拉框
                popWin.types = "";
                this.CfContent[type] = val;
                if(this.CfContent[type] == undefined) {
                    this.dg.parm = null;
                } else {
                    this.dg.parm = this.CfContent[type];
                }
                if(popWin.yfContent.hzlx == "0") {
                    this.dg.sort = "ghxh";
                    popWin.types = "brgh";
                    popWin.hzthem = mzthem;
                } else {
                    this.dg.sort = "zyh";
                    popWin.hzthem = zythem;
                    popWin.types = "rydj";
                }
                //分页参数
                popWin.dg.page = 1;
                popWin.dg.rows = 5;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=' + popWin.types +
                    '&dg=' + JSON.stringify(popWin.dg) + '&json=' + JSON.stringify(popWin.json),
                    function(data) {
                        if(data.a == "0") { //成功
                            popWin.hzsearchCon = data.d.list;
                            popWin.hztotal = data.d.total;
                            popWin.selSearch = 0;
                            if(data.d.list.length > 0) {
                                if(popWin.yfContent.hzlx == "0") {
                                    //处理列名不一致问题
                                    for(var int = 0; int < popWin.hzsearchCon.length; int++) {
                                        popWin.hzsearchCon[int].bah = popWin.hzsearchCon[int].ghxh;
                                        popWin.hzsearchCon[int].brks = popWin.hzsearchCon[int].ghks;
                                        popWin.hzsearchCon[int].brksmc = popWin.hzsearchCon[int].ghksmc;
                                        popWin.hzsearchCon[int].cfys = popWin.hzsearchCon[int].jzys;
                                        popWin.hzsearchCon[int].cfysxm = popWin.hzsearchCon[int].jzysxm;
                                    }
                                } else {
                                    for(var int = 0; int < popWin.hzsearchCon.length; int++) {
                                        popWin.hzsearchCon[int].bah = popWin.hzsearchCon[int].zyh;
                                        popWin.hzsearchCon[int].brks = popWin.hzsearchCon[int].ryks;
                                        popWin.hzsearchCon[int].brksmc = popWin.hzsearchCon[int].ryksmc;
                                        popWin.hzsearchCon[int].cfys = popWin.hzsearchCon[int].zyys;
                                        popWin.hzsearchCon[int].cfysxm = popWin.hzsearchCon[int].zyysxm;
                                        popWin.hzsearchCon[int].brnl = popWin.hzsearchCon[int].nl;
                                    }
                                }
                                if(data.d.list.length != 0) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show()
                                } else {
                                    $(".selectGroup").hide();
                                }
                            } else {
                                malert("未找到匹配的患者",'top','defeadted');
                            }
                        } else {
                            malert("患者信息检索失败:" + data.c,'top','defeadted');
                        }
                    });
            }
        },


        //下拉框加载科室
        ksbmselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify({
                page: 1,
                rows: 100,
                sort: 'ksbm',
                order: 'asc'
            }), function(json) {
                popWin.ksbmList = json.d.list;
            });
        },
        //下拉框加载人员
        ysbmselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" + JSON.stringify({
                page: 1,
                rows: 10000,
                sort: 'rybm',
                order: 'asc'
            }), function(json) {
                popWin.ysbmList = json.d.list;
            });
        },
        //下拉框加载药品单位
        ypdwselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ypdw&dg=" + JSON.stringify({
                page: 1,
                rows: 100,
                sort: 'jldwbm',
                order: 'asc'
            }), function(json) {
                popWin.ypdwList = json.d.list;
                Vue.set(popWin.PfContent,'yfdw',popWin.ypdwList[0].jldwbm);
            });
        },
        //下拉框加载统筹类别
        tclbselect: function() {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yptclb&dg=" + JSON.stringify({
                page: 1,
                rows: 20,
                sort: 'tclbbm',
                order: 'asc'
            }), function(json) {
                popWin.tclbList = json.d.list;
                Vue.set(popWin.PfContent,'ybtclb',popWin.tclbList[0].tclbbm);
            });
        },
        //下拉框加载处方类型
        cflxselect: function() {
            var cflx_dg = {
                page: 1,
                rows: 100,
                sort: "cflxbm",
                order: "asc",
                parm: ""
            };
            $.getJSON('/actionDispatcher.do?reqUrl=XtwhYlfwxmCflx&types=query&dg=' + JSON.stringify(cflx_dg), function(data) {
                if(data.a == 0) {
                    popWin.cflxList = data.d.list;
                } else {
                    malert(data.c,'top','defeadted');
                }
            });
        },
        //下拉框病人费别
        brfbselect: function() {
            var brfb_dg = {
                page: 1,
                rows: 100,
                sort: "fbbm",
                order: "asc",
                parm: ""
            };
            $.getJSON('/actionDispatcher.do?reqUrl=XtwhYlfwxmBrfb&types=query&dg=' + JSON.stringify(brfb_dg), function(data) {
                if(data.a == 0) {
                    popWin.brfbList = data.d.list;
                    console.log(popWin.brfbList);
                    //默认第一个
                    Vue.set(popWin.CfContent,'fbbm',popWin.brfbList[0].fbbm);
                } else {
                    malert(data.c,'top','defeadted');
                }
            });
        },
        //下拉药房选择处方类型
        yfcflx:function(){
            popWin.cflxList = [];
            var brfb_dg = {
                page: 1,
                rows: 500,
                sort: "fbbm",
                order: "asc",
            };
            $.getJSON('/actionDispatcher.do?reqUrl=XtwhYlfwxmYfyycflx&types=query&dg=' + JSON.stringify(brfb_dg)+"&json={'yfbm':"+"'"+popWin.yfContent.yfbm+"'}", function(data) {
                if(data.a == 0) {
                    popWin.cflxList = data.d.list;
                    Vue.set(popWin.yfContent,'cflx',popWin.cflxList[0].cflxbm);
                } else {
                    malert(data.c,'top','defeadted');
                    return
                }
            });
        },

        //获取药房
        getYfbm: function() {
            parm = {
                "ylbm": ylbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm), function(json) {
                if(json.a == 0) {
                    popWin.yfList = json.d.list;
                    if(popWin.yfList.length > 0) {
                        Vue.set(popWin.yfContent, 'yfbm', popWin.yfList[0].yfbm);
                        ksbm = popWin.yfList[0].ksbm;
                        popWin.yfcflx();
                        /*popWin.getCsqx();*/
                    }
                } else {
                    //alert("药房编码获取失败：" + json.c);
                    malert("药房获取失败！",'top','defeadted');
                }
            });
        },
        //药房选择
        Wf_YfChange: function(val) {
            this.jsonList=[]
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            popWin.yfcflx();

        },
        //下拉框加载药品单位
        // ypdwselect: function() {
        //     $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ypdw&dg=" + JSON.stringify({
        //         page: 1,
        //         rows: 100,
        //         sort: 'jldwbm',
        //         order: 'asc'
        //     }), function(json) {
        //         popWin.ypdwList = json.d.list;
        //     });
        // },
        //保存处方方信息
        saveData: function() {
            $.ajaxSettings.async = false;
            //住院号
            Vue.set(popWin.CfContent,'bah',popWin.popContent.zyh);
            //病人姓名
            Vue.set(popWin.CfContent,'brxm',popWin.popContent.brxm);
            //入院科室
            Vue.set(popWin.CfContent,'brks',popWin.popContent.ryks);
            //入院科室名称
            Vue.set(popWin.CfContent,'brksmc',popWin.popContent.ryksmc);
            //病人诊断
            Vue.set(popWin.CfContent,'lczd',popWin.popContent.ryzdmc);
            //住院医生
            Vue.set(popWin.CfContent,'zyys',popWin.popContent.zyys);
            //住院医生姓名
            Vue.set(popWin.CfContent,'zyysxm',popWin.popContent.zyysxm);
            //处方医生
            // var ysxm = $("#cfys").find("option:selected").text();
            // Vue.set(popWin.CfContent,'cfysxm',ysxm);

            var xm = popWin.CfContent.brxm;

            if(!popWin.CfContent.brxm) {
                malert("请输入或选择病人!",'top','defeadted')
                return;
            }

            if(!popWin.yfContent.cflx){
                malert("请选择处方类型",'top','defeadted')
            }
            var kfbz = popWin.CfContent.kfbz;
            if(kfbz == "1") {
                malert("已扣费处方不允许再次修改保存!",'top','defeadted')
                return;
            }
            var fybz = popWin.yfContent.fybz;
            if(fybz == "1") {
                malert("已发药处方不允许再次修改保存!",'top','defeadted')
                return;
            }
            popWin.CfContent["brlx"] = popWin.yfContent.hzlx;
            popWin.CfContent["pybz"] = popWin.yfContent["pybz"];
            popWin.CfContent["cflx"] = popWin.yfContent["cflx"];
            popWin.CfContent["yfbm"] = popWin.yfContent.yfbm;
            //获取处方类型名称
            popWin.CfContent['cflxmc'] = popWin.listGetName(popWin.cflxList, popWin.CfContent["cflx"], 'cflxbm', 'cflxmc');
            var cf = JSON.stringify(popWin.CfContent); //处方
            var pf = JSON.stringify(popWin.jsonList); //配方
            var json = '{"list" : [{"cf": ' + cf + ',"pf": ' + pf + '}]}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCfhj&types=save', json).then(function(data) {
                    if(data.body.a == 0) {
                        var cfzyh=popWin.CfContent.bah;
                        var zyys=fyjz.ysksContent.ysbm;
                        var zyysxm=fyjz.ysksContent.ysxm;
                        var zyks=fyjz.ysksContent.zyks;
                        var zyksmc=fyjz.ysksContent.zyksmc;
                        setTimeout(function () {
                        	popWin.getWsfcf(cfzyh,zyys,zyysxm,zyks,zyksmc);
                        	popWin.saveHjCfFyData();
						     }, 400);
                        malert(data.body.c,'top','success');
                        popWin.jsonList=[];

                        if(data.body.d){
                            //处方号
                            // lr.$set(lr.brfyContent,'yzhm',data.body.d);
                            // lr.$forceUpdate();
                        }
                        popWin.PfContent = {};
                        // lr.brfyContent={}; //注释的原因是：这里需要把处方号在lr页面显示出来！不能清空这个对象！此对象保存了lr的所有信息
                        //document.getElementById("bah").focus();
                        guanbi();
                    } else {
						popWin.jsonList=[];
                        malert(data.body.c,'top','defeadted');
                    }
                }, function(error) {
                    console.log(error);
                });
        },
   	 saveHjCfFyData: function () {
                     if (popWin.brfyjsonList.length == 0) {
             return;
         }
         //婴儿编号
         if(fyjz.yebh && fyjz.yebh != '000'){
             popWin.brfyjsonList.forEach(item => {
                 item.yebh = fyjz.yebh;
				 if(!item.zyksmc){
					item.zyks = popWin.popContent.ryks;
					item.zyksmc = popWin.popContent.ryksmc; 
				 }
				 
             });
         }else{
             popWin.brfyjsonList.forEach(item => {
                 item.yebh = "";
				 if(!item.zyksmc){
				 	item.zyks = popWin.popContent.ryks;
				 	item.zyksmc = popWin.popContent.ryksmc; 
				 }
             })
         }
         var json = '{"list":' + JSON.stringify(popWin.brfyjsonList) + '}';
         this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglFyglBrfy&types=save', json).then(function (data) {
             if (data.body.a == 0) {
                popWin.savecffy(popWin.brfyjsonList[0].yzhm,popWin.brfyjsonList[0].zyks);
                malert(data.body.c,'top');
             } else {
				 popWin.brfyjsonList = [];
                 malert(data.body.c,'top','defeadted');
             }
         }, function (error) {
			 popWin.brfyjsonList = [];
             console.log(error);
         });
     },
    //发药
    savecffy:function(cfh,ksbm){
        var obj = JSON.stringify({
            "cfh": cfh
        });
        this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=cffy&ksbm=' + ksbm, obj)
            .then(function (data) {
                console.log("发药成功");
            }, function (error) {
                console.log(error);
            });
    },
    //获取未收费处方（针对住院）
     getWsfcf:function(zyh,zyys,zyysxm,zyks,zyksmc){
         if (!zyh) {
             malert('住院号不能为空','top','defeadted');
             return;
         }
         this.param.rows = 2000;
         this.param.zyh = zyh;
         this.param.zyks = zyks;
         this.param.zyksmc = zyksmc;
         this.param.zyys = zyys;
         this.param.zyysxm = zyysxm;
         this.param.brid = fyjz.popContent.brid;
         $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglFyglBrfy&types=queryWxfy&parm=" + JSON.stringify(this.param), function (json) {
             if (json.a == 0) {
            	 popWin.brfyjsonList = json.d;
             	for(var i=0;i<popWin.brfyjsonList.length;i++){
             		popWin.brfyjsonList[i].brid=fyjz.popContent.brid;
             		popWin.brfyjsonList[i].yfbm=popWin.yfContent["yfbm"];
             		popWin.brfyjsonList[i].zyks=zyks;
             		popWin.brfyjsonList[i].zyksmc=zyksmc;
             		popWin.brfyjsonList[i].zyys=zyys;
             		popWin.brfyjsonList[i].zyysxm=zyysxm;
                    popWin.brfyjsonList[i].sfrq = fyjz.brfyContent.sfrq;
					}
             } else {
                 malert(json.c,'top','defeadted');
             }
         });
     	},
        //添加一行
        addData: function() {
			            if(popWin.CfContent.kfbz == "1") {
                malert("已扣费处方,不允许添加配方!",'top','defeadted')
                return;
            }
            if(!popWin.PfContent.ypbm) {
                malert("请选择药品!",'top','defeadted')
                return;
            }
            //判断当前用是不是不能大于库存量
            var cfyl = popWin.PfContent.cfyl;
            var sjkc = popWin.PfContent.sjkc;
            var kcsl = popWin.PfContent.kcsl;
            var wfy = popWin.PfContent.wfy;
            if(wfy >= kcsl) {
                malert("库存不足，请检查未发药库存",'top','defeadted');
                return;
            }
            if(!cfyl) {
                malert("处方用量不能为空",'top','defeadted');
                return;
            }
            if(cfyl <= 0) {
                malert("处方用量不能小于等于零",'top','defeadted');
                return;
            }
            if(cfyl > sjkc) {
                popWin.PfContent.cfyl = sjkc;
                malert("当前使用量不能大于库存量!",'top','defeadted')
                return;
            }
            //判断是否有药品存在
            for(var i = 0; i < popWin.jsonList.length; i++) {
                if(popWin.jsonList[i].ypbm == ypbm) {
                    malert("药品【" + popWin.PfContent.ypmc + "】已存在,请修改已有数据!",'top','defeadted');
                    return;
                }
            }
            var json = {
                "ypbm": popWin.PfContent.ypbm,
                "yfbm": popWin.yfContent.yfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkcone' + '&json=' + JSON.stringify(json),
                function(data) {
                    if(data.a == "0") { //成功
                        if(data.d.list.length > 0) {
                            for(var int = 0; int < data.d.list.length; int++) {
                                if(cfyl <= data.d.list[int].sjkc) { //批次够用
                                    data.d.list[int].cfyl = cfyl
                                    popWin.jsonList.push(data.d.list[int]);
                                    break;
                                } else { //批次不够用
                                    data.d.list[int].cfyl = data.d.list[int].sjkc
                                    popWin.jsonList.push(data.d.list[int]);
                                    cfyl = cfyl - data.d.list[int].sjkc;
                                }
                            }
                            //数量回车后，光标回到药品名称
                            document.getElementById("ypmc").focus();
                        } else {
                            malert("无药品批次明细",'top','defeadted');
                        }
                    }else {
                        malert("药品批次检索失败:" + data.c,'top','defeadted');
                    }
                });
            popWin.PfContent = {};
            document.getElementById("ypmc").focus();
        },
        //作废处方信息
        delData: function() {

            if(popWin.CfContent.cfh == null || popWin.CfContent.cfh == undefined) {
                malert('请选择处方','top','defeadted');
                return;
            }

            var obj = {
                'cfh': popWin.CfContent.cfh
            };

            this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywCftyzf&types=cfzf', JSON.stringify(obj))
                .then(function(data) {
                    if(data.body.a == 0) {
                        this.getData();
                        this.isChecked = [];
                        malert("处方作废保存成功",'top','success');
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                }, function(error) {
                    console.log(error);
                });
        },
        // //配方列表删除一行
        delLine: function(num) {
            this.jsonList.splice(num, 1);
        },
        //双击进行修改
        edit: function(num) {
            popWin.title='编辑药品';
            if(num == null) {
                for(var i = 0; i < this.isChecked.length; i++) {
                    if(this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if(num == null) {
                    malert("请选中你要修改的数据",'top','defeadted');
                    return false;
                }
            }
            //这里要拷贝值到popContent中，不能直接=
            popWin.PfContent = this.jsonList[num];
            popWin.CfContent["pybz"] = popWin.yfContent["pybz"];
            popWin.CfContent["cflx"] = popWin.yfContent["cflx"];
            popWin.PfContent.text = popWin.PfContent.ypmc;
            if(popWin.CfContent.kfbz == "1" || popWin.CfContent.fybz == "1") {
                popWin.readonly = true;
                malert("已扣费处方不允许修改配方!",'top','defeadted')
            } else {
                popWin.readonly = false;
            }
        },
        remove: function() {
            var cfhList = [];
            for(var i = 0; i < this.isChecked.length; i++) {
                if(this.isChecked[i] == true) {
                    var cf = {};
                    cf.cfh = this.jsonList[i].cfh
                    cfhList.push(cf);
                }
            }
            if(cfhList.length == 0) {
                malert("请选中您要删除的数据",'top','defeadted');
                return false;
            }
            var json = '{"list":' + JSON.stringify(cfhList) + '}'
            this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywCfhj&types=deletecf&',
                json).then(function(data) {
                tableInfo.getData();
                if(data.body.a == 0) {
                    malert("删除成功",'top','success')
                } else {
                    malert("删除失败",'top','defeadted')
                }
            }, function(error) {
                console.log(error);
            });
        },
        searchclose: function() {
            $(".selectGroup").hide();
        },
        getList:function () {
            var url=window.location.href;
        },
    }
});

var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 0,
        scrollLeft: 0,
        sfcy: '',
        scollType: true,
        total: null,
        cfShow: false,//处方调用
        activeIndex1: undefined,
        hoverIndex1: undefined,
        bcTitle: '',
        ypShow: false,//药品选择
        tjShow: false,//添加附加费用
        wShow: false,//侧滑宽度
        hzShow: false,
        zdShow: false,//诊断处方
        lsShow: false,//历史处方
        fjfjs: "", //附加费用检索
        FjfyJson: [], //附加费用集合
        lsCfList: [], //历史处方处方集合
        lsCfMxList: [],  //历史处方处方明细集合
        lscfjs: "", //历史处方检索
        cfList: [], //处方模板处方集合
        cfMxList: [],//处方模板处方明细集合
        cfdyjs: "",  //处方调用检索
        YPJson: [], //药品集合
        jsypxx: "", //检索药品信息
        dg: {page: 1, rows: 20, sort: "", order: "asc", parm: ""},//分页信息
        selSearch: -1,
        nums: 1,
        lczdJsonList: [], //临床诊断集合
        lczdValue: "", //临床诊断检索内容
        cfmbIndex: '',//选中的处方模板index
        lscfIndex: '',//选中的历史处方index
        beginrq: '',
        endrq: '',
        cfmbsfcy: 0, //针对处方模板判断是否中草药用的
        lscfcxqx: '1', //历史处方判断查询当前病人处方还是当前医生所有处方
        lscfTmp: {},
        userInfo:{},
		jzlx_tran:{
			'1':'个人',
			'2':'科室'
		},
		jzlx:1,
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        this.getUserInfo();
        //初始化检索日期！为今天0点到今天24点

    },
    methods: {
		resultChangelx: function (val) {
			this.jzlx = val[0];
			this.getTemData();
		 },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    brzcList.userInfo = json.body.d;
                });
        },
        //关闭
        closes: function () {
            this.nums = 1;
            this.isChecked = [];
            this.isCheckAll = false;
            this.activeIndex=undefined;
            this.activeIndex1=undefined;
            this.hoverIndex1=undefined;
            this.hoverIndex=undefined;

        },
        open: function () {
            this.nums = 0;
            this.getTemData();
        },
        //*******************************处方模板调用开始***********************************
        getTemData: function () {
            brzcList.cfList = [];
            brzcList.cfMxList = [];
            var parm = {
                rows: 100,
                page: this.param.page,
                parm: this.cfdyjs,
				yfbm:popWin.yfContent.yfbm
            };
			if(this.jzlx =='1'){
				parm.bah = popWin.popContent.zyh
			}else{
				parm.brks = popWin.popContent.ryks
			}
			
			
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryZycf&parm=" + JSON.stringify(parm), function (json) {
                if (json.d.list.length != 0) {
                    brzcList.cfList = brzcList.cfList.concat(json.d.list);
                    brzcList.total = json.d.total;
                }
            });
        },
        scrollGata(event) {
            if (event.srcElement.scrollHeight - event.srcElement.scrollTop === event.srcElement.clientHeight) {
                if (event.target.scrollLeft < 0 || this.scrollLeft == event.target.scrollLeft) {
                    if (event.target.scrollTop > this.scrollTop) {
                        if (this.scollType) {
                            this.scollType = false
                            if (this.cfList.length < this.total) {
                                if (this.uilPageBottom() == true) {
                                    this.param.page = this.param.page + 1;
                                    this.getTemData();
                                }
                            } else {
                                // this.loadData='暂无更多数据...'
                            }
                        }
                    }
                }
            }
            this.scrollLeft = event.target.scrollLeft;
            this.scrollTop = event.target.scrollTop
        },
        //根据处方模板参数查询明细
        getTemMxData: function (index) {
            this.cfmbIndex = index;
            brzcList.isChecked = []
            brzcList.isCheckAll = false
            this.activeIndex1 = index
						var cfh = this.cfList[index].cfh;
			            var yfbm = this.cfList[index].yfbm;
			            var cflx = this.cfList[index].cflx;
			            var json = {
			                cfh: cfh,
			                yfbm: yfbm,
			                cflx: cflx
			            };
			
			            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=lscfKc&json=' + JSON.stringify(json), function (data) {
			                brzcList.cfMxList = [];
			                if (data.d != null) {
			                    brzcList.cfMxList = data.d.list;
			                }
			            });
			
            var json = {
                zhyzbm: zhyzbm,
                yfbm: yfbm,
                cflxbm: cflxbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=zhyzYp&dg&json=' + JSON.stringify(json), function (data) {
                brzcList.cfMxList = [];
                if (data.d != null) {
                    brzcList.cfMxList = data.d.list;
                }
            });
        },
        // 双击处方模板添加药品
        addCfMb: function (index) {
            //全选
            this.sfcy = this.cfList[index].sfcy;
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.cfMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.save(index);
        },


        //保存
        save: function ( index) {
            var pfxx = [];
            //进行数据的判断
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    if (brzcList.cfMxList[i].kcsl <= 0) {
                        malert("选中药品中【" + brzcList.cfMxList[i].ypmc + "】库存告急", 'top', 'defeadted');
                        continue;
                    } else {
                        var jbjl = brzcList.cfMxList[i].jbjl;  //药品基本剂量
                        var yyjl = brzcList.cfMxList[i].yyjl;  //用药剂量
                        if (!jbjl || jbjl < 0) {
                            jbjl = 1;
                        }
                        if (!yyjl || yyjl < 0) {
                            yyjl = 1;
                        }
						brzcList.cfMxList[i].sjkc = brzcList.cfMxList[i].kcsl
                        var cfyl = brzcList.cfMxList[i].cfyl;
                        brzcList.cfMxList[i].fysl = cfyl;
                        popWin.PfContent = brzcList.cfMxList[i];
                        popWin.addData();
                    }
                }
            }
            this.closes();
            return;
        },
    }
});

//下拉框
popWin.getList();
popWin.getYfbm();
popWin.ksbmselect(); //检索科室下拉框
popWin.ysbmselect(); //检索医生下拉框
popWin.tclbselect(); //检索统筹类别下拉框
popWin.cflxselect(); //处方类型
popWin.brfbselect(); //病人费别下拉框
var them = {
    '统筹类别': 'ybtclbmc',
    '农保类别': 'nbtclbmc',
    '药品名称': 'ypmc',
    '药品规格': 'ypgg',
    '库存数量': 'kcsl',
    '实际库存': 'sjkc',
    '单位': 'yfdw',
    '均价': 'yplj',
    '分装比例': 'fzbl',
    '剂型': 'jxmc',
    '拼音代码 ': 'pydm',
    '化学名称': 'hxmc',
    '化学名代码': 'hxmcdm'
};
var mzthem = {
    '姓名': 'brxm',
    '挂号序号': 'bah',
    '性别': 'brxb',
    '年龄': 'brnl',
    '就诊科室': 'brksmc',
    '接诊医生': 'cfysxm'
};
var zythem = {
    '姓名': 'brxm',
    '住院号': 'bah',
    '性别': 'brxb',
    '年龄': 'brnl',
    '住院科室': 'brksmc',
    '住院医生': 'cfysxm'
};

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }

});

function guanbi(){
    $("#loadingPage-cfhj").empty();
}
