<div id="yndryb_008">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary" @click="load()">读卡</button>
            <button class="tong-btn btn-parmary-b" @click="enter()">引入</button>

            <button class="tong-btn btn-parmary" style="margin-left:200px;" @click="loadSFZ()">无卡查询</button>
            <input type="text" class="zui-input wh200 align-right" v-model="cardParm"/>


<!--            <input name="" type="button" style="margin-right:10px;" />-->
<!--            <input name="" type="button" />-->

        </div>
        <ul class="tab-edit-list flex-start">

            <li class="jslb">
                <i>医疗类别</i>
                <select-input @change-data="resultChange"
                              :child="yndryb_jslb_tran" :val="grxxJson.yllb"
                              :search="true" :name="'grxxJson.yllb'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>身份证号码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.sfzh"/>
            </li>
            <li>
                <i>个人编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.grbh" disabled="disabled"/>
            </li>
            <li>
                <i>姓名</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.xm" disabled="disabled"/>
            </li>
            <li>
                <i>性别</i>
                <input type="text" class="zui-input  background-h" v-model="yndryb_sex_tran[grxxJson.xb]" disabled="disabled"/>
            </li>

            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csrq" disabled="disabled"/>
            </li>
            <li>
                <i>人员类别编码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.rylbbm" disabled="disabled"/>
            </li>
            <li>
                <i>人员类别名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.rylbmc" disabled="disabled"/>
            </li>
            <li>
                <i>单位编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.dwbh" disabled="disabled"/>
            </li>
            <li>
                <i>单位名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.dwmc" disabled="disabled"/>
            </li>
            <li>
                <i>统筹区号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.tcqh" disabled="disabled"/>
            </li>
            <li>
                <i>区域编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.qybh" disabled="disabled"/>
            </li>
            <li>
                <i>居民职工标志</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.jmzgbz" disabled="disabled"/>
            </li>
<!--            <li>-->
<!--                <i>参数名1</i>-->
<!--                <input type="text" class="zui-input  background-h" v-model="grxxJson.csm1" disabled="disabled"/>-->
<!--            </li>-->
            <li>
                <i>{{grxxJson.csm1}}</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csz1" disabled="disabled"/>
            </li>
<!--            <li>-->
<!--                <i>参数名2</i>-->
<!--                <input type="text" class="zui-input  background-h" v-model="grxxJson.csm2" disabled="disabled"/>-->
<!--            </li>-->
            <li>
                <i>{{grxxJson.csm2}}</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csz2" disabled="disabled"/>
            </li>
<!--            <li>-->
<!--                <i>参数名3</i>-->
<!--                <input type="text" class="zui-input  background-h" v-model="grxxJson.csm3" disabled="disabled"/>-->
<!--            </li>-->
            <li>
                <i>{{grxxJson.csm3}}</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csz3" disabled="disabled"/>
            </li>

<!--            <li>-->
<!--                <i>参数名4</i>-->
<!--                <input type="text" class="zui-input  background-h" v-model="grxxJson.csm4" disabled="disabled"/>-->
<!--            </li>-->
            <li>
                <i>{{grxxJson.csm4}}</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csz4" disabled="disabled"/>
            </li>
<!--            <li>-->
<!--                <i>参数名5</i>-->
<!--                <input type="text" class="zui-input  background-h" v-model="grxxJson.csm5" disabled="disabled"/>-->
<!--            </li>-->
            <li>
                <i>{{grxxJson.csm5}}</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csz5" disabled="disabled"/>
            </li>
<!--            <li>-->
<!--                <i>参数名6</i>-->
<!--                <input type="text" class="zui-input  background-h" v-model="grxxJson.csm6" disabled="disabled"/>-->
<!--            </li>-->
            <li>
                <i>{{grxxJson.csm6}}</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.csz6" disabled="disabled"/>
            </li>

            <li>
                <i>医保卡号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ybkh" disabled="disabled"/>
            </li>

            <li>
                <i>账户余额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.zhye" disabled="disabled"/>
            </li>

            <li>
                <i>是否封锁</i>
                <input type="text" class="zui-input  background-h" v-model="yndryb_fs_tran[grxxJson.sffs]" disabled="disabled"/>
            </li>

            <li>
                <i>封锁原因</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.fsyy" disabled="disabled"/>
            </li>

            <li>

            </li>

            <li style="width: 100%">
                <i>统筹累计已经使用</i>
                <input class="zui-input wh800" type="text" v-model="grxxJson.tcljsy" disabled="disabled"/>
            </li>
        </ul>


    </div>

</div>
<script type="application/javascript" src="insurancePort/008yndryb/008yndryb.js"></script>