/**
 * Created by mash on 2017/10/2.
 */
(function () {
    var bq = new Vue({                                      // 注意：具体细节可参考科室维护的页面
        el: '#bq',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            text: null,                                     // 要检索的文本
            jsonList: [{},{},{},{},{},{}],                  // 分院数据List
            page: {                                         // 页码
                page: 1,
                rows: 20,
                total: null
            },
            param:{},
            id:null
        },
        methods: {
            // 获取分院List
            getData: function () {
            	$.getJSON(
						"/actionDispatcher.do?reqUrl=XtwhKsryBq&types=query&json="
								+ JSON.stringify(this.param), function(
								json) {
									bq.totlePage = Math
									.ceil(json.d.total/ bq.param.rows);
									bq.jsonList = json.d.list;
						});
            },
            // 新增病区信息
            addData: function () {
            	$.getJSON(
						"/actionDispatcher.do?reqUrl=InterfaceDzbl&types=BQ&method=DSEMR_BQXX_ADD&id="+this.id+"&json="
            			+ JSON.stringify(this.param), function(
								json) {
									if (json.a == "0") {
										malert("新增成功")
									} else {
										malert("新增失败")
									}
						});
            },
            // 编辑病区信息
            edit: function (num) {
            	 if (num == null) {
                     for (var i = 0; i < this.isChecked.length; i++) {
                         if (this.isChecked[i] == true) {
                             num = i;
                             this.id=this.jsonList[i].bqbm;
                             break;
                         }
                     }
                     if (num == null) {
                         malert("请选中你要修改的数据");
                         return false;
                     }
                  
                 }
                 $.getJSON(
 						"/actionDispatcher.do?reqUrl=InterfaceDzbl&types=BQ&method=DSEMR_BQXX_UPDATE&id="+this.id+"&json="
             			+ JSON.stringify(this.param), function(
 								json) {
 									if (json.a == "0") {
 										malert("修改成功")
 									} else {
 										malert("修改失败")
 									}
 						});
            },
            // 删除病区信息
            remove: function (index) {
            	var yqList = [];
				for (var i = 0; i < this.isChecked.length; i++) {
					if (this.isChecked[i] == true) {
						var yq = {};
						yq.bqbm = this.jsonList[i].bqbm;
						yqList.push(yq);
						this.id=this.jsonList[i].bqbm;
					}
				}
				if (yqList.length == 0) {
					malert("请选中您要删除的数据");
					return false;
				}
				if(yqList.length>1){
					malert("只能选择一条数据进行删除");
					return false;
				}
				 $.getJSON(
							"/actionDispatcher.do?reqUrl=InterfaceDzbl&types=BQ&method=DSEMR_BQXX_DEL&id="+this.id+"&json="
            			+ JSON.stringify(this.param), function(
									json) {
										if (json.a == "0") {
											malert("删除成功")
										} else {
											malert("删除失败")
										}
							});
            }
        }
    });

    bq.getData();

})();