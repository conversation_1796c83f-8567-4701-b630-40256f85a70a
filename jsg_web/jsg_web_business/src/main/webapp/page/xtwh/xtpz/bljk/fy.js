/**
 * Created by mash on 2017/10/2.
 */
(function () {
    var fy = new Vue({                                      // 注意：具体细节可参考科室维护的页面
        el: '#fy',
        mixins: [dic_transform, baseFunc, tableBase],
        data: {
            text: null,                                     // 要检索的文本
            jsonList: [{},{},{},{},{},{}],                  // 分院数据List
            page: {                                         // 页码
                page: 1,
                rows: 20,
                total: null
            },
            param:{},
            yqbm:null
        },
        methods: {
            // 获取分院List
            getData: function () {
            
            	$.getJSON(
						"/actionDispatcher.do?reqUrl=XtwhKsryYq&types=query&json="
								+ JSON.stringify(this.param), function(
								json) {
									fy.totlePage = Math.ceil(json.d.total/ fy.param.rows);
									fy.jsonList = json.d.list;
						});
            },
            // 新增分院信息
            addData: function () {

            	$.getJSON(
						"/actionDispatcher.do?reqUrl=InterfaceDzbl&types=YQ&method=DSEMR_YQXX_ADD&id="+this.yqbm+"&json="
            			+ JSON.stringify(this.param), function(
								json) {
									if (json.a == "0") {
										malert("新增成功")
									} else {
										malert("新增失败")
									}
			 });

            },
            // 编辑分院信息
            edit: function (num) {
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            this.yqbm=this.jsonList[i].yqbm;
                            break;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要修改的数据");
                        return false;
                    }
                 
                }
                $.getJSON(
						"/actionDispatcher.do?reqUrl=InterfaceDzbl&types=YQ&method=DSEMR_YQXX_UPDATE&id="+this.yqbm+"&json="
            			+ JSON.stringify(this.param), function(
								json) {
									if (json.a == "0") {
										malert("修改成功")
									} else {
										malert("修改失败")
									}
						});

            },
            // 删除分院信息
            remove: function (index) {
            	var yqList = [];
				for (var i = 0; i < this.isChecked.length; i++) {
					if (this.isChecked[i] == true) {
						var yq = {};
						yq.yqbm = this.jsonList[i].yqbm;
						yqList.push(yq);
						this.yqbm=this.jsonList[i].yqbm;
					}
				}
				if (yqList.length == 0) {
					malert("请选中您要删除的数据");
					return false;
				}
				if(yqList.length>1){
					malert("只能选择一条数据进行删除");
					return false;
				}
				 $.getJSON(
							"/actionDispatcher.do?reqUrl=InterfaceDzbl&types=YQ&method=DSEMR_YQXX_DEL&id="+this.yqbm+"&json="
            			+ JSON.stringify(this.param), function(
									json) {
										if (json.a == "0") {
											malert("删除成功")
										} else {
											malert("删除失败")
										}
							});

            }
        }
    });
    fy.getData();


})();