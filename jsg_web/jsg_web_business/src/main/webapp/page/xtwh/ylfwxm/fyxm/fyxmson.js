//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
//明细费用项目
var tableInfo = new Vue({
	el: '#fyxmson',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	data: {
		param: {
			'page': 1,

		},
		pageParam: {
			'page': '1',
			'rows': '20000'
		},
		jsonList: []
	},
	methods: {
		//进入页面加载列表信息
		getData: function() {
			if($("#fyxmjsvalue").val() != null && $("#fyxmjsvalue").val() != '') {
				this.param.parm = $("#fyxmjsvalue").val();
			} else {
				this.param.parm = '';
			}
			$.getJSON("/actionDispatcher.do?reqUrl=XtwhYlfwxmMxfyxm&types=query&dg=" + JSON.stringify(this.param), function(json) {
				tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
				tableInfo.jsonList = json.d.list;
				popWin.popContent['yhbl'] = 0;
			});

		},

		//检索查询回车键  
		searchHc: function() {
			if(window.event.keyCode == 13) {
				this.getData();
			}
		},

		//下拉框加载费用类别
		fylbSelect: function() {
			$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=fybm&dg=" + JSON.stringify(this.pageParam), function(json) {
				popWin.fylbList = json.d.list;
			});
		},
		//费用统筹类别
		fytclbSelect: function() {
			$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ybtclb&dg=" + JSON.stringify(this.pageParam), function(json) {
				popWin.fytclbList = json.d.list;
			});
		},
		//核算科室
		hsksSelect: function() {
			$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(this.pageParam), function(json) {
				popWin.hsksList = json.d.list;
			});
		},

		//弹出层展示
		addData: function() {
			this.fylbSelect(), //费用类别
				this.fytclbSelect(), //统筹类别
				this.hsksSelect(), //核算科室
				popWin.popContent = {
					'fpfs': "1",
					'fydj': "0",
					'fylx': "0",
					'fytclb': "01",
					'nbtclb': "1",
					'sfgd': "0",
					'sfzy': "0",
					'tybz': "0",
					'ypfy': "0",
				};
			popWin.isShow = true;
		},
		//修改
		edit: function(num) {
			if(num == null) {
				for(var i = 0; i < this.isChecked.length; i++) {
					if(this.isChecked[i] == true) {
						num = i;
						break;
					}
				}
				if(num == null) {
					malert("请选中你要修改的数据");
					return false;
				}
			}
			popWin.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
			popWin.isShow = true;
		},
		//删除
		remove: function() {
			var fyxmList = [];
			for(var i = 0; i < this.isChecked.length; i++) {
				if(this.isChecked[i] == true) {
					var fyxm = {};
					fyxm.mxfybm = this.jsonList[i].mxfybm;
					fyxmList.push(fyxm);
				}
			}
			if(fyxmList.length == 0) {
				malert("请选中您要删除的数据");
				return false;
			}
			if(!confirm("请确认是否删除")) {
				return false;
			}

			var json = '{"list":' + JSON.stringify(fyxmList) + '}';
			this.$http.post('/actionDispatcher.do?reqUrl=XtwhYlfwxmMxfyxm&types=delete&',
				json).then(function(data) {
				tableInfo.getData();
				if(data.body.a == 0) {
					malert("删除成功")
				} else {
					malert("删除失败")
				}
			}, function(error) {
				console.log(error);
			});
		}
	}
});
tableInfo.getData();
tableInfo.fylbSelect();
tableInfo.fytclbSelect();
tableInfo.hsksSelect();
/************弹出层*******************/
var popWin = new Vue({
	el: '#fyxmsonpop',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	data: {
		isShow: false,
		fylbList: [], //费用类别
		fytclbList: [], //费用统筹类别
		hsksList: [], //核算科室
		popContent: {},
		title: '费用项目',
		ifClick: true
	},
	methods: {
		//保存
		saveData: function() {
			if(!popWin.ifClick) return //如果为false表示已经点击了不能再点
			popWin.ifClick = false;
			//必要判断
			if(this.popContent['mxfymc'] == undefined || this.popContent['mxfymc'] == null || this.popContent['mxfymc'] == '') {
				malert("明细费用名称不能为空");
				popWin.ifClick = true;
				return false;
			}
			if(this.popContent.lbbm == undefined || this.popContent.lbbm == null || this.popContent.lbbm == '') {
				malert("费用类别不能为空");
				popWin.ifClick = true;
				return false;
			}
			var xs = /^(\d|[1-9]\d+)(\.\d+)?$/; //大于0的整数小数
			if(!xs.test(this.popContent['fydj'])) {
				malert("费用单价为大于0的数");
				popWin.ifClick = true;
				return false;
			}
			//            	if(!xs.test(this.popContent['yhbl'])){
			//                		malert("优惠比例为大于等于0的数");
			//                    	return false;
			//                }
			this.$http.post('/actionDispatcher.do?reqUrl=XtwhYlfwxmMxfyxm&types=save',
					JSON.stringify(this.popContent))
				.then(function(data) {
					if(data.body.a == 0) {
						tableInfo.getData();
						popWin.isShow = false;
						popWin.isAdd = false;
						popWin.ifClick = true;
						malert("数据更新成功");
					} else {
						popWin.ifClick = true;
						malert("数据失败");
					}
				}, function(error) {
					console.log(error);
				});
		}
	}
});

//验证是否为空
$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
	if($(this).val() == '' || $(this).val() == null) {
		$(this).addClass("emptyError");
	} else {
		$(this).removeClass("emptyError");
	}
});

//为table循环添加拖拉的div（费用项目）
var drawWidthNumFyxm = $(".patientTableFyxm tr").eq(0).find("th").length;
for(var i = 0; i < drawWidthNumFyxm; i++) {
	if(i >= 2) {
		$(".patientTableFyxm th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth>");
	}
}