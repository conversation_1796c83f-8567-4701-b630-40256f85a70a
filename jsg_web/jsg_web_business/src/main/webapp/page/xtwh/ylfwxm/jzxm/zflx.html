<div id="zflx">
	<div class="toolMenu" style="display: block;">
		<input placeholder="名称/拼音/编码"
               style="width: 160px;height: 25px;margin-left: 10px" id="zflxjsvalue" @keydown="searchHc()"/>
	        <button @click="getData"><span class="fa fa-refresh"></span>刷新</button>
	        <button @click="addData"><span class="fa fa-plus"></span>新增</button>
	        <button @click="edit()"><span class="fa fa-edit"></span>修改</button>
	        <button @click="remove"><span class="fa fa-trash-o"></span>删除</button>
	        <button><span class="fa fa-check-square-o"></span>报表</button>
	    </div>
	    <!-- 支付类型 -->
	    <div class="tableDiv">
	        <table class="patientTable patientTableZflx" cellspacing="0" cellpadding="0">
	            <thead style="position: absolute;">
	            <tr>
	                <th class="tableNo"></th>
	                <th ><input type="checkbox" v-model="isCheckAll" @click="checkAll('jsonList')"></th>
	                <th>支付类型编码</th>
				 	<th>支付类型名称</th>
				 	<th>拼音代码</th>
				 	<th>其他支付项目</th>
				 	<th>是否现金</th>
				 	<th>停用标志</th>
	            </tr>
	            </thead>
	            <tr><th v-for="item in 8"></th></tr>
	            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
	                :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
	                @dblclick="edit($index)">
	                <th class="tableNo" v-text="$index+1"></th>
	                <th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)"/></th>
	                <td v-text="item.zflxbm"></td>
	                <td v-text="item.zflxmc"></td>
	                <td v-text="item.pydm"></td>
	                <td v-text="qtzfxm_tran[item.qtzfxm]"></td>
	                <td v-text="item.sfxj==0 ? '否':'是'"></td>
	                <td v-text="stopSign[item.tybz]"></td>
	            </tr>
	        </table>
	    </div>
		<div class="tablePage">
	        <select v-model="param.rows" @change="getData">
	            <option value="10">10</option>
	            <option value="20">20</option>
	            <option value="30">30</option>
	        </select>
	        <div class="pageBtu fa fa-angle-left" @click="changePage('prev')"></div>
	        第<input v-model="param.page" class="enterPage" @keyup.enter="changePage" type="number">页&nbsp;&nbsp;
	        共<span v-text="totlePage"></span>页
	        <div class="pageBtu fa fa-angle-right" @click="changePage('next')"></div>
	    </div>
</div>

<div id="zflxpop">
    <transition name="slide-fade">
        <div class="pop" v-show="isShow" style="display: none">
            <div class="popCenter">
                <div id="popCon" class="popInfo">
			        <div class="popTitle dragCSS" v-text="title"
			             onmousedown="drag(event,'popCon')" onmouseup="stopDrag()"></div>
	         <!--诊疗类别  -->    
	        <table class="popTable" cellspacing="0" cellpadding="0">
	            <tr>
	                <th>支付类型编码:</th>
	                <td><input v-model="popContent.zflxbm" disabled="disabled"></td>
	                 <th>支付类型名称:</th>
	                <td><input v-model="popContent.zflxmc" data-notEmpty="true" @keydown="nextFocus($event)"
	                				@blur="setPYDM(popContent.zflxmc,'popContent','pydm')"></td>   
	            </tr>
	            <tr>
	                <th>拼音代码:</th>
	                <td><input v-model="popContent.pydm" @keydown="nextFocus($event)" data-notEmpty="false"></td>
	            	<th>其他支付项目:</th>
	                <td>
	                	<select-input @change-data="resultChange" :data-notEmpty="false"
				                :child="qtzfxm_tran" :index="popContent.qtzfxm" :val="popContent.qtzfxm"
				                :name="'popContent.qtzfxm'" id="tz">
                    	</select-input>
	                </td>
	            </tr>
	            
					<tr>
						<th>是否现金:</th>
						<td><select-input @change-data="resultChange" :data-notEmpty="false"
				                :child="istrue_tran" :index="popContent.sfxj" :val="popContent.sfxj"
				                :name="'popContent.sfxj'" id="tz">
                    	</select-input></td>
						
						<th>停用标志:</th>
						<td><select-input @change-data="resultChange" :data-notEmpty="false"
				                :child="stopSign" :index="popContent.tybz" :val="popContent.tybz"
				                :name="'popContent.tybz'" id="tz">
                    		</select-input></td>
					</tr>
			</table>
	        <div class="popDoBtu popBtu">
	            <button @click="saveData" class=""><span class="fa fa-save"></span>保存</button>
	            <button @click="isShow = false" class="cancel"><span class="fa fa-close"></span>取消</button>
	        </div>
                </div>
            </div>
        </div>
    </transition>
</div>
<script type="text/javascript" src="zflx.js"></script>