<html>
<head>
    <title>退货查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script src="/pub/top.js"></script>
    <link href="thcx.css" rel="stylesheet" type="text/css"/>
</head>
<body>

<div id="crcx">
    <div class="toolMenu" style="display: block;">
        <input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd 00:00:00' })" onchange="getTime(this, 'star')"/>至
        <input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd 23:59:59' })" onchange="getTime(this, 'end')"/>
        <select class="YFSelect" v-model="yfkf" @change="yfkfChange">
            <option :value="0">-请选择库房-</option>
            <option v-for="item in yfkfList" :value="item.kfbm" v-text="item.kfmc"></option>
        </select>
        <button @click="getData"><span class="fa fa-refresh"></span>查询</button>
        <button @click=""><span class="fa fa-check-square-o"></span>报表</button>
    </div>
    <div class="tableDiv">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute;">
            <tr>
                <th class="tableNo"></th>
                <th><input type="checkbox" v-model="isCheckAll" @click="checkAll"></th>
                <th>出库单号</th>
                <th>审核时间</th>
                <th style="min-width: 200px">药品名称</th>
                <th style="min-width: 150px">药品规格</th>
                <th>出库数量</th>
                <th>药品进价</th>
                <th>药品零价</th>
                <th>药品批号</th>
                <th style="min-width: 120px">有效期至</th>
                <th style="min-width: 250px">药品产地</th>
                <th>库房单位</th>
                <th>药房单位</th>
                <th>分装比例</th>
            </tr>
            </thead>
            <tr>
                <th v-for="item in 13"></th>
            </tr>
            <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
                <th class="tableNo" v-text="$index+1"></th>
                <th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)"/>
                </th>
                <td v-text="item.ckdh"></td>
                <td v-text="fDate(item.shrq,'date')"></td>
                <td v-text="item.ypmc"></td>
                <td v-text="item.ypgg"></td>
                <td v-text="item.cksl"></td>
                <td v-text="fDec(item.ypjj,2)"></td>
                <td v-text="fDec(item.yplj,2)"></td>
                <td v-text="item.scph"></td>
                <td v-text="fDate(item.yxqz,'date')"></td>
                <td v-text="item.cdmc"></td>
                <td v-text="item.kfdwmc"></td>
                <td v-text="item.yfdwmc"></td>
                <td v-text="item.fzbl"></td>
            </tr>
        </table>
    </div>

    <div class="pageDiv">
        <div class="page">
            <div @click="goPage(page, 'prev', 'getData')" class="fa fa-angle-left num"></div>
            <div class="num" v-show="totlePage > 0" :class="{currentPage: page == 1}" @click="goPage(1, null, null)">1
            </div>
            <div v-show="prevMore">...</div>
            <div class="num" v-for="item in totlePage" v-text="item" :class="{currentPage: param.page == item}"
                 @click="goPage(item, null, null)" v-show="showLittle(item)"></div>
            <div v-show="nextMore">...</div>
            <div class="num" :class="{currentPage: param.page == totlePage}" @click="goPage(totlePage, null, null)"
                 v-text="totlePage" v-show="totlePage > 1"></div>
            <div @click="goPage(page, 'next', 'getData')" class="fa fa-angle-right num next"></div>
            <div>
                第<input type="number" v-model="page"/>页
                <div class="divBtu" @click="goPage(page, null, null)">跳转</div>
            </div>
            <div>
                共<span v-text="totlePage"></span>页
                <select v-model="param.rows" @change="getData()">
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="30">30</option>
                </select>条/页
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="thcx.js"></script>
</html>