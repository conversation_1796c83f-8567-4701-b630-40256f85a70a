<html>

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<script src="/pub/top.js"></script>
		<title>限量设置</title>
		<link href="xlsz.css" rel="stylesheet" type="text/css" />
	</head>

	<body>

		<div id="xlsz">
			<div class="toolMenu">
				<span style="margin-left: 10px">请先选择药房:</span>
				<select :class="{'emptyError': kfEmpty}" v-model="thisYF.yfmc" @change="yfChange($event.currentTarget.selectedOptions[0]._value)">
					<option :value="0">-请选择-</option>
					<option v-for="item in YFList" :value="item">{{item.yfmc}}</option>
				</select>
				<button @click="getData"><span class="fa fa-refresh"></span> 刷新</button>
				<button @click="save()"><span class="fa fa-save"></span> 保存</button>
			</div>
			<div class="tableDiv">
				<table class="patientTable" cellspacing="0" cellpadding="0">
					<thead>
						<tr>
							<th class="tableNo"></th>
							<th><input type="checkbox" v-model="isCheckAll" @click="checkAll()"></th>
							<th>药品名称</th>
							<th>药品规格</th>
							<th>上量</th>
							<th>下量</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<th v-for="item in 6"></th>
						</tr>
						<tr v-for="(item, $index) in jsonList" @click="checkOne($index)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
							<th class="tableNo" v-text="$index+1"></th>
							<th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)" />
							</th>
							<td v-text="item.ypmc"></td>
							<td v-text="item.ypgg"></td>
							<td><input @blur="checkEmpty(item.zdkc, $event)" type="number" v-model="item.zdkc"></td>
							<td><input @blur="checkEmpty(item.zgkc, $event)" type="number" v-model="item.zgkc"></td>
						</tr>
					</tbody>
				</table>
			</div>

			<div class="tablePage">
				<select v-model="param.rows" @change="getData">
					<option value="10">10</option>
					<option value="20">20</option>
					<option value="30">30</option>
				</select>
				<div class="pageBtu fa fa-angle-left" @click="changePage('prev')"></div>
				第<input v-model="param.page" class="enterPage" @keyup.enter="changePage" type="number">页&nbsp;&nbsp; 共
				<span v-text="totlePage"></span>页
				<div class="pageBtu fa fa-angle-right" @click="changePage('next')"></div>
			</div>
		</div>
	</body>
	<script type="text/javascript" src="xlsz.js"></script>

</html>