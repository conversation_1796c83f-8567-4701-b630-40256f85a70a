<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script type="application/javascript" src="/pub/top.js"></script>
    <title>出院统计</title>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link href="cytj.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div class="tools printHide">
    <span>从</span>
    <input id="dbegin" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd 00:00:00' })">
    <span>至</span>
    <input id="dEnd" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd 23:59:59' })">
    <span>选择科室</span>
    <select v-model="ksbm">
        <option :value="item.ksbm" v-for="(item,$index) in ksList" v-text="item.ksmc"></option>
    </select>
     <span>选择医生</span>
    <select v-model="zyys">
    	<option value="">全部</option>
        <option :value="item.rybm" v-for="(item,$index) in ysList" v-text="item.ryxm"></option>
    </select>
    <button @click="getData"><span class="fa fa-refresh"></span>查询</button>
    <button @click="print"><span class="fa fa-print"></span>打印</button>
    <span>统计类型</span>
    <select v-model="type">
        <option :value="0">明细统计</option>
        <option :value="1">汇总统计</option>
    </select>
</div>
<!--明细统计-->
<div class="cw_blank mxtj" v-show="isShow">
    <h2>出院明细统计</h2>
    <div class="headInfo">
        <div>
            <span>出院日期:</span>
            <span v-text="json.time"></span>
        </div>
    </div>

    <div class="cw_table tableDiv">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute">
            <tr>
                <th>住院科室</th>
                <th>住院号</th>
                <th>姓名</th>
                <th>性别</th>
                <th>年龄</th>
                <th>住院医生</th>
                <th>床位</th>
                <th>类别</th>
                <th>入院日期</th>
                <th>出院日期</th>
                <th>住院天数</th>
                <th>费用合计</th>
                <th>药品合计</th>
                <th>药占比</th>
                <th>预交合计</th>
                <th>补偿合计</th>
                <th>退补</th>
                <th>出院科室</th>
                <th style="min-width: 288px;">家庭住址</th>
            </tr>
            </thead>
            <tr>
                <th>住院科室</th>
                <th>住院号</th>
                <th>姓名</th>
                <th>性别</th>
                <th>年龄</th>
                <th>住院医生</th>
                <th>床位</th>
                <th>类别</th>
                <th>入院日期</th>
                <th>出院日期</th>
                <th>住院天数</th>
                <th>费用合计</th>
                <th>药品合计</th>
                <th>药占比</th>
                <th>预交合计</th>
                <th>补偿合计</th>
                <th>退补</th>
                <th>出院科室</th>
                <th style="min-width: 288px;">家庭住址</th>
            </tr>
            <tbody v-for="itemList in jsonList">
            <tr v-if="itemList.list.length > 0">
                <td :rowspan="itemList.list.length + 2" v-text="itemList.ksmc"></td>
              <!--  <td colspan="18"></td>-->
            </tr>
            <tr v-for="item in itemList.list">
                <td v-text="item.zyh"></td>
                <td v-text="item.brxm"></td>
                <td v-text="brxb_tran[item.brxb]"></td>
                <td v-text="item.csrq"></td>
                <td v-text="item.zyysxm"></td>
                <td v-text="item.rycwbh"></td>
                <td v-text="item.brfbmc"></td>
                <td v-text="fDate(item.ryrq,'date')"></td>
                <td v-text="fDate(item.cyrq,'date')"></td>
                <td v-text="item.zyts"></td>
                <td v-text="item.fyhj"></td>
                <td v-text="item.yphj"></td>
                <td v-text="item.yzb"></td>
                <td v-text="item.yjhj"></td>
                <td v-text="item.qtzf"></td>
                <td v-text="item.fytb"></td>
                <td v-text="item.cyksmc"></td>
                <td style="min-width: 288px;" v-text="item.jzdmc"></td>
            </tr>
            <tr v-if="itemList.list.length > 0">
                <td></td>
                <td>合计：</td>
                <td>{{itemList.list.length + '人'}}</td>
                <td colspan="17" style="text-align: left;"></td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<!--汇总统计-->
<div class="cw_blank hztj" v-show="isShow">
    <h2>出院汇总统计</h2>
    <div class="headInfo" style="width: 760px">
        <div>
            <span>出院日期:</span>
            <span v-text="json.time"></span>
        </div>
    </div>

    <div class="cw_table tableDiv">
        <table class="patientTable" cellspacing="0" cellpadding="0">
            <thead style="position: absolute">
            <tr>
                <th>入院科室</th>
                <th>住院医生</th>
                <th>居民医保</th>
                <th>职工医保</th>
                <th>农合</th>
                <th>自费</th>
                <th>合计</th>
            </tr>
            </thead>
            <tr>
                <th v-for="item in 7"></th>
            </tr>
            <tbody v-for="itemList in jsonList">
            <tr>
                <td :rowspan="itemList.list.length + 1" v-text="itemList.ksmc"></td>
                <td colspan="6"></td>
            </tr>
            <tr v-for="item in itemList.list">
                <td v-text="item.zyysxm"></td>
                <td v-text="item.jmyb"></td>
                <td v-text="item.zgyb"></td>
                <td v-text="item.nh"></td>
                <td v-text="item.zf"></td>
                <td v-text="item.hj"></td>
            </tr>
            <tr>
                <td>小计</td>
                <td v-text=""></td>
                <td v-text="itemList.jmybxj"></td>
                <td v-text="itemList.zgybxj"></td>
                <td v-text="itemList.nhxj"></td>
                <td v-text="itemList.zfxj"></td>
                <td v-text="itemList.zj"></td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

</body>
<script type="application/javascript" src="cytj.js"></script>

</html>