<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>交款汇总表</title>
    <script type="application/javascript" src="/pub/top.js"></script>
    <script type="application/javascript" src="jquery.jqprint-0.3.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link href="jkhzb.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="person printHide">
    <div class="tableDiv">
        <table cellspacing="0" cellpadding="0" class="patientTable">
            <tr>
                <th><input type="checkbox" v-model="isCheckAll" @click="checkAll('jsonList')"></th>
                <th style="min-width: 40px">类型</th>
                <th style="min-width: 100px">凭证号</th>
                <th style="min-width: 80px">姓名</th>
            </tr>
            <tr v-for="(item, $index) in jsonList"
                :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]"
                @click="checkOne($index)">
                <td><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)"/></td>
                <td v-text="item.jklx ==0 ? '门诊':'住院'"></td>
                <td v-text="item.jkpzh"></td>
                <td v-text="item.czyxm"></td>
            </tr>
        </table>
    </div>
</div>
<div class="tools printHide">
    <span>从</span>
    <input id="dbegin" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" onchange="getTime()">
    <span>至</span>
    <input id="dEnd" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" onchange="getTime()">
    <!--<span>选择日结人员</span>
    <select-input @change-data="resultChange" :not_empty="false"
        :child="allryList" :index="'ryxm'" :index_val="'rybm'" :val="rjry"
        :name="'rjry'" :search="true">
    </select-input>-->
    <button @click="getData"><span class="fa fa-refresh"></span>查询</button>
    <button @click="print"><span class="fa fa-print"></span>打印</button>
</div>
<div class="cw_blank" id="cw_bank">

    <div class="headInfo">
        <h2 class="cw_h2"><span v-text='yljgmc'></span>日结汇总表</h2>
        <div style="width: 100%">
            <span>日结人员:</span>
            <span v-for="(item,$index) in czyList">
            	<div v-text="item"></div>
            </span>
        </div><br>
        <div style="width: 100%">
            <span>汇总日结区间:</span>
            <span>从</span>
            <span v-text="json.ksrq"></span>
            <span>到</span>
            <span v-text="json.jsrq"></span>
        </div><br>
        <div style="width: 180px">
            <span>结算合计:</span>
            <span v-text="fDec(mzzyhz.allJshj,2)"></span>
        </div>
        <div style="width: 180px">
            <span>报销合计:</span>
            <span v-text="fDec(mzzyhz.allBxhj,2)"></span>
        </div>
        <div style="width: 180px">
            <span>支付合计:</span>
            <span v-text="fDec(mzzyhz.allqtzfhj,2)"></span>
        </div>
        <div style="width: 180px">
            <span>自付合计:</span>
            <!-- <span v-text="fDec(fDec(mzzyhz.allJshj,2) - fDec(mzzyhz.allBxhj,2),2) - fDec(mzzyhz.allqtzfhj,2)"></span> -->
            <span v-text="fDec(mzzyhz.allZfhj,2)"></span>
        </div>
        <div style="width: 200px">
            <span>应交现金合计:</span>
            <span v-text="fDec(mzzyhz.allYjxjhj,2)"></span>
        </div>
    </div>

    <div class="cw_table">
        <table cellspacing="0" cellpadding="0" style="float: left;font-size: 12px; width: 1000px;">
            <tr>
                <td rowspan="3" style="width: 80px">病人类型</td>
                <td rowspan="1" colspan="1">结算人次</td>
                <td rowspan="1" colspan="7">结算收入</td>
                <td rowspan="1" colspan="3">现金收入</td>
            </tr>
            <tr>
                <td rowspan="2">结算<br>A</td>
                <td rowspan="2">保险支付<br>B</td>
                <td rowspan="2">优惠减免<br>C</td>
                <td rowspan="2">其他支付<br>Q</td>
                <td colspan="4">自付</td>
                <td rowspan="2">结算合计<br>H</td>
                <td rowspan="2">区间预交金<br>I</td>
                <td rowspan="2">应交现金<br>J</td>
            </tr>
            <tr>
                <td>结算押金D</td>
                <td>退现金E</td>
                <td>补现金F</td>
                <td>小计G</td>
            </tr>
            <tr>
                <td>住院</td>
                <td colspan="12">
                    <span>计算公式：</span>
                    <span style="margin-left: 500px">G=D-E+F</span>
                    <span style="margin-left: 30px">H=B+C+G</span>
                    <span style="margin-left: 142px">J=I-E+F</span>
                </td>
            </tr>
            <tr v-for="(item,$index) in zyBrfbList">
                <td v-text="item.brfbmc"></td>
                <td v-text="item.rc"></td>
                <td v-text="fDec(item.qtzf,2)"></td>
                <td v-text="fDec(item.yhhj,2)"></td>
                <td>0.00</td>
                <td v-text="fDec(item.jsyj,2)"></td>
                <td v-text="fDec(item.tkje,2)"></td>
                <td v-text="fDec(item.bkje,2)"></td>
                <td v-text="fDec(item.xj,2)"></td>
                <td v-text="fDec(item.jshj,2)"></td>
                <td v-text="fDec(item.qjyj,2)"></td>
                <td v-text="fDec(item.yjxj,2)"></td>
            </tr>
      <!--      <tr>
                <td>小计</td>
                <td v-text="zyxj.zyrc"></td>
                <td v-text="fDec(zyxj.zybxzf,2)"></td>
                <td v-text="fDec(zyxj.zyyhje,2)"></td>
                <td v-text="fDec(zyxj.zyjsyj,2)"></td>
                <td v-text="fDec(zyxj.zytkje,2)"></td>
                <td v-text="fDec(zyxj.zybkje,2)"></td>
                <td v-text="fDec(zyxj.zyxjje,2)"></td>
                <td v-text="fDec(zyxj.zyjshj,2)"></td>
                <td v-text="fDec(zyxj.zyyjje,2)"></td>
                <td v-text="fDec(zyxj.zyxjzf,2)"></td>
            </tr>-->
            <tr>
                <td>门诊</td>
                <td colspan="12">
                    <span>计算公式：</span>
                    <span style="margin-left: 594px">H=B+Q+G</span>
                    <span style="margin-left: 130px">J=G+I</span>
                </td>
            </tr>
            <tr v-for="(item,$index) in mzBrfbList">
                <td v-text="item.ryfbmc"></td>
                <td v-text="item.rc"></td>
                <td v-text="fDec(item.ybkzf,2)"></td>
                <td v-text="item.yhje"></td>
                <td v-text="fDec(item.qtzf,2)"></td>
                <td>0.00</td>
                <td>0.00</td>
                <td>0.00</td>
                <td v-text="item.xjzf"></td>
                <td v-text="item.jshj"></td>
                <td v-text="item.zyyj"></td>
                <td v-text="item.yjxjhj"></td>
            </tr>
            <!--<tr>
                <td>小计</td>
                <td v-text="mzxj.mzrc"></td>
                <td v-text="fDec(mzxj.mzybkzf,2)"></td>
                <td v-text="fDec(mzxj.mzyhje,2)"></td>
                <td>0.00</td>
                <td>0.00</td>
                <td>0.00</td>
                <td v-text="fDec(mzxj.mzxjzf,2)"></td>
                <td v-text="fDec(mzxj.mzjshj)"></td>
                <td>0.00</td>
                <td v-text="fDec(mzxj.mzxjzf,2)"></td>
            </tr>-->
            <tr style="height:90px">
                <td>住院结算<br>费用汇总<br>(统计分类)</td>
                <td colspan="12" style="vertical-align: top">
                    <span v-for="(item,$index) in zyFymxList">
                        <span style="width: 80px;text-align: right" v-text="item.lbmc"></span>
                        <span style="width: 80px;text-align: left" v-text="fDec(item.je,2)"></span>
                    </span><br><br><br><br>
                    <!--<span>
                        <span style="width: 80px;text-align: right" >合计：</span>
                        <span style="width: 80px;text-align: left"></span>
                    </span>-->
                </td>
            </tr>
            <tr style="height: 140px">
                <td>门诊结算<br>费用汇总<br>(统计分类)</td>
                <td colspan="12">
                    <span v-for="(item,$index) in mzFylbList">
                        <span style="width: 80px;text-align: right" v-text="item.fylbmc"></span>
                        <span style="width: 80px;text-align: left" v-text="fDec(item.sfyje+item.tfyje,2)"></span>
                    </span><br><br><br><br>
                    <span>
                        <span style="width: 80px;text-align: right">合计：</span>
                        <span style="width: 80px;text-align: left" v-text="mzPopContent.fyhj"></span>
                    </span>
                </td>
            </tr>
        </table>
    </div>

    <div class="headInfo footInfo">
        <div>
            <span>会计:</span>
            <span></span>
        </div>
        <div>
            <span>审核:</span>
            <span></span>
        </div>
        <div>
            <span>出纳:</span>
            <span></span>
        </div>
        <div>
            <span>制单:</span>
            <span></span>
        </div>
        <div>
            <span>打印时间:</span>
            <span></span>
        </div>
        <div style="width: 100%; text-align: left;">备注:</div>
    </div>
</div>
</body>
<script type="application/javascript" src="jkhzb.js"></script>
</html>