.right{
    width: calc(100% - 250px);
    height: 100%;
    float: left;
}

.yzclSearch{
    color: #000000;
    border-bottom: 4px solid #eeeeee;
    border-right:  4px solid #eeeeee;
    margin-top:0px;
}

.YPTable{
    padding-left: 4%;
}

.YPTable td{
    border: 1px solid #bbbbbb;
    height: 25px;
    font-size: 12px;
}

.YPTable th{
    font-size: 12px;
}

.YPTable tr:nth-child(n+2):hover{
    background-color: #EAF2FF;
}

.CFTabTable{
    float: left;
    padding: 0;
    width: calc(100% - 10px);
    height: calc(100% - 114px);
    border-right: 4px solid #EEEEEE;
    overflow: auto;
    margin-left: 6px;
}

.CFTabTable table{

}

.CFTabTable tr{
    border-bottom: 1px solid #bbbbbb;
}

.CFTabTable td{
    border: 1px solid #CCCCCC;
    padding: 6px;
}

.toolMenu img{
    width: 20px;
    margin-bottom: 4px;
}

.toolMenu>div{
    float: left;
    width: 55px;
    text-align: center;
    cursor: pointer;
    padding: 2px 0;
    border: 1px solid #eeeeee;

}

.toolMenu>div:hover{
    background-color: #CCCCCC;
}

.yzcl_context{
    float: left;
    margin-top: 44px;
    width: 100%;
}

.YZInfo{
    width: 70%;
    margin-left: 8px;
    float: left;
}

.YZChoice{
    display: inline-block;
    background-color: #eee;
    width: 100%;
}

.YZChoice>div{
    float: left;
    position: relative;
    margin: 5px 10px 5px 4px;
}

.YZChoice>div>input{
    width: 90px;
}

.YZChoice>div>span{
    position: absolute;
    top: 3px;
    font-size: 14px;
}

.personInfo{
    display: inline-block;
}

.personInfo>div{
    float: left;
    margin-right: 10px;
}

.yzdItem{
    margin-top: 20px;
    text-align: center;
}

.table_tab1 tr td:first-child{
    text-align: center;
}

.table_tab1{
    height: auto;
}

.table_tab1 th{
    background-color: #eeeeee;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
}

.AllYZ{
    height: calc(100% - 260px);
    overflow: scroll;
}

.AllYZSH{
    height: calc(100% - 270px);
    overflow: scroll;
}

.AllYZCX{
    height: calc(100% - 160px);
    overflow: scroll;
}

.YZInfo textarea{
    height: 70px;
    width: 100%;
    background-color: #eeeeee;
}

.patientBaseInfo{
    width: calc(30% - 14px);
    background-color: #eeeeee;
    float: left;
    margin-left: 6px;
}

.baseTitle{
    border-bottom: 1px solid #bbbbbb;
    padding: 6px 0 6px 12px;
}

.baseInfo div{
    width: 100%;
    margin: 2px;
    display: inline-block;
    position: relative;
}

.baseInfo span{
    display: inline-block;
    background-color: #FFFFFF;
    float: left;
    padding: 4px;
}

.baseInfo span:first-child{
    width: 50px;
    margin-right: 2px;
    padding-left: 20px;
}

.baseInfo span:nth-child(2){
    width: calc(100% - 90px);
    min-height: 15px;
}

.baseInfo span:nth-child(3){
    position: absolute;
    left: 0;
    color: #F7D063;
    top: 2px;
}
.ChildtablePage{
    border-top: 4px solid #EEEEEE;
    border-right: 4px solid #EEEEEE;
    background: linear-gradient(to bottom,#ffffff 0,#ffffff 100%);
    width: 230px;
    padding: 4px 0;
}


.toolMenu{
    margin: 0 4px 0 8px;
    width:99%
}

.InfoMenu{
    padding: 10px 0;
}

#demoTable{
    border-top: 2px solid #1AB394 !important;
}

#demoTable td{
    overflow: inherit;
}

.searchDiv{
    position: absolute;
    top: 32px;
    left: 0;
    max-width: 630px;;
    overflow-x: scroll;
}

.searchTableDiv{
    overflow-x: visible;
}

.bookMark{
    border-radius: 0;

}

.bookMarkDiv_selected div:first-child{
    background: #1AB394;
}

select, input[type = text]{
    width: 100px;
    height: 30px;
    border: 1px solid #aaa;
    border-radius: 0;
    outline: none;
}

.searchYp{
    margin: 10px;
}

.searchYp input{
    margin: 0 10px;
}

.tableDiv{
    height: calc(50% - 64px);
    margin-top: 10px;
}

.patientTable tbody tr:first-child th{
    height: 32px;
}

.tableDiv input{
    margin: 0 4px;
    width: 200px;
    height: 22px;
}

.tableDiv span{
    margin-left: 10px;
}

.editTableTitle{
    font-size: 16px;
    margin-left: 10px;
}

.patientTable td{
    position: relative;
}