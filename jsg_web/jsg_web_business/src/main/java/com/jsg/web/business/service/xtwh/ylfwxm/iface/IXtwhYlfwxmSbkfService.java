package com.jsg.web.business.service.xtwh.ylfwxm.iface;

import com.jsg.common.pojo.ResultParam;
import com.jsg.csp.api.xtwh.ylfwxm.pojo.Sb_kfModel;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.InvocationResult;

/**
 *
* @ClassName: IXtwhYlfwxmSbkfService
* @Description: TODO(设备库房)
* <AUTHOR>
* @date 2020年6月8日 上午10:15:46
*
 */
public interface IXtwhYlfwxmSbkfService {


	 /*查询所有设备库房*/
	ResultParam querySb_kfList(UtilRequest msg,InvocationResult result);

	 /*查询单个设备库房*/

	Sb_kfModel querySb_kfOne(UtilRequest msg,InvocationResult result);

	  /*删除设备库房*/

	int deletebatch(UtilRequest msg,InvocationResult result);

	  /*批量更新设备库房*/

	int savebatch(UtilRequest msg,InvocationResult result);
}
