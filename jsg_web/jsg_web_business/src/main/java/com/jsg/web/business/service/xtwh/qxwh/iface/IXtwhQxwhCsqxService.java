package com.jsg.web.business.service.xtwh.qxwh.iface;

import java.util.List;

import com.jsg.common.pojo.ResultParam;
import com.jsg.csp.api.xtwh.qxwh.pojo.Gyb_csqxModel;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.InvocationResult;

/**
 *
* @ClassName: IXtwhQxwhCsqxService
* @Description: TODO(参数权限)
* <AUTHOR>
* @date 2020年6月1日 下午8:43:44
*
 */
public interface IXtwhQxwhCsqxService {

	/*查询所有参数权限*/
	ResultParam queryGyb_csqxList(UtilRequest msg,InvocationResult result);

	 /*查询单个参数权限*/
	Gyb_csqxModel queryGyb_csqxOne(UtilRequest msg,InvocationResult result);

	/*查询参数权限根据用例编码集合*/
	List<Gyb_csqxModel> queryCsqxList(UtilRequest msg,InvocationResult result);

	/*修改参数权限*/
	int updateCsqx(UtilRequest msg,InvocationResult result);


}
