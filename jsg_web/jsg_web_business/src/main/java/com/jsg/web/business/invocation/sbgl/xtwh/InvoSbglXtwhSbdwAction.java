package com.jsg.web.business.invocation.sbgl.xtwh;

import com.alibaba.fastjson.JSONObject;
import com.jsg.common.util.RequestUtil;
import com.jsg.csp.api.sbgl.xtwh.pojo.Sbgl_sbdwModel;
import com.jsg.frame.constants.IRequestConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.BaseInvocation;
import com.jsg.frame.invocation.InvocationContext;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.sbgl.xtwh.iface.ISbglKfwhSbdwService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 *
 * @ClassName: InvoWzkfXtwhWzdwAction
 * @Description: TODO(设备单位控制操作)
 * <AUTHOR> YK
 * @date 2020年9月12日 下午10:31:40
 *
 */
@Controller("INVO_New1SbglXtwhSbdw")
public class InvoSbglXtwhSbdwAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private ISbglKfwhSbdwService new1sbgllKfwhSbdwService;

	// 参数校验
	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		Sbgl_sbdwModel wzb = null;

		switch (optType) {
		// 保存
		case "save":
			wzb = RequestUtil.getObjParamter(invoContext.getRequest(), Sbgl_sbdwModel.class);
			if (wzb == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoSbglXtwhSbdwAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", wzb);
				new1sbgllKfwhSbdwService.saveOne(msg, result);
			}
			break;
		// 批量删除
		case "delete":
			List<Sbgl_sbdwModel> list = (List<Sbgl_sbdwModel>) RequestUtil.getListParamter(invoContext.getRequest(),
					Sbgl_sbdwModel.class);
			if (list == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoSbglXtwhSbdwAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("beanList", list);
				new1sbgllKfwhSbdwService.deletebatch(msg, result);
			}

			break;
		// 修改
		case "update":
			wzb = RequestUtil.getObjParamter(invoContext.getRequest(), Sbgl_sbdwModel.class);
			if (wzb == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoSbglXtwhSbdwAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", wzb);
				new1sbgllKfwhSbdwService.updateOne(msg, result);
			}
			break;
		// 查询
		case "query":
			if (json == null || "".equals(json)) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoSbglXtwhSbdwAction Interface| Requerst Parameter Validation Failed");
			} else {
				wzb = JSONObject.parseObject(json, Sbgl_sbdwModel.class);
				msg.getParam().put("bean", wzb);
				new1sbgllKfwhSbdwService.queryList(msg, result);
			}
			break;

		}
	}

}
