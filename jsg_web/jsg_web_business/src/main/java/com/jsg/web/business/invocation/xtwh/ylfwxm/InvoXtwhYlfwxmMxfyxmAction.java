package com.jsg.web.business.invocation.xtwh.ylfwxm;

import com.alibaba.fastjson.JSONObject;
import com.jsg.common.pojo.DataGrid;
import com.jsg.common.pojo.ResultParam;
import com.jsg.common.util.RequestUtil;
import com.jsg.common.utilfun.UtilFun;
import com.jsg.csp.api.xtwh.ylfwxm.pojo.Gyb_mxfyxmModel;
import com.jsg.frame.constants.IRequestConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.BaseInvocation;
import com.jsg.frame.invocation.InvocationContext;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.xtwh.ylfwxm.iface.IXtwhYlfwxmMxfyxmService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
* @ClassName: InvoXtwhYlfwxmMxfyxmAction
* @Description: TODO(明细费用项目)
* <AUTHOR>
* @date 2020年5月21日 下午6:31:08
*
 */
@Component("INVO_New1XtwhYlfwxmMxfyxm")
public class InvoXtwhYlfwxmMxfyxmAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoXtwhYlfwxmMxfyxmAction.class);

	@Autowired
	IXtwhYlfwxmMxfyxmService new1xtwhYlfwxmMxfyxmService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
    	String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		Integer ref ;
        getSession(invoContext,result);
		List<Gyb_mxfyxmModel> beans;
		Gyb_mxfyxmModel bean=new Gyb_mxfyxmModel();
		ResultParam list;
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types","");//操作类型
		String mxfybm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"mxfybm","");//费用明细编码
		String sjson = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"json","");
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		if (mxfybm == null)
			mxfybm = "";

		switch (optType) {
		case "save"://新增修改
			bean = RequestUtil.getObjParamter(invoContext.getRequest(),Gyb_mxfyxmModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			if(bean.getYhbl()==null||bean.getYhbl().equals("")){
				bean.setYhbl(0.00);
			}

			msg.getParam().put("bean", bean);
			msg.getParam().put("csqxinfo", csqxinfo);
			msg.getParam().put("userinfo", userinfo);
			ref = new1xtwhYlfwxmMxfyxmService.savebatch(msg, result);
			break;
		case "delete"://删除
			if (sjson == null || sjson.equals("")){
				beans = (List<Gyb_mxfyxmModel>)RequestUtil.getListParamter(invoContext.getRequest(),Gyb_mxfyxmModel.class);

			}else {
				beans = (List<Gyb_mxfyxmModel>)JSONObject.parseArray(sjson, Gyb_mxfyxmModel.class);
			}
			msg.getParam().put("bean", beans);
			ref = new1xtwhYlfwxmMxfyxmService.deletebatch(msg, result);
			break;
		case "updateBetch": //针对病案对应码表批量更新明细费用项目
			beans = (List<Gyb_mxfyxmModel>)RequestUtil.getListParamter(invoContext.getRequest(),Gyb_mxfyxmModel.class);
			msg.getParam().put("beans", beans);
			ref = new1xtwhYlfwxmMxfyxmService.updateBetch(msg, result);
			break;
		case "query"://查询全部
			String dg = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"dg","");//排序方式
			DataGrid dGrid = (DataGrid)JSONObject.parseObject(dg, DataGrid.class);
			Gyb_mxfyxmModel dbfabean;
			if(sjson.equals("")||sjson==null){
				dbfabean=new Gyb_mxfyxmModel();
			}else{
				dbfabean=JSONObject.parseObject(sjson, Gyb_mxfyxmModel.class);
			}
			UtilFun.DataGridInit(dGrid, "mxfybm");
			dbfabean.setRows(dGrid.getRows());
			dbfabean.setPage(dGrid.getPage());
			dbfabean.setSort(dGrid.getSort());
			dbfabean.setOrder(dGrid.getOrder());
			dbfabean.setParm(dGrid.getParm());
			msg.getParam().put("bean", dbfabean);
			msg.getParam().put("csqxinfo", csqxinfo);
			list = new1xtwhYlfwxmMxfyxmService.queryGyb_mxfyxmList(msg, result);
			break;
		case "queryOne"://查询单个
			if (mxfybm.equals("")){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				result.setC("请选择要查询的费用明细!");
			}
			bean.setMxfybm(mxfybm);
			msg.getParam().put("bean", bean);
			bean = new1xtwhYlfwxmMxfyxmService.queryGyb_mxfyxmOne(msg,result);
			break;
		case "updateByFylb"://针对病案码表对应根据费用类别更新明细费用项目
			bean = RequestUtil.getObjParamter(invoContext.getRequest(),Gyb_mxfyxmModel.class);
			if (bean == null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				return;
			}
			msg.getParam().put("bean", bean);
			ref = new1xtwhYlfwxmMxfyxmService.updateByFylb(msg, result);
			break;
		default:
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			break;
		}

	}

}
