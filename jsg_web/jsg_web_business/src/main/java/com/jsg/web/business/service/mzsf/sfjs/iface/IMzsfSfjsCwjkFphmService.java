package com.jsg.web.business.service.mzsf.sfjs.iface;

import com.jsg.csp.api.mzsf.sfjs.pojo.Mzb_cwjkFphmModel;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.InvocationResult;

/**
 *
* @ClassName: IMzsfSfjsCwjkFphmService
* @Description: TODO(查询门诊交款需要展示的价款凭证号)
* <AUTHOR>
* @date 2020年8月14日 下午5:00:45
*
 */
public interface IMzsfSfjsCwjkFphmService {
	//查询所有发票号码
	Mzb_cwjkFphmModel queryFphm(UtilRequest msg,InvocationResult result);

	//根据交款凭证号查询所有交款凭证号
	Mzb_cwjkFphmModel queryFphmByjkpzh(UtilRequest msg,InvocationResult result);


}
