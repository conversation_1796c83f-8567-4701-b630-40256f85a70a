package com.jsg.web.business.service.xtwh.ylfwxm.iface;

import com.jsg.common.pojo.ResultParam;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.InvocationResult;

/**
 *
* @ClassName: IXtwhYlfwxmHbsfxmService
* @Description: TODO(号别收费项目)
* <AUTHOR>
* @date 2020年6月10日 上午12:22:17
*
 */
public interface IXtwhYlfwxmHbsfxmService {


	 /*查询所有号别收费项目*/
	ResultParam queryGyb_hbsfxmList(UtilRequest msg,InvocationResult result);

	  /*删除号别收费项目*/

	int deletebatch(UtilRequest msg,InvocationResult result);

	  /*批量更新号别收费项目*/

	int savebatch(UtilRequest msg,InvocationResult result);


	 /*根据挂号种类查询收费明细*/
	ResultParam queryGhfyList(UtilRequest msg,InvocationResult result);
}
