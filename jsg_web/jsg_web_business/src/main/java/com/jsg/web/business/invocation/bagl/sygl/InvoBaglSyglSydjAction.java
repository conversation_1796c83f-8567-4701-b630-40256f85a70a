package com.jsg.web.business.invocation.bagl.sygl;

import com.alibaba.fastjson.JSONObject;
import com.jsg.common.util.RequestUtil;
import com.jsg.csp.api.bagl.sygl.pojo.BaSyScxxModel;
import com.jsg.csp.api.bagl.sygl.pojo.BaglSydjMsgModel;
import com.jsg.csp.api.bagl.sygl.pojo.Bagl_basy_jbxxModel;
import com.jsg.frame.constants.IRequestConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.BaseInvocation;
import com.jsg.frame.invocation.InvocationContext;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.bagl.sygl.iface.IBaglSyglSydjService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
* @ClassName: InvoBaglSyglJbxxAction
* @Description: TODO(病案基本信息)
* <AUTHOR>
* @date 2020年9月6日 下午6:28:19
*
 */
@Component("INVO_New1BaglSyglSydj")
public class InvoBaglSyglSydjAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants{

	private final Logger logger = LoggerFactory.getLogger(InvoBaglSyglSydjAction.class);

	@Autowired
	IBaglSyglSydjService new1baglSyglSydjService;

    @Override
    public void validateParam(InvocationContext invoContext, InvocationResult result)
    {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
        if (StringUtils.isBlank(optType))
        {
            result.setLogicCode(PARAM_VALIDATE_ERROR);
            logger.info("InvoBaglSyglSydjAction Interface| Requerst Parameter Validation Failed");
        }
    }

	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"types",""); //操作类型
		UtilRequest msg = (UtilRequest)this.createRequestMsg(UtilRequest.class, invoContext);
		String parm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"parm","");

		//获取Session对象 userinfo用户信息
		getSession(invoContext,result);
		msg.getUserinfo().put("userinfo", userinfo);//用户信息
		msg.getCsqxinfo().put("csqxinfo", csqxinfo);//参数信息

		BaSyScxxModel basyscxx;

		switch (optType) {
		case "updateSydj"://病案首页登记保存
			BaglSydjMsgModel bean = RequestUtil.getObjParamter(invoContext.getRequest(),BaglSydjMsgModel.class);
			msg.getParam().put("bean", bean);
			new1baglSyglSydjService.updateSydj(msg, result);
			break;
		case "selectByZyhOrBah"://根据住院号或者病案号查询病案首页登记信息
			String zyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zyh","");
			if(zyh.equals("") || zyh==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("zyh", zyh);
				new1baglSyglSydjService.selectByZyhOrBah(msg, result);
			}
			break;
		case "updateSh"://针对审核的修改
			BaglSydjMsgModel beansh = RequestUtil.getObjParamter(invoContext.getRequest(),BaglSydjMsgModel.class);
			//Bagl_basy_jbxxModel beansh = RequestUtil.getObjParamter(invoContext.getRequest(),Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", beansh);//
			msg.getParam().put("ysOrBas", 1); //1代表医生（准许修改疾病编码）；2代表病案室的（不允许修改疾病编码）
			new1baglSyglSydjService.updateSh(msg, result);
			break;
		case "updateJs"://针对接收的修改
			Bagl_basy_jbxxModel beanJs = RequestUtil.getObjParamter(invoContext.getRequest(),Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", beanJs);
			new1baglSyglSydjService.updateJs(msg, result);
			break;
		case "updateGd"://针对归档的修改
			Bagl_basy_jbxxModel beanGd = RequestUtil.getObjParamter(invoContext.getRequest(),Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", beanGd);
			new1baglSyglSydjService.updateGd(msg, result);
			break;
		case "updateQxsh"://针对取消审核的修改
			Bagl_basy_jbxxModel beanQxsh = RequestUtil.getObjParamter(invoContext.getRequest(),Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", beanQxsh);
			new1baglSyglSydjService.updateQxsh(msg, result);
			break;
		case "updateQxjs"://针对取消接收的修改
			Bagl_basy_jbxxModel beanQxjs = RequestUtil.getObjParamter(invoContext.getRequest(),Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", beanQxjs);
			new1baglSyglSydjService.updateQxjs(msg, result);
			break;
		case "updateQxgd"://针对取消归档的修改
			Bagl_basy_jbxxModel beanQxgd = RequestUtil.getObjParamter(invoContext.getRequest(),Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", beanQxgd);
			new1baglSyglSydjService.updateQxgd(msg, result);
			break;
		case "queryIsSh"://针对审核与未审核病案信息的列表信息(接收与归档信息也放在这)
			Bagl_basy_jbxxModel shOrWsh=JSONObject.parseObject(parm, Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", shOrWsh);
			new1baglSyglSydjService.queryIsSh(msg, result);
			break;
		case "getMessage":	//病案获取信息
			String bazyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zyh","");
			if(bazyh.equals("") || bazyh==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("zyh", bazyh);
				new1baglSyglSydjService.getMessage(msg, result);
			}
		case "printBasyxx": //病案首页打印
			String dyzyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zyh","");
			if(dyzyh.equals("") || dyzyh==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("zyh", dyzyh);
				new1baglSyglSydjService.printBasyxx(msg, result);
			}
			break;
		case "queryZkba"://查询所有在库病案信息
			Bagl_basy_jbxxModel zkbz=JSONObject.parseObject(parm, Bagl_basy_jbxxModel.class);
			msg.getParam().put("bean", zkbz);
			new1baglSyglSydjService.queryZkba(msg, result);
			break;

		case "getJbxx":// 拉取基本信息
			String zyhParm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zyh","");
			if(zyhParm.equals("") || zyhParm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("zyh", zyhParm);
				new1baglSyglSydjService.getJbxx(msg, result);
			}
			break;
		case "getFyxx":// 拉取费用信息
			String zyhFyParm = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zyh","");
			if(zyhFyParm.equals("") || zyhFyParm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
	            logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("zyh", zyhFyParm);
				new1baglSyglSydjService.getFyxx(msg, result);
			}
			break;
		case "updateFyxx":
			if(parm.equals("") || parm==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				Bagl_basy_jbxxModel bajbxx=JSONObject.parseObject(parm, Bagl_basy_jbxxModel.class);
				msg.getParam().put("bean", bajbxx);
				new1baglSyglSydjService.updateFyxx(msg, result);
			}
			break;
		case "getJcjg":
			String ryzyh = RequestUtil.getStrParamterAsDef(invoContext.getRequest(),"zyh","");
			if(ryzyh.equals("") || ryzyh==null){
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
			}else{
				msg.getParam().put("zyh", ryzyh);
				new1baglSyglSydjService.getJcjg(msg, result);
			}
			break;
			case "queryYbjsc":
				if (!parm.equals("") && parm != null) {

					basyscxx = (BaSyScxxModel)JSONObject.parseObject(parm, BaSyScxxModel.class);
					msg.getParam().put("bean", basyscxx);
					this.new1baglSyglSydjService.queryYbjsc(msg, result);
				} else {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					this.logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
				}
				break;
			case "queryYbBaSc":
				if (!parm.equals("") && parm != null) {
					basyscxx = (BaSyScxxModel)JSONObject.parseObject(parm, BaSyScxxModel.class);
					msg.getParam().put("bean", basyscxx);
					this.new1baglSyglSydjService.queryYbBaSc(msg, result);
				} else {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					this.logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
				}
				break;
			case "queryYbBaScZyhMx":
				if (!parm.equals("") && parm != null) {
					basyscxx = (BaSyScxxModel)JSONObject.parseObject(parm, BaSyScxxModel.class);
					msg.getParam().put("bean", basyscxx);
					this.new1baglSyglSydjService.queryYbBaScZyhMx(msg, result);
				} else {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					this.logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
				}
				break;
			case "updateYbBaScZyhMx":
				if (!parm.equals("") && parm != null) {
					basyscxx = (BaSyScxxModel)JSONObject.parseObject(parm, BaSyScxxModel.class);
					msg.getParam().put("bean", basyscxx);
					this.new1baglSyglSydjService.updateYbBaScZyhMx(msg, result);
				} else {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					this.logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
				}
				break;
			case "deleteYbBaScZyh":
				if (!parm.equals("") && parm != null) {
					basyscxx = (BaSyScxxModel)JSONObject.parseObject(parm, BaSyScxxModel.class);
					msg.getParam().put("bean", basyscxx);
					this.new1baglSyglSydjService.deleteYbBaScZyh(msg, result);
				} else {
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					this.logger.info("InvoBaglSyglJbxxAction Interface| Requerst Parameter Validation Failed");
				}
				break;
		default:
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			break;
		}
	}
}
