package com.jsg.web.business.invocation.sbgl.xtwh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsg.common.util.RequestUtil;
import com.jsg.csp.api.wzkf.xtwh.pojo.Wzb_kcxlModel;
import com.jsg.frame.constants.IRequestConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.BaseInvocation;
import com.jsg.frame.invocation.InvocationContext;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.sbgl.xtwh.iface.ISbglKfwhKcxlService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 *
 * @ClassName: INVO_New1SbglXtwhKcxl
 * @Description: (设备存量处理控制操作)
 * <AUTHOR> YK
 * @date 2020年9月12日 上午9:45:17
 *
 */
@Controller("INVO_New1SbglXtwhKcxl")
public class InvoSbglXtwhKcxlAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private ISbglKfwhKcxlService new1sbglKfwhKcxlService;

	// 参数有效性判断
	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		// 判断操作类型是否为空
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("InvoWzkfXtwhKcxlAction Interface| Requerst Parameter Validation Failed");
		}
	}

	// 业务处理
	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		// 操作类型
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		Wzb_kcxlModel kcxl = null;
		switch (optType) {
		// 查询
		case "query":
			kcxl = JSONObject.parseObject(json, Wzb_kcxlModel.class);
			if (kcxl == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoWzkfXtwhKcxlAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("kcxl", kcxl);
				new1sbglKfwhKcxlService.qureryCl(msg, result);
			}
			break;
		// 更新
		case "update":
            JSONObject obj = JSON.parseObject(json);
			List<Wzb_kcxlModel> li = JSONObject.parseArray(obj.get("list").toString(),Wzb_kcxlModel.class);
			if (li == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoWzkfXtwhKcxlAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("list", li);
				new1sbglKfwhKcxlService.upateCl(msg, result);
			}
			break;
		}

	}

}
