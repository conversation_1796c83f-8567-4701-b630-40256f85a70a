package com.jsg.web.business.service.xtwh.qxwh.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.jsg.common.pojo.ResultParam;
import com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel;
import com.jsg.csp.api.pubfun.service.IPubFunCspService;
import com.jsg.csp.api.xtwh.qxwh.pojo.Gyb_xtmkModel;
import com.jsg.csp.api.xtwh.qxwh.pojo.QxglXtmkModel;
import com.jsg.csp.api.xtwh.qxwh.pojo.QxglXtmkUseModel;
import com.jsg.csp.api.xtwh.qxwh.pojo.QxglYlqxModel;
import com.jsg.csp.api.xtwh.qxwh.service.IMkCspService;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.frame.service.BaseService;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.xtwh.qxwh.iface.IXtwhQxwhMkService;


/**
 *
* @ClassName: XtwhQxwhMkServiceImpl
* @Description: TODO(系统模块)
* <AUTHOR>
* @date 2020年5月22日 下午10:24:42
*
 */
@Service("new1xtwhQxwhMkService")
public class XtwhQxwhMkServiceImpl extends BaseService implements IXtwhQxwhMkService,LogicCodeConstants{

	private final Logger logger = LoggerFactory.getLogger(XtwhQxwhMkServiceImpl.class);

	@Autowired
	IMkCspService mkCspService;
	@Autowired
	IPubFunCspService pubFunCspService;
	/**
	 * 查询系统模块权限
	 */
	@Override
	public List<Gyb_xtmkModel> querygyb_xtgnmkList(UtilRequest msg, InvocationResult result) {
		try {
			UtilResponse utilResponse = mkCspService.querygyb_xtgnmkList(msg);
			if (isCspRetSuccess(utilResponse)) {
				logger.info("XtwhQxwhMkServiceImpl Interface| Result SUCCESS : XtwhQxwhMkServiceImpl |Result  "
						+ result.toString());
				List<Gyb_xtmkModel> list = (List<Gyb_xtmkModel>) utilResponse.getResResult().get("list");
				result.setD(list);
				result.setLogicCode(SUCCESS);
				return list;
			} else {
				result.setLogicCode(ERROR);
				logger.info("XtwhKsryBcfaServiceImpl Interface| Result FAILED: XtwhKsryBcfaServiceImpl |Result  "
						+ result.toString());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(LOGIN_ERROR);
			logger.error(
					"MainInfoServiceImpl Interface| Result FAILED: MainInfoServiceImpl |Result  " + result.toString());
			return null;
		}
	}
	/**
	 * 查询用例模块权限
	 */
	@Override
	public List<QxglYlqxModel> queryGyb_ylqxList(UtilRequest msg,InvocationResult result) {
		try {
			UtilResponse utilResponse = mkCspService.queryGyb_ylqxList(msg);
			if (isCspRetSuccess(utilResponse)) {
				logger.info("XtwhQxwhMkServiceImpl Interface| Result SUCCESS : XtwhQxwhMkServiceImpl |Result  "
						+ result.toString());
				List<QxglYlqxModel> list = (List<QxglYlqxModel>) utilResponse.getResResult().get("list");
				result.setD(list);
				result.setLogicCode(SUCCESS);
				return list;
			} else {
				result.setLogicCode(ERROR);
				logger.info("XtwhKsryBcfaServiceImpl Interface| Result FAILED: XtwhKsryBcfaServiceImpl |Result  "
						+ result.toString());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(LOGIN_ERROR);
			logger.error(
					"MainInfoServiceImpl Interface| Result FAILED: MainInfoServiceImpl |Result  " + result.toString());
			return null;
		}
	}
	/**
	 * 查询用户自定义系统模块
	 */
	@Override
	public ResultParam queryGyb_xtmkList(UtilRequest msg, InvocationResult result) {
		try {
			logger.info("XtwhQxwhMkServiceImpl Interface| Request Url : XtwhQxwhMkServiceImpl |Requerst Parameter "
					+ msg.toString());
			UtilResponse utilResponse = mkCspService.queryGyb_xtmkList(msg);
			if (isCspRetSuccess(utilResponse)) {
				logger.info("XtwhKsryZybmServiceImpl Interface| Result SUCCESS : XtwhKsryZybmServiceImpl |Result  "
						+ result.toString());
				PageInfo<QxglXtmkModel> pageInfo = (PageInfo<QxglXtmkModel>) utilResponse.getResResult().get("pageInfo");
				List<QxglXtmkModel> list = pageInfo.getList();
				ResultParam res = new ResultParam();
				res.setTotal((int)pageInfo.getTotal());
				res.setList(list);
				result.setD(res);
				result.setLogicCode(SUCCESS);
				return res;
			} else {
				result.setLogicCode(ERROR);
				logger.info("XtwhQxwhMkServiceImpl Interface| Result FAILED: XtwhQxwhMkServiceImpl |Result  "
						+ result.toString());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(LOGIN_ERROR);
			logger.error(
					"MainInfoServiceImpl Interface| Result FAILED: MainInfoServiceImpl |Result  " + result.toString());
			return null;
		}
	}
	/**
	 * 查询单条用户自定义模块
	 */
	@Override
	public QxglXtmkModel queryGyb_xtmkOne(String xtmkbm, InvocationResult result) {
		try {
			UtilResponse utilResponse = mkCspService.queryGyb_xtmkOne(xtmkbm);
			if (isCspRetSuccess(utilResponse)) {
				result.setD(utilResponse.getResResult().get("bean"));
				result.setLogicCode(SUCCESS);
				QxglXtmkModel bean = (QxglXtmkModel)utilResponse.getResResult().get("bean");
				return bean;
			} else {
				result.setLogicCode(ERROR);
				result.setC("职业查询失败!");
				logger.info("XtwhQxwhMkServiceImpl Interface| Result FAILED: XtwhQxwhMkServiceImpl |Result  "
						+ result.toString());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(
					"XtwhQxwhMkServiceImpl Interface| Result FAILED: XtwhQxwhMkServiceImpl |Result  " + result.toString());
			return null;
		}
	}
	/**
	 * 删除用户自定义模块信息
	 */
	@Override
	public int deletebatch(UtilRequest msg, InvocationResult result) {
		try {
			logger.info("XtwhQxwhMkServiceImpl Interface| Request Url : XtwhQxwhMkServiceImpl |Requerst Parameter "
					+ msg.toString());
			UtilResponse utilResponse = mkCspService.deletebatch(msg);
			if (isCspRetSuccess(utilResponse)) {
				String ref = (String)utilResponse.getResResult().get("ref");
				result.setD(ref);
				if (ref.equals("0")){
					result.setC("没有可删除的记录!");
				}
				logger.info("XtwhQxwhMkServiceImpl Interface| Result SUCCESS : XtwhQxwhMkServiceImpl |Result  "
						+ result.toString());
				result.setLogicCode(SUCCESS);
				return Integer.parseInt(ref);
			} else {
				result.setLogicCode(ERROR);
				result.setC(utilResponse.getResultMsg());
				logger.info("XtwhQxwhMkServiceImpl Interface| Result FAILED: XtwhQxwhMkServiceImpl |Result  "
						+ result.toString());
				return 0;
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(
					"XtwhKsryZybmServiceImpl Interface| Result FAILED: XtwhKsryZybmServiceImpl |Result  " + result.toString());
			return 0;
		}
	}
	/***
	 * 在系统模块中添加用户自定义信息
	 */
	@Override
	public int savebatch(UtilRequest msg, InvocationResult result) {
		try {
			QxglXtmkUseModel bean=(QxglXtmkUseModel) msg.getParam().get("bean");
			String mkbm = bean.getMkbm();
			if (mkbm == null || mkbm.equals("") || mkbm.equals("null")){
	        	GetMaxBmModel b = new GetMaxBmModel();
				b.setTablename("gyb_xtmk");
				b.setColumnname("mkbm");
				b.setLeninteger(4);
				msg.getParam().put("bean", b);
				UtilResponse res =  pubFunCspService.GetMaxBm(msg);
				String maxbm =  (String)res.getResResult().get("maxbm");
				bean.setMkbm("U"+maxbm);
			}
			msg.getParam().put("bean", bean);
			UtilResponse utilResponse = mkCspService.savebatch(msg);

			if (isCspRetSuccess(utilResponse)){
				String ref = (String)utilResponse.getResResult().get("ref");
				result.setD(ref);
				result.setLogicCode(SUCCESS);
				return Integer.parseInt(ref);
			}else{
				result.setLogicCode(ERROR);
				result.setC(utilResponse.getResultMsg());
				logger.info("XtwhKsryZybmServiceImpl Interface| Result FAILED: XtwhKsryZybmServiceImpl |Result  "
						+ result.toString());
				return 0;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setC("自定义模块提交失败!"+e.getCause().getMessage());
			logger.error("XtwhKsryZybmServiceImpl Interface| Result FAILED: XtwhKsryZybmServiceImpl |Result  " + result.toString());
			return 0;
		}
	}



}
