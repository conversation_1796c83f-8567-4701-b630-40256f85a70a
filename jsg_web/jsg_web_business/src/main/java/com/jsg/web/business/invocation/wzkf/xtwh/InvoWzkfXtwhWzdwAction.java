package com.jsg.web.business.invocation.wzkf.xtwh;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.jsg.common.util.RequestUtil;
import com.jsg.csp.api.wzkf.xtwh.pojo.Wzb_wzdwModel;
import com.jsg.frame.constants.IRequestConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.invocation.BaseInvocation;
import com.jsg.frame.invocation.InvocationContext;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.wzkf.xtwh.iface.IWzkfKfwhWzdwService;

/**
 *
 * @ClassName: InvoWzkfXtwhWzdwAction
 * @Description: TODO(物资单位控制操作)
 * <AUTHOR> YK
 * @date 2020年9月12日 下午10:31:40
 *
 */
@Controller("INVO_New1WzkfXtwhWzdw")
public class InvoWzkfXtwhWzdwAction extends BaseInvocation implements LogicCodeConstants, IRequestConstants {

	// 日志工具
	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private IWzkfKfwhWzdwService new1wzglKfwhWzdwService;

	// 参数校验
	@Override
	public void validateParam(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");// 操作类型
		if (StringUtils.isBlank(optType)) {
			result.setLogicCode(PARAM_VALIDATE_ERROR);
			logger.info("UserInfoAction Interface| Requerst Parameter Validation Failed");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void doService(InvocationContext invoContext, InvocationResult result) {
		String optType = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "types", "");
		String json = RequestUtil.getStrParamterAsDef(invoContext.getRequest(), "json", "");
		UtilRequest msg = (UtilRequest) this.createRequestMsg(UtilRequest.class, invoContext);
		Wzb_wzdwModel wzb = null;

		switch (optType) {
		// 保存
		case "save":
			wzb = RequestUtil.getObjParamter(invoContext.getRequest(), Wzb_wzdwModel.class);
			if (wzb == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", wzb);
				new1wzglKfwhWzdwService.saveOne(msg, result);
			}
			break;
		// 批量删除
		case "delete":
			List<Wzb_wzdwModel> list = (List<Wzb_wzdwModel>) RequestUtil.getListParamter(invoContext.getRequest(),
					Wzb_wzdwModel.class);
			if (list == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("beanList", list);
				new1wzglKfwhWzdwService.deletebatch(msg, result);
			}

			break;
		// 修改
		case "update":
			wzb = RequestUtil.getObjParamter(invoContext.getRequest(), Wzb_wzdwModel.class);
			if (wzb == null) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				msg.getParam().put("bean", wzb);
				new1wzglKfwhWzdwService.updateOne(msg, result);
			}
			break;
		// 查询
		case "query":
			if (json == null || "".equals(json)) {
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.info("InvoYfbKcglRkglAction Interface| Requerst Parameter Validation Failed");
			} else {
				wzb = JSONObject.parseObject(json, Wzb_wzdwModel.class);
				msg.getParam().put("bean", wzb);
				new1wzglKfwhWzdwService.queryList(msg, result);
			}
			break;

		}
	}

}
