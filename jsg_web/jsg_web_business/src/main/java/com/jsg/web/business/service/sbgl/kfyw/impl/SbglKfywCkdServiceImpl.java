package com.jsg.web.business.service.sbgl.kfyw.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsg.common.pojo.ResultParam;
import com.jsg.common.util.JsonUtil;
import com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.jsg.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.jsg.csp.api.pubfun.service.IPubFunCspService;
import com.jsg.csp.api.sbgl.kfyw.pojo.Sbgl_ckdModel;
import com.jsg.csp.api.sbgl.kfyw.pojo.Sbgl_ckdmxModel;
import com.jsg.csp.api.sbgl.kfyw.pojo.Sbgl_sbkcModel;
import com.jsg.csp.api.sbgl.kfyw.service.ISbgl_ckdCspService;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.csp.pojo.UserInfoModel;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.frame.service.BaseService;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.sbgl.kfyw.iface.ISbglKfywCkdService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 * @ClassName: SbglKfywCkdServiceImpl
 * @Description: TODO(设备出库业务操作)
 * <AUTHOR>
 * @date 2020年9月14日 下午9:30:25
 *
 */
@Service("new1SbglKfywCkdService")
public class SbglKfywCkdServiceImpl extends BaseService implements ISbglKfywCkdService, LogicCodeConstants {

	// 日志工具
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private ISbgl_ckdCspService sbgl_ckdCspService;

	@Autowired
	private IPubFunCspService pubFunCspService;

	// 查询出库设备列表
	@SuppressWarnings("unchecked")
	@Override
	public ResultParam selectWzList(UtilRequest msg, InvocationResult result) {
		try {
			Sbgl_sbkcModel bean = (Sbgl_sbkcModel) msg.getParam().get("bean");
			// 参数校验
			if (bean.getSbkf() == null || "".equals(bean.getSbkf())) {
				result.setC("设备库房不能为空!");
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.error("SbglKfywCkdServiceImpl selectWzList| Requerst Parameter Validation Failed");
				return null;
			}

			msg.getParam().put("bean", bean);

			UtilResponse utilResponse = sbgl_ckdCspService.queryWzList(msg);
			// 校验结果
			if (isCspRetSuccess(utilResponse)) {
				if (isCspRetSuccess(utilResponse)) {
					logger.info("SbglKfywCkdServiceImpl Interface| Result SUCCESS : selectCkd |Result  "
							+ result.toString());
					PageInfo<Sbgl_sbkcModel> pageInfo = (PageInfo<Sbgl_sbkcModel>) utilResponse.getResResult()
							.get("list");
					ResultParam res = new ResultParam();
					res.setList(pageInfo.getList());
					res.setTotal((int) pageInfo.getTotal());
					result.setD(res);
					result.setLogicCode(SUCCESS);
					return res;
				} else {
					result.setLogicCode(ERROR);
					logger.info(
							"SbglKfywCkdServiceImpl Interface| Result FAILED: selectCkd |Result  " + result.toString());
					return null;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(ERROR);
			logger.error("SbglKfywCkdServiceImpl Interface| Result FAILED: selectCkd |Result  " + result.toString());
			return null;
		}

		return null;
	}

	// 查询出库单
	@SuppressWarnings("unchecked")
	@Override
	public List<Sbgl_ckdModel> selectCkd(UtilRequest msg, InvocationResult result) {
		try {
			Sbgl_ckdModel bean = (Sbgl_ckdModel) msg.getParam().get("bean");
			// 参数校验
			if (bean.getSbkf() == null || "".equals(bean.getSbkf())) {
				result.setC("设备库房不能为空!");
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.error("SbglKfywCkdServiceImpl selectCkd| Requerst Parameter Validation Failed");
				return null;
			}

			UtilResponse utilResponse = sbgl_ckdCspService.queryCkd(msg);
			// 校验结果
			if (isCspRetSuccess(utilResponse)) {
				if (isCspRetSuccess(utilResponse)) {
					logger.info("SbglKfywCkdServiceImpl Interface| Result SUCCESS : selectCkd |Result  "
							+ result.toString());
					ResultParam res = new ResultParam();
					PageInfo<Sbgl_ckdModel> pageInfo = (PageInfo<Sbgl_ckdModel>) utilResponse.getResResult().get("pageInfo");
					List<Sbgl_ckdModel> list = pageInfo.getList();
					res.setList(list);
					res.setTotal((int) pageInfo.getTotal());
					result.setD(res);
					result.setLogicCode(SUCCESS);
					return list;
				} else {
					result.setLogicCode(ERROR);
					logger.info(
							"SbglKfywCkdServiceImpl Interface| Result FAILED: selectCkd |Result  " + result.toString());
					return null;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(ERROR);
			logger.error("SbglKfywCkdServiceImpl Interface| Result FAILED: selectCkd |Result  " + result.toString());
			return null;
		}

		return null;
	}

	// 查询出库单明细
	@SuppressWarnings("unchecked")
	@Override
	public List<Sbgl_ckdmxModel> selectCkdmx(UtilRequest msg, InvocationResult result) {
		try {
			Sbgl_ckdModel bean = (Sbgl_ckdModel) msg.getParam().get("bean");
			// 参数校验
			if (bean.getSbkf() == null || "".equals(bean.getSbkf())) {
				result.setC("设备库房不能为空!");
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.error("SbglKfywCkdServiceImpl selectCkdmx| Requerst Parameter Validation Failed");
				return null;
			}

			UtilResponse utilResponse = sbgl_ckdCspService.queryCkdMx(msg);
			if (isCspRetSuccess(utilResponse)) {
				if (isCspRetSuccess(utilResponse)) {
					logger.info("SbglKfywCkdServiceImpl Interface| Result SUCCESS : selectCkdmx |Result  "
							+ result.toString());
					List<Sbgl_ckdmxModel> list = (List<Sbgl_ckdmxModel>) utilResponse.getResResult().get("list");
					result.setD(list);
					result.setLogicCode(SUCCESS);
					return list;
				} else {
					result.setLogicCode(ERROR);
					logger.info("SbglKfywCkdServiceImpl Interface| Result FAILED: selectCkdmx |Result  "
							+ result.toString());
					return null;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(ERROR);
			logger.error("SbglKfywCkdServiceImpl Interface| Result FAILED: selectCkdmx |Result  " + result.toString());
			return null;
		}

		return null;
	}

	// 保存出库单
	@Override
	public int insertCkd(UtilRequest msg, InvocationResult result) {
		try {
			// 获取参数
			JSONObject object = (JSONObject) msg.getParam().get("bean");

			JSONObject obj = object.getJSONObject("ckd");
			JSONArray list = object.getJSONArray("ckdmx");
			String kfdw = object.getString("sbkf");
			Sbgl_ckdModel bean = JsonUtil.JsonToClass(obj, Sbgl_ckdModel.class);
			List<Sbgl_ckdmxModel> beanList = (List<Sbgl_ckdmxModel>) JsonUtil.JsonToArray(list, Sbgl_ckdmxModel.class);

			// 设备库房校验
			if (bean.getSbkf() == null || "".equals(bean.getSbkf())) {
				result.setC("设备库房不能为空!");
				result.setLogicCode(PARAM_VALIDATE_ERROR);
				logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
				return 0;
			}

			// 出库单号, 单号为空自动生成
			GetXhidResModel xhId = new GetXhidResModel();
			if (bean.getCkdh() == null || "".equals(bean.getCkdh())) {
				GetXhidMsgModel b = new GetXhidMsgModel();
				b.setKfbm(bean.getSbkf());
				b.setCslx("CK");
				b.setScfs("2");// 按月生成
				msg.getParam().put("bean", b);
				UtilResponse res = pubFunCspService.GetWzPzh(msg);
				xhId = (GetXhidResModel) res.getResResult().get("bean");
				bean.setCkdh(xhId.getDqxh());
			}

			bean.setLyyf(obj.getString("ksbm"));
			bean.setLyks(obj.getString("ksbm"));
			// 01-出库，02-退货 03-报损 04-盘出库库
			bean.setCkfs("01");

			// 制单人
			UserInfoModel userinfo = (UserInfoModel) msg.getUserinfo().get("userinfo");
			bean.setZdr(userinfo.czybm);

			// 审核作废标志 0-未审，1-已审，2-作废
			bean.setShzfbz("0");

			// 审核作废标志 0-未审，1-已审，2-作废
			bean.setKfshbz("0");

			// 制单日期
			bean.setZdrq(new Date());

			// 设置是否打印标志
			bean.setIsprint("0");// 是否打印 0-否，1-是

			// 校验出库单明细
			for (int i = 0; i < beanList.size(); i++) {
				Sbgl_ckdmxModel tempCkd = beanList.get(i);
//				tempCkd.setKfdw(kfdw);
				// 有效期至
				if (tempCkd.getYxqz() == null || "".equals(tempCkd.getYxqz())) {
					result.setC("设备有效期不能为空!");
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
					return 0;
				}
				// 出库数量
				if (tempCkd.getCksl() == 0) {
					result.setC("出库数量不能为0!");
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
					return 0;
				}
				// 进价
				if (tempCkd.getJj() == 0) {
					result.setC("设备进价不能为0!");
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
					return 0;
				}
//				// 单价
//				if (tempCkd.getDj() == 0) {
//					result.setC("设备单价不能为0!");
//					result.setLogicCode(PARAM_VALIDATE_ERROR);
//					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
//					return 0;
//				}
//				// 领用单位
//				if (tempCkd.getLydw() == null || "".equals(tempCkd.getLydw())) {
//					result.setC("领用单位不能为空!");
//					result.setLogicCode(PARAM_VALIDATE_ERROR);
//					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
//					return 0;
//				}
				// 库房单位
				if (tempCkd.getKfdw() == null || "".equals(tempCkd.getKfdw())) {
					result.setC("库房单位不能为空!");
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
					return 0;
				}
				// 药品批号
				if (tempCkd.getScph() == null || "".equals(tempCkd.getScph())) {
					result.setC("设备生产批号不能为空!");
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
					return 0;
				}
				// 设备编码
				if (tempCkd.getSbbm() == null || "".equals(tempCkd.getSbbm())) {
					result.setC("设备编码不能为空!");
					result.setLogicCode(PARAM_VALIDATE_ERROR);
					logger.error("UserInfoAction Interface| Requerst Parameter Validation Failed");
					return 0;
				}
				// 设置明细参数
				// 出库单号
				beanList.get(i).setCkdh(xhId.getDqxh());
				// 明细序号
				beanList.get(i).setMxxh(i + 1);

			}
//			msg.getParam().put("kfdw", kfdw);
			msg.getParam().put("dj", bean);
			msg.getParam().put("djmx", beanList);
			//报损之前先判断在途库存
			UtilResponse utilRespons = sbgl_ckdCspService.checkZtBs(msg);
			if(isCspRetSuccess(utilRespons)) {
				//如果是根据科室申领单直接开出库单需要先判断是否已经开出库单
				UtilResponse utilRespon = new UtilResponse ();
				if (bean.getSldh()!=null && !"".equals(bean.getSldh())){
					 utilRespon = sbgl_ckdCspService.checkSFkd(msg);
				}
				if (isCspRetSuccess(utilRespon)) {
					UtilResponse utilResponse = sbgl_ckdCspService.addCkd(msg);

					// 结果校验
					if (isCspRetSuccess(utilResponse)) {
						if (isCspRetSuccess(utilResponse)) {
							logger.info("SbglKfywCkdServiceImpl Interface| Result SUCCESS : insertCkd |Result  "
									+ result.toString());
							int ref = (int) utilResponse.getResResult().get("ref");
							result.setD(ref);
							result.setLogicCode(SUCCESS);
							return 1;
						} else {
							result.setLogicCode(ERROR);
							result.setC(utilResponse.getResultMsg());
							logger.error("SbglKfywCkdServiceImpl Interface| Result FAILED: WzkfKfywCkdServiceImpl |Result  "
									+ result.toString());
							return 0;
						}
					}
				}else {
					result.setC(utilRespon.getResultMsg());
					result.setLogicCode(ERROR);
					logger.error("SbglKfywCkdServiceImpl Interface| Result FAILED: WzkfKfywSldServiceImpl |Result  "
							+ result.toString());
					return 0;
				}
			}else {
				result.setC(utilRespons.getResultMsg());
				result.setLogicCode(ERROR);
				logger.error("SbglKfywCkdServiceImpl Interface| Result FAILED: WzkfKfywSldServiceImpl |Result  "
						+ result.toString());
				return 0;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(ERROR);
			logger.error(
					"MainInfoServiceImpl Interface| Result FAILED: MainInfoServiceImpl |Result  " + result.toString());
		}
		return 0;
	}

	// 作废出库单
	@Override
	public int cancelCkd(UtilRequest msg, InvocationResult result) {
		try {
			Sbgl_ckdModel bean = (Sbgl_ckdModel) msg.getParam().get("bean");
			// 参数校验
			if (bean.getSbkf() == null || "".equals(bean.getSbkf())) {
				result.setLogicCode(ERROR);
				result.setC("设备库房不能为空!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: cancelCkd |Result  " + result.toString());
				return 0;
			}
			if (bean.getCkdh() == null || "".equals(bean.getCkdh())) {
				result.setLogicCode(ERROR);
				result.setC("出库单号不能为空!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: cancelCkd |Result  " + result.toString());
				return 0;
			}
			// 校验审核状态
			if (bean.getShzfbz() == null || "1".equals(bean.getShzfbz())) {
				result.setLogicCode(ERROR);
				result.setC("出库单已审核!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: cancelCkd |Result  " + result.toString());
				return 0;
			}
			if (bean.getShzfbz() == null || "2".equals(bean.getShzfbz())) {
				result.setLogicCode(ERROR);
				result.setC("出库单已作废!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: cancelCkd |Result  " + result.toString());
				return 0;
			}

			UtilResponse utilResponse = sbgl_ckdCspService.invaldCkd(msg);
			// 校验结果
			if (isCspRetSuccess(utilResponse)) {
				if (isCspRetSuccess(utilResponse)) {
					logger.info("SbglKfywCkdServiceImpl Interface| Result SUCCESS : cancelCkd |Result  "
							+ result.toString());
					int ref = (int) utilResponse.getResResult().get("ref");
					result.setD(ref);
					result.setLogicCode(SUCCESS);
					return 1;
				} else {
					result.setLogicCode(ERROR);
					logger.info("SbglKfywCkdServiceImpl Interface| Result FAILED: WzkfKfywCkdServiceImpl |Result  "
							+ result.toString());
					return 0;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(ERROR);
			logger.error(
					"MainInfoServiceImpl Interface| Result FAILED: MainInfoServiceImpl |Result  " + result.toString());
		}

		return 0;
	}

	// 审核出库单
	@Override
	public int confirmCkd(UtilRequest msg, InvocationResult result) {
		try {
			Sbgl_ckdModel bean = (Sbgl_ckdModel) msg.getParam().get("bean");
			// 参数校验
			if (bean.getSbkf() == null || "".equals(bean.getSbkf())) {
				result.setLogicCode(ERROR);
				result.setC("设备库房不能为空!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: confirmCkd |Result  " + result.toString());
				return 0;
			}
			if (bean.getCkdh() == null || "".equals(bean.getCkdh())) {
				result.setLogicCode(ERROR);
				result.setC("出库单号不能为空!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: confirmCkd |Result  " + result.toString());
				return 0;
			}
			// 校验审核状态
			if (bean.getShzfbz() == null || "1".equals(bean.getShzfbz())) {
				result.setLogicCode(ERROR);
				result.setC("出库单已审核!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: confirmCkd |Result  " + result.toString());
				return 0;
			}
			if (bean.getShzfbz() == null || "2".equals(bean.getShzfbz())) {
				result.setLogicCode(ERROR);
				result.setC("出库单已作废!");
				logger.error(
						"SbglKfywCkdServiceImpl Interface| Result FAILED: confirmCkd |Result  " + result.toString());
				return 0;
			}

			UtilResponse utilResponse = sbgl_ckdCspService.passCkd(msg);
			// 校验结果
			if (isCspRetSuccess(utilResponse)) {
				if (isCspRetSuccess(utilResponse)) {
					logger.info("SbglKfywCkdServiceImpl Interface| Result SUCCESS : confirmCkd |Result  "
							+ result.toString());
					int ref = (int) utilResponse.getResResult().get("ref");
					result.setD(ref);
					result.setLogicCode(SUCCESS);
					return 1;
				} else {
					result.setLogicCode(ERROR);
					logger.info("SbglKfywCkdServiceImpl Interface| Result FAILED: WzkfKfywCkdServiceImpl |Result  "
							+ result.toString());
					return 0;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setLogicCode(ERROR);
			logger.error(
					"MainInfoServiceImpl Interface| Result FAILED: MainInfoServiceImpl |Result  " + result.toString());
		}

		return 0;
	}

}
