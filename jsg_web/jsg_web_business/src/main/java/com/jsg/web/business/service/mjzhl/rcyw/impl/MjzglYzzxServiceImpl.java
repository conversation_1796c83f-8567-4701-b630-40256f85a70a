package com.jsg.web.business.service.mjzhl.rcyw.impl;

import com.jsg.common.pojo.ResultParam;
import com.jsg.csp.api.mjzhl.rcyw.pojo.Mjzgl_YzzxModel;
import com.jsg.csp.api.mjzhl.rcyw.pojo.Mjzgl_YzzxdxxModel;
import com.jsg.csp.api.mjzhl.rcyw.service.IMjzgl_LgdjCspService;
import com.jsg.csp.api.mjzhl.rcyw.service.IMjzgl_YzzxCspService;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.invocation.InvocationResult;
import com.jsg.frame.service.BaseService;
import com.jsg.web.business.constants.LogicCodeConstants;
import com.jsg.web.business.service.mjzhl.rcyw.iface.IMjzglYzzxService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("new1mjzglYzzxService")
public class MjzglYzzxServiceImpl extends BaseService implements IMjzglYzzxService, LogicCodeConstants {

    private final Logger logger = LoggerFactory.getLogger(MjzglYzzxServiceImpl.class);

    @Autowired
    private IMjzgl_YzzxCspService mjzgl_yzzxCspService;

    @Autowired
    private IMjzgl_LgdjCspService mjzgl_lgdjCspService;

    @Override
    public List<Mjzgl_YzzxdxxModel> queryYzzxdList(UtilRequest msg, InvocationResult result) {
        try {

//            UtilResponse lgdjRes = mjzgl_lgdjCspService.QueryLgdj(msg);
//            List<Mjzgl_LgdjModel> lgdjList = (List<Mjzgl_LgdjModel>) lgdjRes.getResResult().get("list");

            UtilResponse response = mjzgl_yzzxCspService.queryYzzxd(msg);
            if (isCspRetSuccess(response)){
                logger.info("MjzglYzzxServiceImpl Interface | Result SUCCESS : queryYzzxdList |Result "
                            +result.toString());
                List<Mjzgl_YzzxdxxModel> list = (List<Mjzgl_YzzxdxxModel>) response.getResResult().get("list");
                ResultParam res = new ResultParam();
                res.setTotal(list.size());
                res.setList(list);
                result.setLogicCode(SUCCESS);
                result.setD(res);
                return list;
            }else {
                result.setLogicCode(ERROR);
                result.setC(response.getResultMsg());
                logger.info("MjzglYzzxServiceImpl Interface| Result FAILED: queryYzzxdList |Result  "
                        + result.toString());
                return null;
            }
        }catch (Exception e){
            result.setLogicCode(ERROR);
            return null;
        }
    }

    @Override
    public List<Mjzgl_YzzxModel> querySytList(UtilRequest msg, InvocationResult result) {
        try {
            UtilResponse response = mjzgl_yzzxCspService.querySytList(msg);
            if (isCspRetSuccess(response)){
                logger.info("MjzglYzzxServiceImpl Interface | Result SUCCESS : querySytList |Result "
                        +result.toString());
                List<Mjzgl_YzzxModel> list = (List<Mjzgl_YzzxModel>) response.getResResult().get("list");
                ResultParam res = new ResultParam();
                res.setTotal(list.size());
                res.setList(list);
                result.setLogicCode(SUCCESS);
                result.setD(res);
                return list;
            }else {
                result.setLogicCode(ERROR);
                result.setC(response.getResultMsg());
                logger.info("MjzglYzzxServiceImpl Interface| Result FAILED: querySytList |Result  "
                        + result.toString());
                return null;
            }
        }catch (Exception e){
            result.setLogicCode(ERROR);
            return null;
        }
    }
}
