<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsg.csp.xtwh.ylfwxm.dao.New1Yfb_cflxModelMapper" >
  <resultMap id="BaseResultMap" type="com.jsg.csp.api.xtwh.ylfwxm.pojo.Yfb_cflxModel" >
    <id column="CFLXBM" property="cflxbm" jdbcType="VARCHAR" />
    <result column="CFLXMC" property="cflxmc" jdbcType="VARCHAR" />
    <result column="CFLB" property="cflb" jdbcType="VARCHAR" />
    <result column="DZF" property="dzf" jdbcType="DECIMAL" />
    <result column="MZSFKM" property="mzsfkm" jdbcType="VARCHAR" />
    <result column="ZYSFKM" property="zysfkm" jdbcType="VARCHAR" />
    <result column="SSWRZ" property="sswrz" jdbcType="VARCHAR" />
    <result column="SSZ" property="ssz" jdbcType="DECIMAL" />
    <result column="YPWS" property="ypws" jdbcType="DECIMAL" />
    <result column="CFZDTS" property="cfzdts" jdbcType="DECIMAL" />
    <result column="CFZDJE" property="cfzdje" jdbcType="DECIMAL" />
    <result column="TYBZ" property="tybz" jdbcType="VARCHAR" />
    <result column="MZSFKMMC" property="mzsfkmmc" jdbcType="VARCHAR" />
    <result column="ZYSFKMMC" property="zysfkmmc" jdbcType="VARCHAR" />
    <result column="FYXMBM" property="fyxmbm" jdbcType="VARCHAR" />
    <result column="FYXMMC" property="fyxmmc" jdbcType="VARCHAR" />
    <result column="MBMC" property="mbmc" jdbcType="VARCHAR" />
    <result column="DMCFBZ" property="dmcfbz" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    CFLXBM, CFLXMC, CFLB, DZF, MZSFKM, ZYSFKM, SSWRZ, SSZ, YPWS, CFZDTS, CFZDJE, TYBZ,FYXMBM.FYXMMC, MBMC, DMCFBZ
  </sql>

  <!-- 查询单条记录 -->
  <select id="queryYfb_cflxOne4Mem" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.xtwh.ylfwxm.pojo.Yfb_cflxModel" >
    select
    <include refid="Base_Column_List" />
    from YFB_CFLX
    where yljgbm = #{yljgbm,jdbcType=VARCHAR}
    and CFLXBM = #{cflxbm,jdbcType=VARCHAR}
  </select>

  <!-- 分页查询 -->
  <select id="queryYfb_cflx4Mem" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.xtwh.ylfwxm.pojo.Yfb_cflxModel">
  	select cflx.*,mzsfmxfy.mxfymc mzsfkmmc,zysfmxfy.mxfymc zysfkmmc
  	from YFB_CFLX cflx
	left join gyb_mxfyxm mzsfmxfy on mzsfmxfy.mxfybm=cflx.mzsfkm and mzsfmxfy.yljgbm=cflx.yljgbm
	left join gyb_mxfyxm zysfmxfy on zysfmxfy.mxfybm=cflx.zysfkm and zysfmxfy.yljgbm=cflx.yljgbm
    <where>
    	cflx.yljgbm = #{yljgbm,jdbcType=VARCHAR}
    <if test="parm != null and parm != '' ">
    	and (cflx.CFLXMC like '%'||#{parm,jdbcType=VARCHAR}||'%' or cflx.CFLXBM like #{parm,jdbcType=VARCHAR}||'%')
    </if>
    <if test="tybz != null and tybz != '' ">
    	and (cflx.TYBZ = #{tybz,jdbcType=VARCHAR})
    </if>
    </where>
    ORDER BY  ${sort} ${order}
  </select>

  <!-- 批量删除 -->
  <delete id="deletebatch3Mem" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
    delete from YFB_CFLX WHERE yljgbm= #{item.yljgbm,jdbcType=VARCHAR} and CFLXBM=
        #{item.cflxbm,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <!-- 保存 -->
  <insert id="save3Mem" parameterType="com.jsg.csp.api.xtwh.ylfwxm.pojo.Yfb_cflxModel">
  	MERGE INTO YFB_CFLX T1
  	USING
  	(
  	  SELECT #{cflxbm,jdbcType=VARCHAR} CFLXBM, #{cflxmc,jdbcType=VARCHAR} CFLXMC,
  	  #{cflb,jdbcType=VARCHAR} CFLB,#{dzf,jdbcType=DECIMAL} DZF,
  	  #{mzsfkm,jdbcType=DECIMAL} MZSFKM,#{zysfkm,jdbcType=VARCHAR} ZYSFKM,
  	  #{sswrz,jdbcType=VARCHAR} SSWRZ,#{ssz,jdbcType=DECIMAL} SSZ,
  	  #{ypws,jdbcType=DECIMAL} YPWS,#{cfzdts,jdbcType=DECIMAL} CFZDTS,
  	  #{cfzdje,jdbcType=DECIMAL} CFZDJE,#{tybz,jdbcType=VARCHAR} TYBZ,
  	  #{fyxmbm,jdbcType=VARCHAR} FYXMBM,#{fyxmmc,jdbcType=VARCHAR} FYXMMC,
  	  #{mbmc,jdbcType=VARCHAR} MBMC,#{dmcfbz,jdbcType=VARCHAR} DMCFBZ,
  	  #{yljgbm,jdbcType=VARCHAR} YLJGBM
      FROM DUAL
  	) T2
  	ON (T1.CFLXBM = T2.CFLXBM AND T1.YLJGBM=T2.YLJGBM)
  	WHEN MATCHED THEN
  	  	UPDATE
	    SET T1.CFLXMC = T2.CFLXMC,
	      T1.CFLB = T2.CFLB,
	      T1.DZF = T2.DZF,
	      T1.MZSFKM = T2.MZSFKM,
	      T1.ZYSFKM = T2.ZYSFKM,
	      T1.SSWRZ = T2.SSWRZ,
	      T1.SSZ = T2.SSZ,
	      T1.YPWS = T2.YPWS,
	      T1.CFZDTS = T2.CFZDTS,
	      T1.CFZDJE = T2.CFZDJE,
	      t1.TYBZ=T2.TYBZ,
	      T1.FYXMBM = T2.FYXMBM,
	      T1.FYXMMC = T2.FYXMMC,
          T1.MBMC = T2.MBMC,
          T1.DMCFBZ = T2.DMCFBZ
  	WHEN NOT MATCHED THEN
  		insert (CFLXBM, CFLXMC, CFLB, DZF, MZSFKM, ZYSFKM,
  		SSWRZ, SSZ, YPWS, CFZDTS, CFZDJE, TYBZ,YLJGBM,FYXMBM,FYXMMC,MBMC,DMCFBZ)
	    values (T2.CFLXBM, T2.CFLXMC, T2.CFLB, T2.DZF,
	     T2.MZSFKM, T2.ZYSFKM, T2.SSWRZ, T2.SSZ,
	     T2.YPWS, T2.CFZDTS, T2.CFZDJE, T2.TYBZ, T2.YLJGBM,T2.FYXMBM,T2.FYXMMC,T2.MBMC,T2.DMCFBZ)
  </insert>

  <!-- @yqq 保存处方时判断是否使用袋子费 -->
  <select id="queryDzf" resultType="com.jsg.csp.api.xtwh.ylfwxm.pojo.Yfb_cflxAndMxfyxmDto" parameterType="com.jsg.csp.api.xtwh.ylfwxm.pojo.Yfb_cflxModel" >
   	SELECT
    	yc.*,gm.*,fylb.*,ksbm.*
	FROM
	yfb_cflx yc inner join gyb_mxfyxm gm on yc.FYXMBM = gm.mxfybm and yc.yljgbm=gm.yljgbm
	INNER join gyb_fylb fylb on gm.lbbm = fylb.LBBM and gm.yljgbm=fylb.yljgbm
	INNER join gyb_ksbm ksbm  on gm.hsks = ksbm.ksbm and gm.yljgbm=ksbm.yljgbm
	where
    yc.CFLXBM = #{cflxbm,jdbcType=VARCHAR} and yc.yljgbm=#{yljgbm}
  </select>
</mapper>
