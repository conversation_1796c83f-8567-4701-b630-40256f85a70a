<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsg.csp.mzys.zlgl.dao.New1Mz_blmbModelMapper" >

  <!-- 查询集合 -->
  <select id="query" resultType="com.jsg.csp.api.mzys.zlgl.pojo.Mz_blmbModel"
  parameterType="com.jsg.csp.api.mzys.zlgl.pojo.Mz_blmbModel">
  	select * from MZ_BLMB
    <where>
    	yljgbm = #{yljgbm,jdbcType=VARCHAR}
    <if test="pydm != null and pydm != '' ">
    	and pydm = #{pydm,jdbcType=VARCHAR}
    </if>
    <if test="lx != null and lx != '' ">
    	and lx = #{lx,jdbcType=VARCHAR}
    </if>
    <if test="czybm != null and czybm != '' ">
    	and czybm = #{czybm,jdbcType=VARCHAR}
    </if>
    <if test="parm!=null and parm!=''.toString()">
			and (pydm like '%${parm}%'
			or mc like '%'|| #{parm,jdbcType=VARCHAR} ||'%' )
	</if>
    </where>
     ORDER BY cjrq desc
  </select>

  <!-- 批量删除 -->
  <delete id="delete" parameterType="java.util.List" >
  	<foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
	    delete from MZ_BLMB
	    where YLJGBM = #{item.yljgbm,jdbcType=VARCHAR}
	      and id = #{item.id,jdbcType=VARCHAR}
      </foreach>
  </delete>

  <!-- 保存 -->
  <insert id="save" parameterType="com.jsg.csp.api.mzys.zlgl.pojo.Mz_blmbModel">
  	MERGE INTO MZ_BLMB T1
  	USING
  	(
  	  SELECT
  	   <if test="id != null" >
  	   #{id,jdbcType=VARCHAR} ID,
  	   </if>
  	  <if test="pydm != null" >
  	   #{pydm,jdbcType=VARCHAR} PYDM,
  	   </if>
  	   <if test="lx != null" >
  	  #{lx,jdbcType=VARCHAR} LX,
  	  </if>
  	  <if test="zs != null" >
  	   #{zs,jdbcType=VARCHAR} ZS,
  	   </if>
  	   <if test="xbs != null" >
  	  #{xbs} XBS,
  	  </if>
  	  <if test="jws != null" >
  	  #{jws,jdbcType=VARCHAR} JWS ,
  	  </if>
  	  <if test="zyzztz != null" >
  	 #{zyzztz} ZYZZTZ,
  	 </if>
  	 <if test="gms != null" >
  	  #{gms} GMS ,
  	  </if>
  	  <if test="jzs != null" >
  	 #{jzs,jdbcType=VARCHAR} JZS,
  	 </if>
  	 <if test="czybm != null" >
  	 #{czybm,jdbcType=VARCHAR} CZYBM,
  	 </if>
  	 <if test="cjrq != null" >
  	 #{cjrq} CJRQ,
  	 </if>
      <if test="dycl != null" >
          #{dycl} DYCL,
      </if>
  	  #{yljgbm,jdbcType=VARCHAR} YLJGBM,
  	  #{mc} MC
      FROM DUAL
  	) T2
  	ON (T1.id = T2.id AND T1.YLJGBM=T2.YLJGBM)
  	WHEN MATCHED THEN
  	  	UPDATE
	    SET
      <if test="pydm != null" >
		    T1.PYDM = T2.PYDM,
		    </if>
      <if test="lx != null" >
		    T1.LX = T2.LX,
		    </if>
      <if test="zs != null" >
		    T1.ZS =  T2.ZS,
		    </if>
      <if test="xbs != null" >
		    T1.XBS = T2.XBS,
		    </if>
      <if test="jws != null" >
		    T1.JWS = T2.JWS,
		    </if>
      <if test="zyzztz != null" >
		    T1.ZYZZTZ = T2.ZYZZTZ,
		    </if>
      <if test="gms != null" >
		    T1.GMS = T2.GMS,
		    </if>
      <if test="jzs != null" >
		    T1.JZS  = T2.JZS,
		    </if>
      <if test="czybm != null" >
		    T1.CZYBM = T2.CZYBM,
		    </if>
      <if test="cjrq != null" >
		    T1.CJRQ  = T2.CJRQ,
		    </if>
      <if test="dycl != null" >
          T1.DYCL  = T2.DYCL,
      </if>
		    T1.MC = T2.MC

  	WHEN NOT MATCHED THEN
  		insert (
	<if test="id != null" >
        ID,
      </if>
      <if test="pydm != null" >
        PYDM,
      </if>
      <if test="lx != null" >
        LX,
      </if>
      <if test="zs != null" >
        ZS,
      </if>
      <if test="xbs != null" >
        XBS,
      </if>
      <if test="jws != null" >
        JWS,
      </if>
      <if test="zyzztz != null" >
        ZYZZTZ,
      </if>
      <if test="gms != null" >
        GMS,
      </if>
      <if test="jzs != null" >
        JZS,
      </if>
      <if test="czybm != null" >
        CZYBM,
      </if>
      <if test="cjrq != null" >
        CJRQ,
      </if>
      <if test="dycl != null" >
          DYCL,
      </if>
        YLJGBM,mc
)
	    values (
	  <if test="id != null" >
        T2.ID,
      </if>
      <if test="pydm != null" >
        T2.PYDM,
      </if>
      <if test="lx != null" >
        T2.LX,
      </if>
      <if test="zs != null" >
        T2.ZS,
      </if>
      <if test="xbs != null" >
        T2.XBS,
      </if>
      <if test="jws != null" >
        T2.JWS,
      </if>
      <if test="zyzztz != null" >
        T2.ZYZZTZ,
      </if>
      <if test="gms != null" >
        T2.GMS,
      </if>
      <if test="jzs != null" >
        T2.JZS,
      </if>
      <if test="czybm != null" >
        T2.CZYBM,
      </if>
      <if test="cjrq != null" >
        T2.CJRQ,
      </if>
      <if test="dycl != null" >
          T2.DYCL,
      </if>
        T2.YLJGBM,T2.mc)
  </insert>


</mapper>
