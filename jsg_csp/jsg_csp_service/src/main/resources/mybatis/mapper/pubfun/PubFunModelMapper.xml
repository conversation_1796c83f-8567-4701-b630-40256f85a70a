<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.jsg.csp.pubfun.dao.PubFunModelMapper">
<resultMap id="BaseResultMap" type="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidResModel" >
    <id column="xhlx" property="cslx" jdbcType="VARCHAR" />
    <result column="dqxh" property="dqxh" jdbcType="VARCHAR" />
    <result column="xtrq" property="dqrq" jdbcType="TIMESTAMP" />
    <result column="scfs" property="scfs" jdbcType="VARCHAR" />
  </resultMap>
<!-- 获取最大编码 -->
<select id="GetMaxYljgbm" resultType="java.lang.String" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel">
	select ltrim(rtrim(max(to_number(${columnname})))) maxbm from ${tablename}
	where trim(translate(${columnname},'0123456789',' ')) is null
</select>
<select id="GetMaxBm" resultType="java.lang.String" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel">
	select ltrim(rtrim(max(to_number(${columnname})))) maxbm from ${tablename}
	where regexp_instr(${columnname},'[0-9]+') = 1
	and trim(translate(${columnname},'0123456789',' ')) is null and yljgbm =  #{yljgbm,jdbcType=VARCHAR}
    <if test="sfzdcd != null and sfzdcd != ''.toString()">
        and length(${columnname}) = #{leninteger}
    </if>
</select>

<select id="GetMaxBmLstdxh" resultType="java.lang.String" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel">
	select ltrim(rtrim(max(to_number(${columnname})))) maxbm from ${tablename}
	where trim(translate(${columnname},'0123456789',' ')) is null  and ghrq >= to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')
	and yljgbm =  #{yljgbm,jdbcType=VARCHAR}
</select>

<select id="GetMaxFrylbm" resultType="java.lang.String" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel">
	select ltrim(rtrim(max(to_number(${columnname})))) maxbm from ${tablename}
	where trim(translate(${columnname},'0123456789',' ')) is null and mkbm =  #{mkbm,jdbcType=VARCHAR}
</select>
<!-- and types='3' and ver='0' -->

<select id="GetMaxFrylbmNew" resultType="java.lang.String" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel">
	select ltrim(rtrim(max(to_number(${columnname})))) maxbm from ${tablename}
	where trim(translate(${columnname},'0123456789',' ')) is null and mkbm =  #{mkbm,jdbcType=VARCHAR} and types='3' and ver='1'
</select>


<select id="GetMaxBmAll" resultType="java.lang.String" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel">
	select ltrim(rtrim(max(to_number(${columnname})))) maxbm from ${tablename}
	where trim(translate(${columnname},'0123456789',' ')) is null
</select>
<!-- 序号ID -->
<!-- 获取序号ID -->
<select id="queryGetXhid" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select * from (
	   select xhlx,dqxh,xtrq,scfs from gyb_ywxhb where  xhlx = #{cslx,jdbcType=VARCHAR}
	   and yljgbm = #{yljgbm,jdbcType=VARCHAR} Order By Dqxh Desc
   ) where rownum=1
</select>
<!-- 更新序号 -->
<update id="updateGetXhid" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update gyb_ywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</update>
<!-- 插入序号 -->
<insert id="insertGetXhid" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into gyb_ywxhb(xhlx,dqxh,xtrq,scfs,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},#{yljgbm,jdbcType=VARCHAR})
</insert>

<!-- 挂号挂号序号 -->
<!-- 获取挂号序号 -->
<select id="queryGetGhxh" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select * from (
   select xhlx,dqxh,xtrq,scfs from ghb_ywxhb
   where  xhlx = #{cslx,jdbcType=VARCHAR} and  yljgbm = #{yljgbm,jdbcType=VARCHAR}
   and to_char(xtrq,'yyyy-MM-dd') = to_char(#{rq,jdbcType=TIMESTAMP},'yyyy-MM-dd') Order By Dqxh Desc
   ) where rownum=1
</select>
<!-- 更新挂号序号 -->
<update id="updateGetGhxh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update ghb_ywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
    and to_date(to_char(xtrq,'yyyy-mm-dd'),'yyyy-mm-dd')  =  to_date(to_char(#{rq,jdbcType=TIMESTAMP},'yyyy-mm-dd'),'yyyy-mm-dd')
</update>
<!-- 插入挂号序号 -->
<insert id="insertGetGhxh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into ghb_ywxhb(xhlx,dqxh,xtrq,scfs,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},#{yljgbm,jdbcType=VARCHAR})
</insert>

<!-- 库房序号 -->
<!-- 获取库房序号 -->
<select id="queryGetKfpzh" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select xhlx,dqxh,xtrq,scfs from ykb_ywxhb where  xhlx = #{cslx,jdbcType=VARCHAR}
   and kfbm = #{kfbm,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</select>
<!-- 更新库房序号 -->
<update id="updateGetKfpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update ykb_ywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and kfbm = #{kfbm,jdbcType=VARCHAR}
    and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</update>
<!-- 插入库房序号 -->
<insert id="insertGetKfpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into ykb_ywxhb(xhlx,dqxh,xtrq,scfs,kfbm,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},
  #{kfbm,jdbcType=VARCHAR},#{yljgbm,jdbcType=VARCHAR})
</insert>

<!-- 药房序号 -->
<!-- 获取药房序号 -->
<select id="queryGetYfpzh" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select xhlx,dqxh,xtrq,scfs from yfb_ywxhb where  xhlx = #{cslx,jdbcType=VARCHAR}
   and yfbm = #{yfbm,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</select>
<!-- 更新药房序号 -->
<update id="updateGetYfpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update yfb_ywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and yfbm = #{yfbm,jdbcType=VARCHAR}
    and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</update>
<!-- 插入药房序号 -->
<insert id="insertGetYfpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into yfb_ywxhb(xhlx,dqxh,xtrq,scfs,yfbm,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},
  #{yfbm,jdbcType=VARCHAR},#{yljgbm,jdbcType=VARCHAR})
</insert>

<!-- 物资库房序号 -->
<!-- 获取物资库房序号 -->
<select id="queryGetWzpzh" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select xhlx,dqxh,xtrq,scfs from wz_ywxhb where  xhlx = #{cslx,jdbcType=VARCHAR}
   and kfbm = #{kfbm,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</select>
<!-- 更新物资库房序号 -->
<update id="updateGetWzpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update wz_ywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and kfbm = #{kfbm,jdbcType=VARCHAR}
    and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</update>
<!-- 插入物资库房序号 -->
<insert id="insertGetWzpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into wz_ywxhb(xhlx,dqxh,xtrq,scfs,kfbm,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},
  #{kfbm,jdbcType=VARCHAR},#{yljgbm,jdbcType=VARCHAR})
</insert>

<!-- 科室库房序号 -->
<!-- 获取科室库房序号 -->
<select id="queryGetKskfpzh" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select xhlx,dqxh,xtrq,scfs from yfb_ksywxhb where  xhlx = #{cslx,jdbcType=VARCHAR}
   and ksbm = #{ksbm,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</select>
<!-- 更新科室库房序号 -->
<update id="updateGetKskfpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update yfb_ksywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and ksbm = #{ksbm,jdbcType=VARCHAR}
    and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</update>
<!-- 插入科室库房序号 -->
<insert id="insertGetKskfpzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into yfb_ksywxhb(xhlx,dqxh,xtrq,scfs,ksbm,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},
  #{ksbm,jdbcType=VARCHAR},#{yljgbm,jdbcType=VARCHAR})
</insert>

    <!-- 科室库房序号 -->
    <!-- 获取科室库房序号 -->
    <select id="queryGetKspzh" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
   select xhlx,dqxh,xtrq,scfs from yfb_ksywxhb where  xhlx = #{cslx,jdbcType=VARCHAR}
   and ksbm = 'aa' and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</select>
    <!-- 更新科室库房序号 -->
    <update id="updateGetKspzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  update yfb_ksywxhb set dqxh = #{dqxh,jdbcType=VARCHAR},xtrq = #{rq,jdbcType=TIMESTAMP}
    where  xhlx = #{cslx,jdbcType=VARCHAR} and ksbm = 'aa'
    and dqxh = #{oldxh,jdbcType=VARCHAR} and yljgbm = #{yljgbm,jdbcType=VARCHAR}
</update>
    <!-- 插入科室库房序号 -->
    <insert id="insertGetKspzh" parameterType="com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel">
  insert into yfb_ksywxhb(xhlx,dqxh,xtrq,scfs,ksbm,yljgbm)
  values (#{cslx,jdbcType=VARCHAR},#{dqxh,jdbcType=VARCHAR},#{rq,jdbcType=TIMESTAMP},#{scfs,jdbcType=VARCHAR},
  'aa',#{yljgbm,jdbcType=VARCHAR})
</insert>

</mapper>
