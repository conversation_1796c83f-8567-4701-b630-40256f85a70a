<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsg.csp.ykgl.kfwh.dao.New1Ykb_hcgxModelMapper" >
  <resultMap id="BaseResultMap" type="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel" >
    <id column="GXBM" property="gxbm" jdbcType="VARCHAR" />
    <result column="GXMC" property="gxmc" jdbcType="VARCHAR" />
    <result column="PYJM" property="pyjm" jdbcType="VARCHAR" />
    <result column="KSSBZ" property="kssbz" jdbcType="VARCHAR" />
    <result column="TYBZ" property="tybz" jdbcType="VARCHAR" />
    <result column="MZYP" property="mzyp" jdbcType="VARCHAR" />
    <result column="LX" property="lx" jdbcType="VARCHAR" />
  </resultMap>

  <select id="queryAllYpgx" resultMap="BaseResultMap" parameterType="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel">
    SELECT
    <include refid="Base_Column_List" />
    FROM YKB_HCGX
    where
    yljgbm = #{yljgbm,jdbcType=VARCHAR}
    <if test="lx != null and lx != '' ">
      and lx =  #{lx,jdbcType=VARCHAR}
    </if>
    <if test="parm != null and parm != '' ">
      and (GXMC like '%'||#{parm,jdbcType=VARCHAR}||'%' or PYJM like
      '%'||#{parm,jdbcType=VARCHAR}||'%')
     </if>
    ORDER BY  ${sort} ${order}
  </select>

  <!-- 批量删除 -->
  <delete id="deletebatch3Mem" parameterType="java.util.List" >
    DELETE FROM YKB_HCGX WHERE GXBM IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item.gxbm,jdbcType=VARCHAR}
    </foreach>
    and yljgbm in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item.yljgbm,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <insert id="save3Mem" parameterType="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel">
    MERGE INTO YKB_HCGX T1
    USING
    (
    SELECT #{yljgbm,jdbcType=VARCHAR} yljgbm,#{gxbm,jdbcType=VARCHAR} GXBM, #{gxmc,jdbcType=VARCHAR} GXMC,
    #{pyjm,jdbcType=VARCHAR} PYJM,#{kssbz,jdbcType=VARCHAR} KSSBZ,
    #{tybz,jdbcType=VARCHAR} TYBZ,#{mzyp,jdbcType=VARCHAR} MZYP,#{lx,jdbcType=VARCHAR} LX FROM DUAL
    ) T2
    ON (T1.GXBM = T2.GXBM and T1.yljgbm = T2.yljgbm)
    WHEN MATCHED THEN
    UPDATE
    SET
    T1.GXMC = T2.GXMC,
    T1.PYJM = T2.PYJM,
    T1.KSSBZ = T2.KSSBZ,
    T1.TYBZ = T2.TYBZ,
    T1.MZYP = T2.MZYP,
    T1.LX =  T2.LX
    WHEN NOT MATCHED THEN
    insert (yljgbm,GXBM, GXMC, PYJM, KSSBZ, TYBZ, MZYP,LX)
    values (T2.yljgbm,T2.GXBM, T2.GXMC, T2.PYJM, T2.KSSBZ, T2.TYBZ, T2.MZYP, T2.LX)
  </insert>

  <sql id="Base_Column_List" >
    GXBM, GXMC, PYJM, KSSBZ, TYBZ, MZYP,lx
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from YKB_HCGX
    where yljgbm = #{yljgbm,jdbcType=VARCHAR} and GXBM = #{gxbm,jdbcType=VARCHAR}
    <if test="lx != null and lx != '' ">
      and lx =  #{lx,jdbcType=VARCHAR},
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from YKB_HCGX
    where yljgbm = #{yljgbm,jdbcType=VARCHAR} and GXBM = #{gxbm,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel" >
    insert into YKB_HCGX (yljgbm,GXBM, GXMC, PYJM,
      KSSBZ, TYBZ, MZYP)
    values (#{yljgbm,jdbcType=VARCHAR},#{gxbm,jdbcType=VARCHAR}, #{gxmc,jdbcType=VARCHAR}, #{pyjm,jdbcType=VARCHAR},
      #{kssbz,jdbcType=VARCHAR}, #{tybz,jdbcType=VARCHAR}, #{mzyp,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel" >
    insert into YKB_HCGX
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="yljgbm != null" >
        yljgbm,
      </if>
      <if test="gxbm != null" >
        GXBM,
      </if>
      <if test="gxmc != null" >
        GXMC,
      </if>
      <if test="pyjm != null" >
        PYJM,
      </if>
      <if test="kssbz != null" >
        KSSBZ,
      </if>
      <if test="tybz != null" >
        TYBZ,
      </if>
      <if test="mzyp != null" >
        MZYP,
      </if>
      <if test="lx != null and lx != '' ">
        LX,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="yljgbm != null" >
        #{yljgbm,jdbcType=VARCHAR},
      </if>
      <if test="gxbm != null" >
        #{gxbm,jdbcType=VARCHAR},
      </if>
      <if test="gxmc != null" >
        #{gxmc,jdbcType=VARCHAR},
      </if>
      <if test="pyjm != null" >
        #{pyjm,jdbcType=VARCHAR},
      </if>
      <if test="kssbz != null" >
        #{kssbz,jdbcType=VARCHAR},
      </if>
      <if test="tybz != null" >
        #{tybz,jdbcType=VARCHAR},
      </if>
      <if test="mzyp != null" >
        #{mzyp,jdbcType=VARCHAR},
      </if>
      <if test="lx != null" >
        #{lx,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel" >
    update YKB_HCGX
    <set >
      <if test="gxmc != null" >
        GXMC = #{gxmc,jdbcType=VARCHAR},
      </if>
      <if test="pyjm != null" >
        PYJM = #{pyjm,jdbcType=VARCHAR},
      </if>
      <if test="kssbz != null" >
        KSSBZ = #{kssbz,jdbcType=VARCHAR},
      </if>
      <if test="tybz != null" >
        TYBZ = #{tybz,jdbcType=VARCHAR},
      </if>
      <if test="mzyp != null" >
        MZYP = #{mzyp,jdbcType=VARCHAR},
      </if>
    </set>
    where yljgbm = #{yljgbm,jdbcType=VARCHAR} and GXBM = #{gxbm,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_hcgxModel" >
    update YKB_HCGX
    set GXMC = #{gxmc,jdbcType=VARCHAR},
      PYJM = #{pyjm,jdbcType=VARCHAR},
      KSSBZ = #{kssbz,jdbcType=VARCHAR},
      TYBZ = #{tybz,jdbcType=VARCHAR},
      MZYP = #{mzyp,jdbcType=VARCHAR}
    where yljgbm = #{yljgbm,jdbcType=VARCHAR} and GXBM = #{gxbm,jdbcType=VARCHAR}
  </update>
</mapper>
