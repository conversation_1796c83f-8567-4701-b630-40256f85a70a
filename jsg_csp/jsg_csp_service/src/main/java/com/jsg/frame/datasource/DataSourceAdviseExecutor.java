package com.jsg.frame.datasource;

import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * @ClassName: DataSourceAdviseExecutor
 * @Description: DAO数据缓存AOP
 * <AUTHOR>
 * @date 2020年4月7日 上午11:14:45
 */
public class DataSourceAdviseExecutor
{
	// 日志工具
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
    /**
     * @Title: transmit
     * @Description: 拦截需要读写分离的DAO方法
     * @param pjp
     * @return void 返回类型
     * @throws
     */
    public void transmit(JoinPoint point)
    {
        // 方法名
        String methodName = point.getSignature().getName();
        //根据医疗机构读不同数据库
        /*UtilRequest result = (UtilRequest)point.getArgs()[0];
        result.getYljgbm();*/

        if (methodName.startsWith(RoutingContext.WRITE_PREFIX))
        {
            RoutingContext.setName(RoutingContext.WRITE_DATASOURCE);
        }
        else if (methodName.startsWith(RoutingContext.READ_PREFIX))
        {
            RoutingContext.setName(RoutingContext.READ_DATASOURCE);
        }
        else if (methodName.startsWith(RoutingContext.YZPACS_PREFIX))
        {
            RoutingContext.setName(RoutingContext.YZPACS_DATASOURCE);
        }
        else if (methodName.startsWith(RoutingContext.EMR_PREFIX))
        {
            RoutingContext.setName(RoutingContext.EMR_DATASOURCE);
        }
        else if (methodName.startsWith(RoutingContext.SYSERROR_PREFIX))
        {
            RoutingContext.setName(RoutingContext.SYS_DATASOURCE);
        }
        else if (methodName.startsWith(RoutingContext.OLDJCBG_PREFIX))
        {
            RoutingContext.setName(RoutingContext.OLDJCBG_DATASOURCE);
        }
        else if (methodName.startsWith(RoutingContext.OLDJYBG_PREFIX))
        {
            RoutingContext.setName(RoutingContext.OLDJYBG_DATASOURCE);
        }else if (methodName.startsWith(RoutingContext.ESBPT_PREFIX))
        {
            RoutingContext.setName(RoutingContext.ESBPT_DATASOURCE);
        }
        else{
        	RoutingContext.setName(RoutingContext.WRITE_DATASOURCE);
        }
        //logger.info("在这里!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"+methodName);
    }
}
