package com.jsg.csp.xtwh.ylfwxm.dao;

import java.util.List;

import com.jsg.csp.api.xtwh.ylfwxm.pojo.Gyb_zflxModel;


public interface New1Gyb_zflxModelMapper {

	//批量删除支付类型
    int deletebatch3Mem(List<Gyb_zflxModel> record);

    //查询单条支付类型
    Gyb_zflxModel queryGyb_zflxOne4Mem(Gyb_zflxModel bean);

    //分页查询支付类型
    List<Gyb_zflxModel> querygyb_zflx4Mem(Gyb_zflxModel bean);

    //保存支付类型
    int save3Mem(Gyb_zflxModel record);
}
