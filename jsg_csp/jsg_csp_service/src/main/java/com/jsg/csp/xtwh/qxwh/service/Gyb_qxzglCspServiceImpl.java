package com.jsg.csp.xtwh.qxwh.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsg.csp.api.xtwh.qxwh.pojo.CzyzCsModel;
import com.jsg.csp.api.xtwh.qxwh.pojo.Gyb_qxzglModel;
import com.jsg.csp.api.xtwh.qxwh.service.IGyb_qxzglCspService;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.csp.xtwh.qxwh.dao.New1Gyb_qxzglModelMapper;

/**
 *
* @ClassName: Gyb_qxzglCspServiceImpl
* @Description: TODO(权限组管理)
* <AUTHOR>
* @date 2020年5月23日 下午10:27:52
*
 */
public class Gyb_qxzglCspServiceImpl  extends ServiceInvocationHelper implements IGyb_qxzglCspService{

	private final Logger logger = LoggerFactory.getLogger(Gyb_qxzglCspServiceImpl.class);
	@Autowired
	private New1Gyb_qxzglModelMapper gyb_qxzglModelMapper;

	/**
	 * 查询所有权限组管理
	 */
	@Override
	public UtilResponse queryGyb_qxzglList(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Gyb_qxzglModel bean = (Gyb_qxzglModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Gyb_qxzglModel> list = (List<Gyb_qxzglModel>)gyb_qxzglModelMapper.querygyb_qxzgl4Mem(bean);
			PageInfo<Gyb_qxzglModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("权限组管理查询失败:"+ex.getCause().getMessage());
            logger.error("调用查询权限组管理列表信息接口【Gyb_qxzglCspServiceImpl queryGyb_qxzglList】发生异常", ex);
		}
		return result;
	}

	/**
	 * 查询单权限组管理
	 */
	@Override
	public UtilResponse queryGyb_qxzglOne(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Gyb_qxzglModel record=(Gyb_qxzglModel) msg.getParam().get("bean");
			record.setYljgbm(msg.getYljgbm());
			Gyb_qxzglModel bean = gyb_qxzglModelMapper.queryGyb_qxzglOne4Mem(record);
			result.getResResult().put("bean", bean);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("权限组管理查询失败:"+ex.getCause().getMessage());
			logger.error("调用查询单个权限组管理信息接口【Gyb_qxzglCspServiceImpl queryGyb_qxzglOne】发生异常", ex);
		}
		return result;
	}

	/**
	 * 删除权限组管理
	 */
	@Override
	public UtilResponse deletebatch(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			List<Gyb_qxzglModel> beans = (List<Gyb_qxzglModel>)msg.getParam().get("bean");
			for(Gyb_qxzglModel qxzgl: beans){
				qxzgl.setYljgbm(msg.getYljgbm());
			}
			Integer ref = gyb_qxzglModelMapper.deletebatch3Mem(beans);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除权限组管理失败:"+e.getCause().getMessage());
			logger.error("调用删除权限组管理接口【Gyb_qxzglCspServiceImpl deletebatch】发生异常", e);
		}
		return result;
	}

	/**
	 * 权限组管理保存修改
	 */
	@Override
	public UtilResponse savebatch(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Gyb_qxzglModel bean=(Gyb_qxzglModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = gyb_qxzglModelMapper.save3Mem(bean);
			result.getResResult().put("ref", ref.toString());
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("权限组管理保存失败:"+ex.getCause().getMessage());
			logger.error("调用权限组管理保存接口【Gyb_qxzglCspServiceImpl savebatch】发生异常",ex);
		}
		return result;
	}

	/**
	 * 通过查询权限组获取操作员组信息
	 */
	@Override
	public UtilResponse queryGyb_qxzCzyz(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			CzyzCsModel bean = (CzyzCsModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Gyb_qxzglModel> list = (List<Gyb_qxzglModel>)gyb_qxzglModelMapper.queryGyb_qxzCzyz(bean);
			PageInfo<Gyb_qxzglModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("权限组管理查询失败:"+ex.getCause().getMessage());
            logger.error("调用查询权限组管理列表信息接口【Gyb_qxzglCspServiceImpl queryGyb_qxzCzyz】发生异常", ex);
		}
		return result;
	}

}


















