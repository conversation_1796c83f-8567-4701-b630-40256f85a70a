package com.jsg.csp.hsz.hlyw.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsg.common.util.CommonUtil;
import com.jsg.common.util.DateTimeUtil;
import com.jsg.csp.api.hsz.hlyw.pojo.Hsz_tysqCxModel;
import com.jsg.csp.api.hsz.hlyw.pojo.Hsz_tysqModel;
import com.jsg.csp.api.hsz.hlyw.pojo.Hsz_tysqModelQuery;
import com.jsg.csp.api.hsz.hlyw.pojo.Hsz_yz_fymxModel;
import com.jsg.csp.api.hsz.hlyw.service.IhszHlywTysqCspService;
import com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.jsg.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.jsg.csp.api.pubfun.pojo.ypkc.YfKcSaveModel;
import com.jsg.csp.api.pubfun.pojo.ypkc.YfkcclMsgModel;
import com.jsg.csp.api.pubfun.pojo.ypkc.YpkcMsgModel;
import com.jsg.csp.api.pubfun.service.IPubFunCspService;
import com.jsg.csp.api.pubfun.service.IYpFunCspService;
import com.jsg.csp.api.yfgl.kcgl.pojo.Yfb_ypkcModel;
import com.jsg.csp.api.yfgl.yfyw.pojo.YfbYfywBqbyYpfycxModel;
import com.jsg.csp.api.zygl.crygl.pojo.Zyb_rydjModel;
import com.jsg.csp.api.zygl.fygl.pojo.Zyb_brfyModel;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.csp.pojo.UserInfoModel;
import com.jsg.csp.hsz.hlyw.dao.New1Hsz_tysqModelMapper;
import com.jsg.csp.hsz.hlyw.dao.New1Hsz_yz_fymxModelMapper;
import com.jsg.csp.pubfun.dao.YpFunModelMapper;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.csp.ykgl.kfwh.dao.New1Ykb_ypzlModelMapper;
import com.jsg.csp.zygl.crygl.dao.New1Zyb_rydjModelMapper;
import com.jsg.csp.zygl.fygl.dao.New1Zyb_brfyModelMapper;
import com.jsg.util.HttpClientTools;
import com.jsg.util.JsonAssemblerForCancelAdvice;
import com.jsg.util.JsonAssemblerForReturnAdvice;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
* @ClassName: HszHlywTysqCspServiceImpl
* @Description: 病区退药申请
* <AUTHOR>
* @date 2020年7月12日 下午5:17:35
*
 */
public class HszHlywTysqCspServiceImpl  extends ServiceInvocationHelper
implements IhszHlywTysqCspService{

	private final Logger logger = LoggerFactory.getLogger(HszHlywFjxmCxCspServiceImpl.class);
	@Autowired
	private New1Hsz_tysqModelMapper hsz_tysqModelMapper;
	@Autowired
	private New1Ykb_ypzlModelMapper ykb_ypzlModelMapper;
	@Autowired
	private New1Hsz_yz_fymxModelMapper hsz_yz_fymxModelMapper;
	@Autowired
	private IPubFunCspService pubFunCspService;
	@Autowired
	private New1Zyb_rydjModelMapper zyb_rydjModelMapper;
	@Autowired
	private YpFunModelMapper ypFunModelMapper;
	@Autowired
	private New1Zyb_brfyModelMapper zyb_brfyModelMapper;
	@Autowired
	private IYpFunCspService ypFunCspService;
	/**
	 * 批量插入退药申请
	 */
	@Transactional(rollbackFor=Exception.class)
	@Override
	public synchronized UtilResponse insert(UtilRequest msg) throws Exception {
		UtilResponse result = new UtilResponse().newInstance();
		try{
			List<Hsz_tysqModel> beans = (List<Hsz_tysqModel>)msg.getParam().get("bean");
			UserInfoModel usserinfo = (UserInfoModel)msg.getParam().get("userinfo");
			String zdtybz = (String)msg.getParam().get("zdtybz"); // 下出院医嘱自动退药标志
			String[] fymxid = new String[beans.size()];
			for (int i = 0; i < beans.size(); i++) {
				fymxid[i] = beans.get(i).getFymxid();
			}
			if (fymxid.length <= 0 ){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("没有可退的药品明细!");
				logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】发生异常:没有可退的药品明细!");
				return result;
			}
			//退药判断病人是否在院
            Zyb_rydjModel rydj = new Zyb_rydjModel();
            rydj.setZyh(beans.get(0).getZyh());
            rydj.setYljgbm(msg.getYljgbm());
            rydj = zyb_rydjModelMapper.queryByZyh(rydj);
            if (rydj.getBqcybz().equals("1") || rydj.getZyzt().equals("1")){
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("已经出院不允许退药！");
                logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】发生异常:已经出院不允许退药！");
                return result;
            }
			Hsz_yz_fymxModel fymxmsg = new Hsz_yz_fymxModel();
			fymxmsg.setSearchfymxid(fymxid);
			fymxmsg.setYljgbm(msg.getYljgbm());
			//取需退药的发药明细
			List<Hsz_yz_fymxModel> fymxs = hsz_yz_fymxModelMapper.queryFymx(fymxmsg);
			if (fymxs.size() <= 0 ){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg("没有可退的药品明细!");
				logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】发生异常:没有可退的药品明细!");
				return result;
			}
			String strErr = "";
			for (Hsz_tysqModel tymx : beans) {
				String fyid = tymx.getFymxid();
				Double sqsl = tymx.getSqsl();
				for (Hsz_yz_fymxModel fymx:fymxs ){
					if (fymx.getFymxid().equals(fyid)){

						Double ysqsl = 0.0;
						if(fymx.getYsqsl() != null){
							ysqsl = fymx.getYsqsl();
						}

						if(fymx.getFysl() - fymx.getTysl()<=0){
							strErr += "药品["+fymx.getRyypmc()+"]已经退完,无须再次申请";
							break;
						}

						if (sqsl > (fymx.getFysl() - fymx.getTysl() - ysqsl)){
							strErr += "药品["+fymx.getRyypmc()+"]申请数量["+sqsl+"]大于可退数量["+(fymx.getFysl() - fymx.getTysl()- ysqsl)+"]";
							break;
						}
						tymx.setSqry(usserinfo.getCzybm());
						tymx.setSqryxm(usserinfo.getCzyxm());
						tymx.setZyh(fymx.getZyh());
						tymx.setRyyfbm(fymx.getYfbm());
						tymx.setRyypbm(fymx.getRyypbm());
						tymx.setRyypmc(fymx.getRyypmc());
						tymx.setRyksbm(fymx.getKsbm());
						tymx.setRyksmc(fymx.getKsmc());
						tymx.setSqsj(new Date());

						if(StringUtils.isNotEmpty(zdtybz)){
							if("1".equals(zdtybz)){
								tymx.setBzsm("出院医嘱自动退药记录");
							}
							if("2".equals(zdtybz)){
								tymx.setBzsm("停嘱审核自动退药记录");
							}
						}
					}
				}
			}
			if (!strErr.equals("")){
				result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
				result.setResultMsg(strErr);
	            logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】发生异常"+strErr);
	            return result;
			}
			//取退药单号
			UtilResponse resxh = null;
			GetXhidMsgModel beanbyd = new GetXhidMsgModel();
			beanbyd.setScfs("3");
			beanbyd.setCslx("TYD");
			msg.getParam().put("bean", beanbyd);
			resxh = pubFunCspService.GetIdNameLock(msg);
			GetXhidResModel cfhbean = (GetXhidResModel)resxh.getResResult().get("bean");
			String tydh = cfhbean.getDqxh();
			//写退药单号
			List<Hsz_tysqModel> parm = new ArrayList<Hsz_tysqModel>();

			for (Hsz_tysqModel ty : beans) {
				ty.setTysqdh(tydh);
				ty.setYljgbm(msg.getYljgbm());

				if(ty.getSqsl() > 0){
					parm.add(ty);
				}
			}
			if(parm.size() > 0){
				int ref = hsz_tysqModelMapper.insert(parm);
				if (ref !=-1 ){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("批量插入退药申请失败!");
		            logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】更新退药记录失败!");
		            result.getResResult().put("ref", 0);
				}
				result.getResResult().put("ref", ref);
			}else{
				result.getResResult().put("ref", 0);
			}
			//三医通系统接收 HIS
			HttpClientTools httpClientTools = new HttpClientTools();
			Hsz_tysqModelQuery record = new Hsz_tysqModelQuery();
			String[] zyhArray = beans.stream().map(Hsz_tysqModel::getZyh).filter(Objects::nonNull).distinct().toArray(String[]::new);
			String[] fymxIdArray = beans.stream().map(Hsz_tysqModel::getFymxid).filter(Objects::nonNull).distinct().toArray(String[]::new);
			record.setSearchzyh(zyhArray);
			record.setSearchfymxid(fymxIdArray);
			if(null != record){
				List<JSONObject> dbResult = hsz_tysqModelMapper.queryTysqPostList(record);
				JSONArray finalPayload = JsonAssemblerForReturnAdvice.convertToReturnAdviceFormatLowerCase(dbResult);
				System.out.println(finalPayload.toJSONString());
				if(finalPayload.size() > 0){
					JSONObject jsbResult = httpClientTools.PostMethod(finalPayload,"ty_url_post");
					if(null != jsbResult && 200 == jsbResult.getInteger("code")){
						logger.info("调用三医通退药申请信息接口成功!: ", jsbResult);
					}else{
						logger.info("调用三医通退药申请信息接口失败!: ", jsbResult);
					}
				}
			}
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("批量插入退药申请失败:"+e.getCause().getMessage());
            logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】发生异常", e);
            throw new Exception();
		}
		return result;
	}

	/**
	 * 病区退药审核
	 */
	@Transactional(rollbackFor=Exception.class)
	@Override
	public synchronized UtilResponse bqtysh(UtilRequest msg) throws Exception{
		UtilResponse result = new UtilResponse().newInstance();
		int ref = 0;
		try{
			Date ldDate = new Date();

			List<Hsz_tysqModel> tybeans = (List<Hsz_tysqModel>)msg.getParam().get("bean");
			UserInfoModel userinfo = (UserInfoModel)msg.getParam().get("userinfo");
			msg.getUserinfo().put("userinfo",userinfo);
			String yfbm = (String)msg.getParam().get("yfbm");

			Map<String, List<Hsz_tysqModel>> tymap = tybeans.stream().collect(Collectors.groupingBy(Hsz_tysqModel::getTysqdh));

			for (Map.Entry<String, List<Hsz_tysqModel>> entry : tymap.entrySet()) {
				List<Hsz_tysqModel> beans = entry.getValue();
				String[] tysqid = new String[beans.size()];
				for (int i = 0; i < beans.size(); i++) {
					tysqid[i] = beans.get(i).getTysqid();
				}
				if (tysqid.length <= 0 ){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("没有可审核的药品明细!");
					logger.error("病区退药审核接口【HszHlywTysqCspServiceImpl update】发生异常:没有可审核的药品明细!");
					return result;
				}
				Hsz_tysqModel tymxmsg = new Hsz_tysqModel();
				tymxmsg.setSearchtysqid(tysqid);
				tymxmsg.setYljgbm(msg.getYljgbm());
				//取需退药的发药明细
				List<Hsz_tysqModel> tyxms = hsz_tysqModelMapper.queryTysq(tymxmsg);
				if (tyxms.size() <= 0 ){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("没有可审核的退药明细!");
					logger.error("病区退药审核接口【HszHlywTysqCspServiceImpl update】发生异常:没有可退的药品明细!");
					return result;
				}
				//根据退药ID取发药明细
				String[] fymxid = new String[tyxms.size()];
				for (int i = 0; i < tyxms.size(); i++) {
					fymxid[i] = tyxms.get(i).getFymxid();
				}
				Hsz_yz_fymxModel fymxmsg = new Hsz_yz_fymxModel();
				fymxmsg.setSearchfymxid(fymxid);
				fymxmsg.setYljgbm(msg.getYljgbm());
				//取需退药的发药明细
				List<Hsz_yz_fymxModel> fymxs = hsz_yz_fymxModelMapper.queryFymx(fymxmsg);
				if (fymxs.size() <= 0 ){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("没有可退的药品明细!");
					logger.error("批量插入退药申请接口【HszHlywTysqCspServiceImpl insert】发生异常:没有可退的药品明细!");
					return result;
				}

				//查询药品种类对应费用
				YfbYfywBqbyYpfycxModel bean = new YfbYfywBqbyYpfycxModel();
				bean.setYljgbm(msg.getYljgbm());
				List<YfbYfywBqbyYpfycxModel> ypzldyfys = (List<YfbYfywBqbyYpfycxModel>)ykb_ypzlModelMapper.queryYpglfycx(bean);
				if (ypzldyfys == null || ypzldyfys.size() <= 0 ){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("请维护药品种类对应费用项目!");
					logger.error(
							"HszHlywTysqCspServiceImpl Interface| Result FAILED: update |Result  " + result.toString());
					return result;
				}
				//根据住院取入院登记信息
				Zyb_rydjModel rydj = new Zyb_rydjModel();
				rydj.setZyh(tyxms.get(0).getZyh());
				rydj.setYljgbm(msg.getYljgbm());
				rydj = zyb_rydjModelMapper.queryByZyh(rydj);
				if (rydj.getBqcybz().equals("1") || rydj.getZyzt().equals("1")){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg("已经出院不允许退药！");
					logger.error("病区退药审核接口【HszHlywTysqCspServiceImpl update】发生异常:已经出院不允许退药！");
					return result;
				}
				//根据药房编码取药品库存
				YpkcMsgModel kcmsg = new YpkcMsgModel();
				kcmsg.setYfbm(yfbm);
				kcmsg.setCrbz("RK");
				kcmsg.setYljgbm(msg.getYljgbm());
				//查询库存
				List<Yfb_ypkcModel> ypkc = (List<Yfb_ypkcModel>)ypFunModelMapper.Getyfkc(kcmsg);

				String strErr = "";
				List<Hsz_yz_fymxModel> insertfymx = new ArrayList<Hsz_yz_fymxModel>();//退药写负记录
				List<Hsz_yz_fymxModel> updatefymx = new ArrayList<Hsz_yz_fymxModel>();//更新原退药数量
				tysqid = new String[tyxms.size()];//更新退药ID
				for (int i = 0 ; i < tyxms.size() ;i++) {
					Hsz_tysqModel tysqmx = tyxms.get(i);
					String fyid = tysqmx.getFymxid();
					BigDecimal sqsl =BigDecimal.valueOf(tysqmx.getSqsl());
					for (Hsz_yz_fymxModel fymx:fymxs ){
						if (fymx.getFymxid().equals(fyid)){
							BigDecimal fysl=BigDecimal.valueOf(fymx.getFysl());
							BigDecimal tysl=BigDecimal.valueOf(fymx.getTysl());
							if (sqsl.compareTo(fysl.subtract(tysl))==1){
								strErr += "药品["+fymx.getRyypmc()+"]审核数量["+sqsl+"]大于可退数量["+(fymx.getFysl() - fymx.getTysl())+"]";
								break;
							}
							//处理退药申请明细
							tysqid[i] = tysqmx.getTysqid();
							//处理退费明细写负记录
							Hsz_yz_fymxModel tymx = fymx;
							tymx.setFysl(BigDecimal.ZERO.subtract(sqsl).doubleValue());
							tymx.setTysl(0.0);
							tymx.setBysj(ldDate);
							tymx.setByr(userinfo.getCzybm());
							tymx.setByrxm(userinfo.getCzyxm());
							tymx.setTyfymxid(fymx.getFymxid());//退药发药明细ID等原发药ID
							tymx.setFycfh(null);
							tymx.setFysj(null);
							tymx.setFyr(null);
							tymx.setFyrxm(null);

							tymx.setDybz(null);
							tymx.setDyrq(null);
							tymx.setDyrxm(null);

							tymx.setBzms("退药审核扣费");
							tymx.setYyrq(fymx.getYyrq());
							insertfymx.add(tymx);
							//处理原药品记录退药数量
							tymx = new Hsz_yz_fymxModel();
							tymx.setFymxid(fymx.getFymxid());
							tymx.setTysl(tysl.add(sqsl).doubleValue());
							updatefymx.add(tymx);
						}
					}
				}
				if (!strErr.equals("")){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg(strErr);
					logger.error("病区退药审核接口【HszHlywTysqCspServiceImpl update】发生异常:"+strErr);
					return result;
				}
				//处理退药申请更新为单条
				Hsz_tysqModel tyupdate = new Hsz_tysqModel();
				tyupdate.setSearchtysqid(tysqid);
				tyupdate.setZyh(tyxms.get(0).getZyh());
				tyupdate.setTysqdh(tyxms.get(0).getTysqdh());
				tyupdate.setHdry(userinfo.getCzybm());
				tyupdate.setHdryxm(userinfo.getCzyxm());
				tyupdate.setHdsj(ldDate);
				tyupdate.setHdbz("1");
				//更新病人费用
				Map<String, Object> map = addbrfy(insertfymx,ypzldyfys,rydj,userinfo,ldDate);
				if (map.get("code").equals("false")){
					result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
					result.setResultMsg((String)map.get("mess"));
					logger.error("病区退药审核接口【HszHlywTysqCspServiceImpl update】发生异常:"+(String)map.get("mess"));
					return result;
				}//
				List<Zyb_brfyModel> brfylist = (List<Zyb_brfyModel>)map.get("brfylist");
				//根据处理处理需要处理库存记录
				map  = updateYpkc(insertfymx,ypkc,ldDate);
				List<YfKcSaveModel> updateypkc = (List<YfKcSaveModel>)map.get("updateypkc");
				List<YfKcSaveModel> insertypkc = (List<YfKcSaveModel>)map.get("insertypkc");

				//取摆药单号
				//2018/01/05 退药审核多个药品一条一条审核，会在zyb_brfy生成多条费用记录，每条记录需要一个新的退药单号
				UtilRequest msgxhTyd = new UtilRequest();
				UtilResponse resxhTyd = null;
				GetXhidMsgModel beanxhTyd = new GetXhidMsgModel();
				beanxhTyd.setScfs("3");
				beanxhTyd.setCslx("TYD");
				msgxhTyd.getParam().put("bean", beanxhTyd);
				msgxhTyd.setYljgbm(msg.getYljgbm());
				resxhTyd = pubFunCspService.GetIdNameLock(msgxhTyd);
				GetXhidResModel resbeanTyd = (GetXhidResModel)resxhTyd.getResResult().get("bean");
				String tydh = resbeanTyd.getDqxh();
				//单据号直接取退药单号
//			String tydh = tyxms.get(0).getTysqdh();
				//退药明细写摆药单号
				if (insertfymx != null && insertfymx.size() > 0){
					for (Hsz_yz_fymxModel fymx : insertfymx) {
						String lbbm = "";

						for (YfbYfywBqbyYpfycxModel ypzldyfy : ypzldyfys) {
							if (ypzldyfy.getYpzlbm().equals(fymx.getYpzl())) {
								lbbm = ypzldyfy.getLbbm();
								break;
							}
						}
						fymx.setBydh(tydh+"_"+DateTimeUtil.getChar8En(fymx.getYyrq())+"_"+lbbm);
						fymx.setBysj(ldDate);
					}
				}
				if (brfylist != null && brfylist.size() > 0){
					for (Zyb_brfyModel brfy : brfylist) {
						brfy.setBydxh(tydh+"_"+DateTimeUtil.getChar8En(brfy.getYyrq())+"_"+brfy.getFylb());
					}
				}

				//取系统批号
				if (insertypkc != null && insertypkc.size() > 0 ){
					GetXhidMsgModel beanxh = new GetXhidMsgModel();
					beanxh = new GetXhidMsgModel();
					beanxh.setCslx("PC");
					beanxh.setYfbm(yfbm);
					beanxh.setScfs("3"); //
					beanxh.setRq(new Date());
					msg.getParam().put("bean", beanxh);
					UtilResponse resultxtph = pubFunCspService.GetYfPzh(msg);
					GetXhidResModel resbean = (GetXhidResModel)resultxtph.getResResult().get("bean");
					String xtph = resbean.getDqxh();
					for (YfKcSaveModel yfKcSaveModel : insertypkc) {
						yfKcSaveModel.setXtph(xtph);
					}
				}

				//提交数据
				//退药申请
				if (tyupdate != null){
					tyupdate.setYljgbm(msg.getYljgbm());
					ref = hsz_tysqModelMapper.update(tyupdate);
				}
				//写发药负记录
				if (insertfymx != null && insertfymx.size() > 0){
					for (int i = 0; i < insertfymx.size(); i++) {
						insertfymx.get(i).setYljgbm(msg.getYljgbm());
						insertfymx.get(i).setKfdw(null);
					}
					ref = hsz_yz_fymxModelMapper.insertFymx(insertfymx);
				}
				//更新发药明细原记录
				if (updatefymx != null && updatefymx.size() > 0){
					for (int i = 0; i < updatefymx.size(); i++) {
						updatefymx.get(i).setYljgbm(msg.getYljgbm());
						updatefymx.get(i).setKfdw(null);
					}
					ref = hsz_yz_fymxModelMapper.updateFymx(updatefymx);
				}
				//写退药费用记录
				if (brfylist != null && brfylist.size() > 0){
					for (int i = 0; i < brfylist.size(); i++) {
						brfylist.get(i).setYljgbm(msg.getYljgbm());
					}
					ref = zyb_brfyModelMapper.insert(brfylist);
				}
				//更新库存
				if ((updateypkc != null && updateypkc.size() > 0 ) ||
						(insertypkc != null && insertypkc.size() > 0)){
					//库存处理入参
					YfkcclMsgModel ypkcmsg = new YfkcclMsgModel();
					ypkcmsg.setCzybm(userinfo.getCzybm());
					ypkcmsg.setDjh(tydh);
					ypkcmsg.setCrlx("10");//病区退药审上帐
					msg.getParam().put("bean", ypkcmsg);
					msg.getParam().put("updateypkc", updateypkc);
					msg.getParam().put("insertypkc", insertypkc);
					//库存更新
					UtilResponse resxh = ypFunCspService.YfkcclLock(msg);
					ref = (Integer)resxh.getResResult().get("ref");
				}
			}
			result.getResResult().put("ref", ref);
			result.setResultMsg("病区退药审核成功!");
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("病区退药审核失败:"+e.getCause().getMessage());
            logger.error("病区退药审核接口【HszHlywTysqCspServiceImpl update】发生异常", e);
            throw new Exception();
		}
		return result;
	}

    /*
     * 根据发药明细处理更新库存
     */
	public Map<String, Object> updateYpkc(List<Hsz_yz_fymxModel> insertfymx,
		List<Yfb_ypkcModel> ypkc, Date ldDate){
		List<YfKcSaveModel> updateypkc = new ArrayList<YfKcSaveModel>();
		List<YfKcSaveModel> insertypkc = new ArrayList<YfKcSaveModel>();
		Map<String, Object> map = new HashMap<String, Object>();
		for (Hsz_yz_fymxModel fymx : insertfymx) {
			String ypbm = fymx.getRyypbm();
			String xtph = fymx.getXtph();
			Double yplj = fymx.getYplj();
			Double ypjj = fymx.getYpjj();
			String yfbm = fymx.getYfbm();
			Double syl = Math.abs(fymx.getFysl());
			YfKcSaveModel bean = new YfKcSaveModel();
			String newbz = "1";
			for (Yfb_ypkcModel kc : ypkc) {
				//库存中查询有相同的库存记录，则修改原库存
				if (kc.getYpbm().equals(ypbm) && kc.getXtph().equals(xtph) && kc.getYfbm().equals(yfbm)
						&& kc.getYplj().compareTo(yplj) == 0 && kc.getYpjj().compareTo(ypjj) == 0){
					//循环查找是否有修改库存记录
					String kcxgbz = "1";
					for (YfKcSaveModel kcsave : updateypkc){
						if (kcsave.getYpbm().equals(ypbm) && kcsave.getXtph().equals(xtph) && kcsave.getYfbm().equals(yfbm)
						  && kcsave.getYplj().compareTo(yplj) == 0 	&& kcsave.getYpjj().compareTo(ypjj) == 0
						){
							kcsave.setSyl(kcsave.getSyl() + syl);//原库存修改记录上修改使用量
							kcxgbz = "0";
							break;
						}
					}
					if (kcxgbz == "1"){//写库存修改记录
						bean.setPckcid(kc.getYfyppcid());;//批次ID
						bean.setYpbm(kc.getYpbm());//药品编码
						bean.setYfbm(kc.getYfbm());//药房编码
						bean.setXtph(kc.getXtph());//系统批号
						bean.setScph(kc.getScph());//生产批号
						bean.setYplj(kc.getYplj());//药品进价
						bean.setYpjj(kc.getYpjj());//药品零价
						bean.setSyl(syl);//
						bean.setKcsl(kc.getKcsl());//
						updateypkc.add(bean);
					}
					newbz = "0";
					break;
				}
			}
			if (newbz == "1"){
				bean.setYpbm(fymx.getRyypbm());//药品编码
				bean.setYfbm(fymx.getYfbm());//药房编码
				bean.setXtph(fymx.getXtph());//系统批号
				bean.setScph(fymx.getScph());//生产批号
				bean.setScrq(fymx.getScrq());//生产日期
				bean.setYxqz(DateTimeUtil.getDayChar21ByDays(ldDate, 365));//有效期至
				bean.setYpjj(fymx.getYpjj());//进价
				bean.setYplj(fymx.getYplj());//零价ldDate
				bean.setCdbm(fymx.getYpcd());//产地
				bean.setGhdw(fymx.getGhdw());//供货单位
				bean.setKfdw(fymx.getKfdw());//库房单位
				bean.setYfdw(fymx.getYfdw());//药房单位
				bean.setFzbl(fymx.getFzbl());//分装比例
				bean.setKcsl(-fymx.getFysl());//入库数量
				bean.setPcty("0");
				bean.setRkrq(new Date());
				insertypkc.add(bean);
			}
		}
		if (updateypkc.size() > 0 ){
			map.put("updateypkc", updateypkc);
		}
		if (insertypkc.size() > 0 ){
			map.put("insertypkc", insertypkc);
		}
		return map;
	}
	/*
	 * 根据退药明细处理住院费用
	 */
	public Map<String, Object> addbrfy(List<Hsz_yz_fymxModel> insertfymx,
			List<YfbYfywBqbyYpfycxModel> ypzldyfys,Zyb_rydjModel rydj,UserInfoModel userinfo,Date ldDate){
		Map<String, Object> map = new HashMap<>();
		map.put("mess", "");
		List<Zyb_brfyModel> brfylist = new ArrayList<Zyb_brfyModel>();
		for (Hsz_yz_fymxModel fyxm : insertfymx) {
			String zlbm = fyxm.getYpzl();
			String zyh = fyxm.getZyh();
			String fybm = "";
			YfbYfywBqbyYpfycxModel ypzldyfy = new YfbYfywBqbyYpfycxModel();
			//循环药品种对应费用
			for (YfbYfywBqbyYpfycxModel zlfy : ypzldyfys) {
				if (zlfy.getYpzlbm().equals(zlbm)){
					ypzldyfy = zlfy;
					fybm = zlfy.getMxfybm();
					break;
				}
			}
			if (fybm == null || fybm.equals("")){
				map.put("mess", "药品种类停用或种类未对应费用!");
				map.put("code", "false");
				return map;
			}
			//查找是否有存在的费用记录
			String newbz = "1";
			for (Zyb_brfyModel brfy : brfylist) {
				if (brfy.getZyh().equals(zyh) && brfy.getMxfyxmbm().equals(fybm) && brfy.getYyrq().equals(fyxm.getYyrq()) ){
					brfy.setFyje(CommonUtil.doubleAdd(brfy.getFyje() , CommonUtil.doubleRound(fyxm.getYplj(),fyxm.getFysl())));
					brfy.setFydj(brfy.getFyje());
					newbz = "0";
				}
			}
			if (newbz == "1"){
				Zyb_brfyModel fy = new Zyb_brfyModel();
				fy.setFylb(ypzldyfy.getLbbm());
				fy.setFylbmc(ypzldyfy.getLbmc());
				fy.setMxfyxmbm(ypzldyfy.getMxfybm());
				fy.setMxfyxmmc(ypzldyfy.getMxfymc());
				fy.setFydj(CommonUtil.doubleRound(fyxm.getYplj(),fyxm.getFysl()));
				fy.setFysl(1.0);
				fy.setFyje(fy.getFydj());
				fy.setSfrq(fyxm.getYyrq());
				fy.setZyh(fyxm.getZyh());
				fy.setZyks(fyxm.getKsbm());
				fy.setZyksmc(fyxm.getKsmc());
				fy.setZyys(fyxm.getYsbm());
				fy.setZyysxm(fyxm.getYsxm());
				fy.setYsbm(fyxm.getYsbm());
				fy.setYsxm(fyxm.getYsxm());
				fy.setYsks(fyxm.getYsks());
				fy.setYsksmc(fyxm.getYsksmc());
				fy.setZxks(fyxm.getZxks());
				fy.setZxksmc(fyxm.getZxksmc());
				fy.setYfbm(fyxm.getYfbm());
				fy.setYfmc(fyxm.getYfmc());
				fy.setSftf("0");
				fy.setYxbz("1");
				fy.setDjrq(ldDate);
			    fy.setBzsm("退药审核扣费");
				fy.setYhbl(0.0);
				fy.setYhje(0.0);
				fy.setCzybm(userinfo.getCzybm());
				fy.setCzyxm(userinfo.getCzyxm());
				fy.setBrid(rydj.getBrid());
				fy.setYyrq(fyxm.getYyrq());
				fy.setLsrq(DateTimeUtil.getChar8En(fyxm.getYyrq()));
				brfylist.add(fy);
			}

		}
		//List<Zyb_brfyModel>
		map.put("code", "true");
		map.put("brfylist", brfylist);
		return map;

	}

	/**
	 * 退药单查询
	 */
	@Override
	public UtilResponse queryTysq(UtilRequest msg) {
		UtilResponse result = new UtilResponse().newInstance();
		try{
			Hsz_tysqModel bean = (Hsz_tysqModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Hsz_tysqCxModel> list = (List<Hsz_tysqCxModel>)hsz_tysqModelMapper.queryTysqBrxx(bean);
			result.getResResult().put("list", list);
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("病区退药单查询失败:"+e.getCause().getMessage());
            logger.error("病区退药单查询接口【HszHlywTysqCspServiceImpl queryTysq】发生异常", e);
		}
		return result;
	}
	/**
	 * 作废护士退药申请
	 */
	@Override
	public UtilResponse deleteTysq(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Hsz_tysqModel bean = (Hsz_tysqModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_tysqModelMapper.deleteTysq(bean);
			result.getResResult().put("ref", ref.toString());
			//三医通系统接收 HIS
			HttpClientTools httpClientTools = new HttpClientTools();
			if(null != bean){
				List<JSONObject> dbResult = hsz_tysqModelMapper.queryQxFyPostList(bean);
				JSONArray finalPayload = JsonAssemblerForCancelAdvice.convertToCancelAdviceFormatLowerCase(dbResult);
				System.out.println(finalPayload.toJSONString());
				if(finalPayload.size() > 0){
					JSONObject jsbResult = httpClientTools.PostMethod(finalPayload,"qxty_url_post");
					if(200 == jsbResult.getInteger("code")){
						logger.info("调用三医通取消退药申请信息接口成功!: ", jsbResult);
					}else{
						logger.info("调用三医通取消退药申请信息接口失败!: ", jsbResult);
					}
				}
			}
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除【Hsz_tysqModel】护士退药申请信息失败:"+e.getCause().getMessage());
			logger.error("调用【Hsz_tysqModel】护士退药信息【HszHlywTysqCspServiceImpl deleteTysq】发生异常", e);
		}
		return result;
	}
}
