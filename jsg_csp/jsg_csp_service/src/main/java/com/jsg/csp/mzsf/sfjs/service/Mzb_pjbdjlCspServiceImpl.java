package com.jsg.csp.mzsf.sfjs.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.jsg.csp.api.mzsf.sfjs.pojo.Mzb_pjbdjlModel;
import com.jsg.csp.api.mzsf.sfjs.service.IMzb_pjbdjlCspService;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.csp.mzsf.sfjs.dao.New1Mzb_pjbdjlModelMapper;
/**
 *
* @ClassName: Mzb_pjbdjlCspServiceImpl
* @Description: TODO(票据补打记录)
* <AUTHOR>
* @date 2020年8月15日 上午11:51:00
*
 */
public class Mzb_pjbdjlCspServiceImpl extends ServiceInvocationHelper implements IMzb_pjbdjlCspService{
	private final Logger logger = LoggerFactory.getLogger(Mzb_pjbdjlCspServiceImpl.class);

	@Autowired
	private New1Mzb_pjbdjlModelMapper mzb_pjbdjlModelMapper;
	/**
	 * 添加门诊票据补打记录
	 */
	@Override
	public UtilResponse insert(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Mzb_pjbdjlModel bean=(Mzb_pjbdjlModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = mzb_pjbdjlModelMapper.insert(bean);
			result.getResResult().put("ref", ref.toString());

		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("添加票据补打记录失败:"+ex.getCause().getMessage());
			logger.error("调用添加票据补打记录接口【Mzb_pjbdjlCspServiceImpl inser】发生异常",ex);
		}
		return result;
	}
	/**
	 * 更新门诊票据补打记录
	 */
	@Override
	public UtilResponse update(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Mzb_pjbdjlModel bean=(Mzb_pjbdjlModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = mzb_pjbdjlModelMapper.update(bean);
			result.getResResult().put("ref", ref.toString());

		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("更新票据补打记录失败:"+ex.getCause().getMessage());
			logger.error("调用更新票据补打记录接口【Mzb_pjbdjlCspServiceImpl inser】发生异常",ex);
		}
		return result;
	}

}
