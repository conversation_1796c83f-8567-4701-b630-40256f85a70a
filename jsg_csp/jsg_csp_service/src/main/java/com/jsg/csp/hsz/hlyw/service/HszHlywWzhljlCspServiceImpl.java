package com.jsg.csp.hsz.hlyw.service;

import com.jsg.csp.api.hsz.hlyw.pojo.HszHlwsNewFxpgModel;
import com.jsg.csp.api.hsz.hlyw.pojo.Hsz_Hlws_NewModel;
import com.jsg.csp.api.hsz.hlyw.service.IHszHlywWzhljlCspService;
import com.jsg.csp.hsz.hlyw.dao.New1Hsz_Hlws_NewModelMapper;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.csp.pojo.UserInfoModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HszHlywWzhljlCspServiceImpl extends ServiceInvocationHelper implements IHszHlywWzhljlCspService{


	private final Logger logger = LoggerFactory.getLogger(HszHlywWzhljlCspServiceImpl.class);
	@Autowired
	private New1Hsz_Hlws_NewModelMapper hsz_Hlws_NewModelMapper;
	//@Autowired
	//private Emr_HljlMapper emrHljlMapper;

	@Override
	public UtilResponse insert(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Hsz_Hlws_NewModel bean = (Hsz_Hlws_NewModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_Hlws_NewModelMapper.insertSelective(bean);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("保存危重护理记录失败:"+e.getCause().getMessage());
			logger.error("调用保存危重护理记录接口【HszHlywWzhljlCspServiceImpl insert】发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse update(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Hsz_Hlws_NewModel beans = (Hsz_Hlws_NewModel)msg.getParam().get("beans");
			beans.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_Hlws_NewModelMapper.update(beans);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("更新危重护理记录失败:"+e.getCause().getMessage());
			logger.error("调用更新危重护理记录接口【HszHlywWzhljlCspServiceImpl update】发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse delete(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Hsz_Hlws_NewModel beans = (Hsz_Hlws_NewModel)msg.getParam().get("beans");
			beans.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_Hlws_NewModelMapper.delete(beans);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除危重护理记录失败:"+e.getCause().getMessage());
			logger.error("调用删除危重护理记录接口【HszHlywWzhljlCspServiceImpl delete】发生异常", e);
		}
		return result;
	}
	/**
	 * 查询风险评估
	 */
	@Override
	public UtilResponse queryFxpg(UtilRequest msg) {
		UtilResponse result = new UtilResponse().newInstance();
		try{
			HszHlwsNewFxpgModel bean = (HszHlwsNewFxpgModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Hsz_Hlws_NewModel> list = (List<Hsz_Hlws_NewModel>)hsz_Hlws_NewModelMapper.queryFxpg(bean);
			result.getResResult().put("list", list);
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("查询风险评估失败:"+e.getCause().getMessage());
			logger.error("调用查询风险评估接口【HszHlywWzhljlCspServiceImpl queryFxpg】发生异常", e);
		}
		return result;
	}
	@Override
	public UtilResponse queryAll(UtilRequest msg) {
		UtilResponse result = new UtilResponse().newInstance();
		try{
			Hsz_Hlws_NewModel bean = (Hsz_Hlws_NewModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			List<Hsz_Hlws_NewModel> list = (List<Hsz_Hlws_NewModel>)hsz_Hlws_NewModelMapper.queryAll(bean);
			result.getResResult().put("list", list);
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("危重护理记录查询失败:"+e.getCause().getMessage());
            logger.error("调用危重护理记录息接口【HszHlywWzhljlCspServiceImpl selecyByid】发生异常", e);
		}
		return result;
	}
	@Override
	public UtilResponse emr_getHljl(UtilRequest msg) {
		UtilResponse result = new UtilResponse().newInstance();
		try{
			Map<String,Object> bean = (Map<String,Object>)msg.getParam().get("bean");
			bean.put("czybm", ((UserInfoModel)msg.getUserinfo().get("userinfo")).getCzybm());
			String str = "";
			Integer xhcs = (Integer) bean.get("xhcs");
			for (int i = 1; i <= xhcs; i++) {
				try {
					bean.put("id", ""+i);
					bean.put("refcursor", new ArrayList<Map<String, Object>>());
					//emrHljlMapper.getHljl(bean);
					ArrayList<Map<String, Object>> arr = (ArrayList<Map<String, Object>>)bean.get("refcursor");
					if(arr.size() > 0){
						Object s = arr.get(0);
						if(null != s){
							Map<String, Object> a = (Map<String, Object>) s;
							if (!a.get("zyz").equals(";")){
								str += a.get("zyz");
							}
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			result.getResResult().put("str", str);
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("危重护理记录查询失败:"+e.getCause().getMessage());
			logger.error("调用危重护理记录息接口【HszHlywWzhljlCspServiceImpl selecyByid】发生异常", e);
		}
		return result;
	}
	@Override
	public UtilResponse selecyByid(UtilRequest msg) {
		UtilResponse result = new UtilResponse().newInstance();
		try{
			Hsz_Hlws_NewModel bean = (Hsz_Hlws_NewModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Hsz_Hlws_NewModel hl = hsz_Hlws_NewModelMapper.selectByid(bean);
			result.getResResult().put("bean", hl);
		}catch(Exception e){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("危重护理记录查询失败:"+e.getCause().getMessage());
            logger.error("调用危重护理记录息接口【HszHlywWzhljlCspServiceImpl selecyByid】发生异常", e);
		}
		return result;
	}
	@Override
	public UtilResponse addFxpg(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			HszHlwsNewFxpgModel bean = (HszHlwsNewFxpgModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_Hlws_NewModelMapper.addFxpg(bean);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("保存风险评估记录失败:"+e.getCause().getMessage());
			logger.error("调用保存风险评估记录接口【HszHlywWzhljlCspServiceImpl addFxpg】发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse updateFxpg(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			HszHlwsNewFxpgModel beans = (HszHlwsNewFxpgModel)msg.getParam().get("bean");
			beans.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_Hlws_NewModelMapper.updateFxpg(beans);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("更新风险评估记录失败:"+e.getCause().getMessage());
			logger.error("调用更新风险评估记录接口【HszHlywWzhljlCspServiceImpl updateFxpg】发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse deleteFxpg(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			HszHlwsNewFxpgModel beans = (HszHlwsNewFxpgModel)msg.getParam().get("bean");
			beans.setYljgbm(msg.getYljgbm());
			Integer ref = hsz_Hlws_NewModelMapper.deleteFxpg(beans);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除危风险评估记录失败:"+e.getCause().getMessage());
			logger.error("调用删除风险评估记录接口【HszHlywWzhljlCspServiceImpl deleteFxpg】发生异常", e);
		}
		return result;
	}
}
