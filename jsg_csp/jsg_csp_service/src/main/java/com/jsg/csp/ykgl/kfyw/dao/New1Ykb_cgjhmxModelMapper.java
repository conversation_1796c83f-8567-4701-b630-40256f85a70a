package com.jsg.csp.ykgl.kfyw.dao;

import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_cgHistoryDto;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_cgjhModel;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_cgjhmxModel;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_cgjhmxjlModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @ClassName: Ykb_cgjhModelMapper
 * @Description: TODO(采购计划明细数据操作)
 * <AUTHOR> YK
 * @date 2020年8月22日 下午8:02:56
 *
 */
public interface New1Ykb_cgjhmxModelMapper {

	/**
	 * 查询采购计划明细
	 */
	List<Ykb_cgjhmxModel> seMxList(Ykb_cgjhModel model);

	/**
	 * 保存采购计划明细
	 */
	int saveCgjhmx(@Param("sqlall") String sqlall);

	/**
	 * 修改采购计划明细供货单位
	 */
	int updateCgjhmxGhdw(List<Ykb_cgjhmxModel> list);

	/**
	 * 修改采购计划明细
	 */
	int updateCgjhmx(List<Ykb_cgjhmxModel> list);

	int saveCgjhmxTmp(List<com.jsg.csp.api.ykgl.kfwh.pojo.Ykb_ypzdModel> list);


	int updateCgjhMxtmp(Ykb_cgjhmxModel mxmodel);

	int deleteCgjhMxtmp(Ykb_cgjhmxModel mxmodel);

	/**
	 * 保存采购计划明细记录
	 */
	int saveCgjhmxjl(List<Ykb_cgjhmxjlModel> list);

	/**
	 * 查询采购计划明细记录
	 */
	List<Ykb_cgjhmxjlModel> seMxjlList(Ykb_cgjhModel model);

	/**
	 * 保存采购计划明细记录
	 */
	int saveCgjhmxjl(Ykb_cgjhmxjlModel model);

	int deleteCgjhmx(Ykb_cgjhModel upCgjh);

	/**
	 * 查询选中的药品编码数据
	 * @param model
	 * @return
	 */
	List<Ykb_cgjhmxModel> getYfKc(Ykb_cgjhmxjlModel model);


	List<Ykb_cgHistoryDto> getCgHistoryList(Ykb_cgjhmxjlModel model);

	int queryMxTmpCount(Ykb_cgjhmxModel mxmodel);


	List<Ykb_cgjhmxModel> queryMxTmpAll(Ykb_cgjhmxModel ykb_cgjhmxModel);

	int saveCgjhmxTmpT(List<Ykb_cgjhmxModel> list);


}
