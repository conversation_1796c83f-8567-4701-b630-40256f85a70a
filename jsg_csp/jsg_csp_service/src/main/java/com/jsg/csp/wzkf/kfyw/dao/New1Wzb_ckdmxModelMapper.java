package com.jsg.csp.wzkf.kfyw.dao;

import java.util.List;

import com.jsg.csp.api.wzkf.kfyw.pojo.Wzb_ckdModel;
import com.jsg.csp.api.wzkf.kfyw.pojo.Wzb_ckdmxModel;
import com.jsg.csp.api.yfgl.kcgl.pojo.Yfb_ksckdmxModel;

/**
 *
 * @ClassName: Wzb_ckdmxModelMapper
 * @Description: TODO(物资入库明显数据操作接口)
 * <AUTHOR>
 * @date 2020年9月14日 下午8:44:26
 *
 */
public interface New1Wzb_ckdmxModelMapper {
	// 查询入库单明显
	List<Wzb_ckdmxModel> selectCkdmx(Wzb_ckdModel model);

	// 新增入库单明显
	int insertCkdmx(List<Wzb_ckdmxModel> list);

	// 新增科室入库单明显
	int insertKsCkdmx(List<Yfb_ksckdmxModel> list);

}
