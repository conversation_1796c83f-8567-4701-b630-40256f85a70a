package com.jsg.csp.pubfun.dao;

import com.jsg.csp.api.pubfun.pojo.ypkc.*;
import com.jsg.csp.api.yfgl.kcgl.pojo.Yfb_ckdmxModel;
import com.jsg.csp.api.yfgl.kcgl.pojo.Yfb_pdbModel;
import com.jsg.csp.api.yfgl.kcgl.pojo.Yfb_pdbmxModel;
import com.jsg.csp.api.yfgl.kcgl.pojo.Yfb_ypkcModel;
import com.jsg.csp.api.ykgl.kccx.pojo.Ykb_ypkcModel;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_pdbModel;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_pdbmxModel;

import java.util.List;

public interface YpFunModelMapper {

	/*
	 * 库房库存
	 */
	List<Ykb_ypkcModel> Getkfkc(YpkcMsgModel kcmsg);
	/*
	 * 库房出入单据号
	 */
	YkKcCrdjhModel GetKfCrdjh(YpkcMsgModel record);
	/*
	 * 根据出入单据号查询出入明细  GetYkcrmx
	 */
	List<YkcrmxModel> GetYkcrmx(YpkcMsgModel record);
	/*
	 * 药库库存入库更新
	 */
	int updateYkypkcRk(YkKcSaveModel ykkc);
	/*
	 * 药库库存出库更新
	 */
	int updateYkypkcCk(YkKcSaveModel ykkc);
	/*
	 * 药库库存出库更新
	 */
	int updateYftoKsCk(List<Yfb_ckdmxModel> ykkc);
	/*
	 * 药库库存插入
	 */
	int insertYkypkc(List<YkKcSaveModel> ykkc);

	/*
	 * 药房库存
	 */
	List<Yfb_ypkcModel> Getyfkc(YpkcMsgModel kcmsg);

	/**
	 * 查询药房未占用的库存
	 * 医嘱摆药时用
	 */
	List<Yfb_ypkcModel> GetyfWzyKc(YpkcMsgModel kcmsg);

	/*
	 * 药房处方出入明细查询
	 */
	List<YfcrmxModel> getYfcrmx(YpkcMsgModel record);
	/*
	 * 药品处方发药
	 */
	List<YfcrmxModel> getYfcffy(YpkcMsgModel record);
	/*
	 * 药品处方退药
	 */
	List<YfcrmxModel> getYfcfty(YpkcMsgModel record);

	/*
	 * 药房库存入库更新
	 */
	int updateYfypkcRk(YfKcSaveModel ykkc);
	/*
	 * 科室库存入库更新
	 */
	int updateKsRk(Yfb_ypkcModel yfb_ypkcModel);
	/*
	 * 药房库存出库更新
	 */
	int updateYfypkcCk(YfKcSaveModel ykkc);
	/*
	 * 药房库存插入
	 */
	int insertYfypkc(List<YfKcSaveModel> ykkc);
	/*
	 * 库房盘点表明细处理明细
	 */
	List<Ykb_pdbmxModel> getYkpdbmx(Ykb_pdbmxModel rerord);
	/*
	 * 库房盘点表查询
	 */
	Ykb_pdbModel getYkpdb(Ykb_pdbModel rerord);
	/*
	 * 药房盘点表明细处理明细
	 */
	List<Yfb_pdbmxModel> getYfpdbmx(Yfb_pdbmxModel rerord);
	/*
	 * 药房盘点表查询
	 */
	Yfb_pdbModel getYfpdb(Yfb_pdbModel rerord);

	Integer selectYpxx(YfKcSaveModel yfKcSaveModel);

	Integer updateCkdmx(List<YfcrmxModel> list);
}
