package com.jsg.csp.ykgl.kfyw.dao;

import com.jsg.csp.api.pubfun.pojo.ypkc.YpkcMsgModel;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_tjdModel;
import com.jsg.csp.api.ykgl.kfyw.pojo.Ykb_tjdmxModel;

import java.util.List;

/**
 * <AUTHOR> YK
 * @ClassName: Ykb_tjdModelMapper
 * @Description: TODO(调价单)
 * @date 2020年8月7日 下午4:26:12
 */
public interface New1Ykb_tjdModelMapper {
    /*
     * 保存调价单
     */
    int saveTJD(Ykb_tjdModel record);

    /*
     * 审核作废调价单
     */
    int updateTJd(Ykb_tjdModel record);

    /*
     * 审核调价单，修改药库库存和药房库存价格
     */
    int updatePassTJd(List<Ykb_tjdmxModel> record);

    /*
     * 审核中根据条件查询调价单
     */
    List<Ykb_tjdModel> queryTjdForPass(Ykb_tjdModel record);

    /*
     * 开单中根据条件查询调价单，按药品编码
     */
    List<Ykb_tjdmxModel> queryTjdForSelBm(YpkcMsgModel record);

    /*
     * 开单中根据条件查询调价单，按批次
     */
    List<Ykb_tjdmxModel> queryTjdForSelPc(YpkcMsgModel record);

    /*
     * 新增开单中根据条件查询调价单，按批次
     */
    List<Ykb_tjdmxModel> newqueryTjdForSelPc(YpkcMsgModel record);

    /*
     * 药品库存详情
     */
    List<Ykb_tjdmxModel> newqueryYpkcDetail(YpkcMsgModel record);

    /**
     * 获取药品是否被使用
     *
     * @param record
     * @return
     */
    Integer getYpSy(YpkcMsgModel record);

    int update(Ykb_tjdModel record);

}
