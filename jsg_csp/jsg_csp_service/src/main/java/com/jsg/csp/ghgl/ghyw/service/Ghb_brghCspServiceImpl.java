package com.jsg.csp.ghgl.ghyw.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsg.common.pojo.CsqxInfoModel;
import com.jsg.common.pojo.IniConfig;
import com.jsg.common.pojo.SysIniConfig;
import com.jsg.common.util.ConfigurationRead;
import com.jsg.common.util.DateTimeUtil;
import com.jsg.common.util.Utilpubfun;
import com.jsg.csp.TimedTask.dao.GhinfoDataMapper;
import com.jsg.csp.api.TimedTask.pojo.MedicalJchrModel;
import com.jsg.csp.api.TimedTask.pojo.MedicalRecordModel;
import com.jsg.csp.api.pubfun.pojo.getxh.GetMaxBmModel;
import com.jsg.csp.api.pubfun.pojo.getxh.GetXhidMsgModel;
import com.jsg.csp.api.pubfun.pojo.getxh.GetXhidResModel;
import com.jsg.csp.api.pubfun.pojo.pj.Gyb_fphMsgModel;
import com.jsg.csp.api.pubfun.pojo.pj.Gyb_fphResModel;
import com.jsg.csp.api.pubfun.pojo.pj.Gyb_pjjlModel;
import com.jsg.csp.api.pubfun.service.IPjhclCspService;
import com.jsg.csp.api.pubfun.service.IPubFunCspService;
import com.jsg.csp.api.ghgl.ghyw.pojo.*;
import com.jsg.csp.api.ghgl.ghyw.service.IGhb_brghCspService;
import com.jsg.csp.api.mzsf.sfjs.pojo.Mzb_brfyModel;
import com.jsg.csp.api.mzsf.sfjs.pojo.Mzb_jsjlModel;
import com.jsg.csp.api.xtwh.ylfwxm.service.IGyb_ylxx_xgjlCspService;
import com.jsg.csp.api.zygl.crygl.pojo.Pkh_ryxxModel;
import com.jsg.csp.pubfun.dao.PjhclModelMapper;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.csp.pojo.UserInfoModel;
import com.jsg.csp.ghgl.ghyw.dao.*;
import com.jsg.csp.mzsf.sfjs.dao.New1Mzb_brfyModelMapper;
import com.jsg.csp.mzsf.sfjs.dao.New1Mzb_jsjlModelMapper;
import com.jsg.csp.zygl.crygl.dao.New1Pkh_ryxxModelMapper;
import com.wondersgroup.common.decrypt.utils.Sm4HexDe;
import com.wondersgroup.common.endecrypt.utils.Sm4HexEn;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.*;

public class Ghb_brghCspServiceImpl extends ServiceInvocationHelper implements IGhb_brghCspService {
    ConfigurationRead read = new ConfigurationRead("/", "properties/csp.properties");
    private final Logger logger = LoggerFactory.getLogger(Ghb_brghCspServiceImpl.class);
    @Autowired
    private New1Ghb_brghModelMapper Ghb_brghModelMapper;
    @Autowired
    private New1Ghb_yshycModelMapper ghb_yshycModelMapper;
    @Autowired
    private New1Gyb_brjbxxModelMapper Gyb_brjbxxModelMapper;
    @Autowired
    private New1Gyb_brylkxxModelMapper Gyb_brylkxxModelMapper;
    @Autowired
    private New1Mzb_brfyModelMapper Mzb_brfyModelMapper;
    @Autowired
    private New1Mzb_jsjlModelMapper Mzb_jsjlModelMapper;
    @Autowired
    private IPjhclCspService IPjhclCspService;
    @Autowired
    private GhinfoDataMapper medicalRecordMapper;
    @Autowired
    IPubFunCspService pubFunCspService;
    @Autowired
    private New1Pkh_ryxxModelMapper pkh_ryxxModelMapper;
    @Autowired
    private PjhclModelMapper pjhclModelMapper; //发票号码
    @Autowired
    private IGyb_ylxx_xgjlCspService gyb_ylxx_xgjlCspService;
    @Autowired
    private MjzFzglModelMapper mjzFzglModelMapper;

    @Override
    public UtilResponse queryFzxx(MjzFzgl fzId) {
        UtilResponse result = UtilResponse.newInstance();
        MjzFzgl mjzFzgl = mjzFzglModelMapper.queryMjz_fzgl(fzId);
        result.getResResult().put("bean", mjzFzgl);
        return result;
    }

    /**
     * 查询效期内的挂号记录
     */
    @Override
    public UtilResponse queryJzlb(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            UserInfoModel userInfoModel = (UserInfoModel) msg.getUserinfo().get("user");
            List<CsqxInfoModel> csqxinfo = (List<CsqxInfoModel>) msg.getParam().get("csqxinfo");
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            String csz = Utilpubfun.getCsqx(csqxinfo, "N01006400146", null);//是否使用五笔编码
            if ("1".equals(csz)) {
                bean.setWbjm("1");
            }
            //单条记录查询时判断是否有接诊医生
            String ghxh = bean.getGhxh();
            String jzys = bean.getJzys();
            if (ghxh != null && !ghxh.equals("") && jzys != null && !jzys.equals("")) {
                Ghb_brghModel beanone = Ghb_brghModelMapper.queryGhb_brghOne(bean);
                if (beanone != null) {
                    if (beanone.getJzys() == null || beanone.getJzys().equals("")
                            || beanone.getGhks() == null || beanone.getGhks().equals("")) {
                        Ghb_brghModel beanupdate = new Ghb_brghModel();
                        beanupdate.setGhxh(ghxh);
                        beanupdate.setJzys(jzys);
                        beanupdate.setJzysxm(userInfoModel.getCzyxm());
                        beanupdate.setJzsj(new Date());
                        beanupdate.setGhks(bean.getGhks());
                        beanupdate.setGhksmc(bean.getGhksmc());
                        //beanupdate.setJzbz("1");
                        beanupdate.setYljgbm(msg.getYljgbm());
                        Ghb_brghModelMapper.update(beanupdate);
                    }
                }
            }
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Ghb_brghModel> list = (List<Ghb_brghModel>) Ghb_brghModelMapper.queryJzlb(bean);
            PageInfo<Ghb_brghModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("Ghb_brgh病人挂号查询失败:" + ex.getCause().getMessage());
            logger.error("调用Ghb_brgh病人挂号信息接口【Ghb_brghCspServiceImpl queryJzlb】发生异常", ex);
        }
        return result;
    }


    /**
     * 查询所有病人病人挂号
     */
    @Override
    public UtilResponse queryGhb_brghList(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Ghb_brghModel> list = (List<Ghb_brghModel>) Ghb_brghModelMapper.queryGhb_brgh(bean);
            PageInfo<Ghb_brghModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("Ghb_brgh病人挂号查询失败:" + ex.getCause().getMessage());
            logger.error("调用Ghb_brgh病人挂号信息接口【Ghb_brghCspServiceImpl queryGhb_brghList】发生异常", ex);
        }
        return result;
    }

    /**
     * 返回挂号信息
     *
     * @param msg
     * @return
     */
    @Override
    public UtilResponse queryGhxxByBrxx(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Ghb_brghModel> list = Ghb_brghModelMapper.queryGhxxByBrxx(bean);
            PageInfo<Ghb_brghModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("Ghb_brgh病人挂号查询失败:" + ex.getCause().getMessage());
            logger.error("调用Ghb_brgh病人挂号信息接口【Ghb_brghCspServiceImpl queryGhxxByBrxx】发生异常", ex);
        }
        return result;
    }

    /**
     * 查询单个挂号信息
     */
    @Override
    public UtilResponse queryGhb_brghOne(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel ghbean = (Ghb_brghModel) msg.getParam().get("bean");
            ghbean.setYljgbm(msg.getYljgbm());
            Ghb_brghModel bean = Ghb_brghModelMapper.queryGhb_brghOne(ghbean);

            String rybh = Ghb_brghModelMapper.getGhrybh(ghbean);

            bean.setRybh(rybh);

            result.getResResult().put("bean", bean);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病人挂号查询失败:" + ex.getCause().getMessage());
            logger.error("调用查询单个病人挂号信息接口【Ghb_brghCspServiceImpl queryGhb_brghOne】发生异常", ex);
        }
        return result;
    }


    /**
     * 病人挂号 新增加
     */
    @Override
    public UtilResponse insert(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            Integer ref = Ghb_brghModelMapper.insert(bean);
            result.getResResult().put("ref", ref.toString());

        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病人挂号保存失败:" + ex.getCause().getMessage());
            logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl savebatch】发生异常", ex);
        }
        return result;
    }

    /**
     * 病人挂号 修改
     */
    @Override
    public UtilResponse update(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            Integer ref = Ghb_brghModelMapper.update(bean);
            result.getResResult().put("ref", ref.toString());

        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病人挂号保存失败:" + ex.getCause().getMessage());
            logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl savebatch】发生异常", ex);
        }
        return result;
    }


    /**
     * 病人挂号保存（自动处理ghb_brgh和gyb_brjbxx两张表）
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse Brgh_Save(UtilRequest msg) throws Exception {

        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel ghxx = (Ghb_brghModel) msg.getParam().get("ghxx");
            Gyb_brjbxxModel zcxx = (Gyb_brjbxxModel) msg.getParam1().get("zcxx");
            Gyb_brylkxxModel ylkxx = (Gyb_brylkxxModel) msg.getParam2().get("ylkxx");
            List<Mzb_brfyModel> mzfyS = (List<Mzb_brfyModel>) msg.getParam3().get("mzfy");
            List<Mzb_jsjlModel> jsjlS = (List<Mzb_jsjlModel>) msg.getParam4().get("jsjl");
            UserInfoModel userinfo = (UserInfoModel) msg.getParam().get("userinfo");//用户信息
            List<SysIniConfig> sysiniinfo = (List<SysIniConfig>) msg.getParam().get("sysiniinfo");//本地参数
            //取参数
            String csz206 = (String) msg.getParam().get("csz206"); //挂号收费方式 0=只挂号不收费(由收费室收费),1=挂号同步收费
            String csz223 = (String) msg.getParam().get("csz223"); //挂号票是否与门诊票共用
            String csz252 = (String) msg.getParam().get("csz252"); //宣汉就诊卡
            String lstd = (String) msg.getParam().get("lstd");//绿色通道
            if (null == lstd) {
                lstd = "0";
            }
            String ywckbm = null;
            if (sysiniinfo != null) {
                IniConfig iniconfig = Utilpubfun.getSysconfig(sysiniinfo, "000001");//业务窗口编码
                ywckbm = iniconfig.getCsz();//业务窗口编码
            }

            //处理医疗机构编码
            if (ghxx != null) {
                ghxx.setYljgbm(msg.getYljgbm());
            }
            //绿色通道
            if (ghxx != null && lstd.equals("1")) {
                ghxx.setLstdbz("1");
                GetMaxBmModel bean = new GetMaxBmModel();
                bean.setTablename("ghb_brgh");
                bean.setColumnname("lstdxh");
                msg.getParam().put("bean", bean);
                UtilResponse lstdxhre = pubFunCspService.GetMaxNumLstdxh(msg);
                String lstdxh = (String) lstdxhre.getResResult().get("maxbm");
                ghxx.setLstdxh(lstdxh);
            }
            if (zcxx != null) {
                zcxx.setYljgbm(msg.getYljgbm());
            }
            if (ylkxx != null) {
                ylkxx.setYljgbm(msg.getYljgbm());
            }

            if (jsjlS != null && jsjlS.size() > 0) {
                for (Mzb_jsjlModel jsjl : jsjlS) {
                    jsjl.setYljgbm(msg.getYljgbm());
                }
            }

            //********************  处理更新  **********************
            Integer zcxxRef = 0, ghxxRef = 0, ylkxxRef = 0, fyxxRef = 0, jsxxRef = 0, hycxxRef = 0;

            //注册信息提交
            String zcxxType = zcxx.getType();
            if (zcxxType != null && zcxxType.equals("insert")) {
                zcxxRef = Gyb_brjbxxModelMapper.insert(zcxx);
            } else {
                //写修改记录
                if (zcxx.getBrid() != null && !zcxx.getBrid().equals("")) {
                    Gyb_brjbxxModel pve = Gyb_brjbxxModelMapper.queryGyb_brjbxxOne(zcxx);
                    if (pve != null) {
                        String logs = getChange(zcxx, pve);
                        if (logs != null && !logs.equals("")) {
                            msg.getParam().put("xmbm", zcxx.getBrid());//项目编码
                            msg.getParam().put("logs", logs);//修改日志内容
                            msg.getParam().put("xglb", "04");//修改类别
                            gyb_ylxx_xgjlCspService.insert(userinfo, msg);
                        }
                    }
                }
                zcxxRef = Gyb_brjbxxModelMapper.update(zcxx);
            }

            //判断更新行数
            if (zcxxRef <= 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("病人挂号保存 【注册信息保存】 失败:    提交行数：" + zcxxRef);
                logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】失败， 【注册信息保存】 提交行数：" + zcxxRef);
                throw new Exception();  //异常自动返回  并且SQL事务回滚
            }


            //判断医疗卡是否新加
            String ylkxxType = ylkxx.getType();
            if (ylkxxType != null && ylkxxType.equals("insert")) {
                List<Gyb_brylkxxModel> ylk_Beans = new ArrayList<Gyb_brylkxxModel>();
                ylk_Beans.add(ylkxx);
                try {
                    ylkxxRef = Gyb_brylkxxModelMapper.insert(ylk_Beans);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //判断更新行数（批量更新成功后返回-1）
                if (ylkxxRef != -1) {
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg("病人挂号保存 【医疗卡信息保存】 失败:    提交行数：" + ylkxxRef);
                    logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】失败， 【医疗卡信息保存】 提交行数：" + ylkxxRef);
                    throw new Exception();  //异常自动返回  并且SQL事务回滚
                }
            }

            if ("1".equals(csz252)) {//宣汉院内就诊卡
                if (ylkxxType != null && ylkxxType.equals("update")) {
                    List<Gyb_brylkxxModel> ylk_Beans = new ArrayList<Gyb_brylkxxModel>();
                    ylk_Beans.add(ylkxx);
                    ylkxxRef = Gyb_brylkxxModelMapper.update(ylk_Beans);

                    //判断更新行数（批量更新成功后返回-1）
                    if (ylkxxRef != -1) {
                        result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                        result.setResultMsg("病人挂号保存 【医疗卡信息保存】 失败:    提交行数：" + ylkxxRef);
                        logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】失败， 【医疗卡信息保存】 提交行数：" + ylkxxRef);
                        throw new Exception();  //异常自动返回  并且SQL事务回滚
                    }
                }
            }

            //挂号信息提交
            //这里先根据身份证件号码查询该病人是否是贫困户
            if (zcxx.getSfzjhm() != null && !zcxx.getSfzjhm().equals("")) {
                Pkh_ryxxModel pkhBean = new Pkh_ryxxModel();
                pkhBean.setZjhm(zcxx.getSfzjhm());
                Pkh_ryxxModel resultPkh = pkh_ryxxModelMapper.queryByZjhm(pkhBean);
                //判断是否是贫困户
                if (resultPkh != null) {
                    ghxx.setPkhbz("1");
                }
            }

            String ghxxType = ghxx.getType();
            if (ghxxType != null && ghxxType.equals("insert")) {

                // 赋值最大科室挂号序号与医生最大挂号序号
                Ghb_brghModel obj = new Ghb_brghModel();
                obj.setGhks(ghxx.getGhks());
                obj.setJzys(ghxx.getJzys());
                obj.setYljgbm(msg.getYljgbm());
                obj.setGhrq(ghxx.getGhrq());
                Ghb_brghModel res = Ghb_brghModelMapper.queryMaxXh(obj);

                ghxx.setYsxh(res.getYsxh());
                ghxx.setKsxh(res.getKsxh());

                ghxxRef = Ghb_brghModelMapper.insert(ghxx);
            } else {
                //写修改记录
                if (ghxx.getGhxh() != null && !ghxx.getGhxh().equals("")) {
                    Ghb_brghModel pve = Ghb_brghModelMapper.queryGhb_brghOne(ghxx);
                    if (pve != null) {
                        String logs = getChangegh(pve, ghxx);
                        if (logs != null && !logs.equals("")) {
                            msg.getParam().put("xmbm", zcxx.getBrid());//项目编码
                            msg.getParam().put("logs", logs);//修改日志内容
                            msg.getParam().put("xglb", "04");//修改类别
                            gyb_ylxx_xgjlCspService.insert(userinfo, msg);
                        }
                    }
                }
                ghxxRef = Ghb_brghModelMapper.update(ghxx);
            }

            //更新发票返回
            List<Gyb_fphResModel> fpulist = null;
            //判断更新行数
            if (ghxxRef <= 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("病人挂号保存 【挂号信息保存】 失败:    提交行数：" + ghxxRef);
                logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】失败， 【挂号信息保存】 提交行数：" + zcxxRef);
                throw new Exception();  //异常自动返回  并且SQL事务回滚
            }


            //挂号是新增的时候 才插入费用和结算记录更新号源池
            if (ghxxType != null && ghxxType.equals("insert")) {
                //结算信息提交  挂号同步收费并且非绿色通道
                if (csz206.equals("1") && lstd.equals("0")) {
                    if (jsjlS.size() > 0) {
                        jsxxRef = Mzb_jsjlModelMapper.insert(jsjlS);
                        if (jsxxRef != -1) {  //判断更新行数
                            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                            result.setResultMsg("病人挂号保存 【结算记录信息保存】 失败:    提交行数：" + jsxxRef);
                            logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】 【结算记录信息保存】 失败， 【挂号信息保存】 提交行数：" + jsxxRef);
                            throw new Exception();  //异常自动返回  并且SQL事务回滚
                        }
                    }
                }
                //费用信息提交
                if (mzfyS.size() > 0) {
                    //取发票号码
                    if (csz206.equals("1") && lstd.equals("0")) {   //挂号收费模式
                        Gyb_fphMsgModel bean = new Gyb_fphMsgModel();
                        bean.setCzybm(userinfo.getCzybm());  //操作员
                        bean.setYwckbm(ywckbm);  //业务窗口
                        if (csz223.equals("0")) {
                            bean.setPjlx("01");      //票据类型
                        } else {
                            bean.setPjlx("02");      //票据类型
                        }
                        bean.setFpzs(1);         //使用发票张数
                        bean.setUpdatebz("0");   //不同步更新
                        msg.getParam().put("bean", bean);
                        UtilResponse result_fp = IPjhclCspService.getfphm(msg);
                        List<Gyb_pjjlModel> fp = (List<Gyb_pjjlModel>) result_fp.getResResult().get("list");
                        fpulist = (List<Gyb_fphResModel>) result_fp.getResResult().get("ulist");
                        if (fp == null || fp.size() <= 0) {
                            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                            result.setResultMsg("病人挂号保存 【费用信息保存】 失败:   获取发票失败：" + result_fp.getResultMsg());
                            logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】 【获取发票失败】 失败" + result_fp.getResultMsg());
                            throw new Exception();  //异常自动返回  并且SQL事务回滚
                        }
                        //挂号只取一张发票
                        for (Mzb_brfyModel mzfy : mzfyS) {
                            mzfy.setFphm(fp.get(0).getFphm());
                        }
                    } else {     //挂号不收费模式
                        //设置结算状态为0
                        for (Mzb_brfyModel mzfy : mzfyS) {
                            mzfy.setSfjs("0");
                        }
                    }

                    if (mzfyS != null && mzfyS.size() > 0) {
                        GetXhidMsgModel bean = new GetXhidMsgModel();
                        bean.setScfs("3");
                        bean.setCslx("GH");
                        msg.getParam().put("bean", bean);
                        UtilResponse res_cfh = pubFunCspService.GetIdNameLock(msg);
                        GetXhidResModel brbean = (GetXhidResModel) res_cfh.getResResult().get("bean");
                        String ghYzhm = brbean.getDqxh();

                        for (Mzb_brfyModel mzfy : mzfyS) {
                            mzfy.setYzhm(ghYzhm);
                            mzfy.setYljgbm(msg.getYljgbm());
                        }
                    }
                    fyxxRef = Mzb_brfyModelMapper.insert(mzfyS);

                    if (fyxxRef != -1) {  //判断更新行数
                        result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                        result.setResultMsg("病人挂号保存 【费用信息保存】 失败:    提交行数：" + fyxxRef);
                        logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】 【费用信息保存】 失败， 【挂号信息保存】 提交行数：" + jsxxRef);
                        throw new Exception();  //异常自动返回  并且SQL事务回滚
                    }
                }

                //*******更新号源池信息
                if (ghxx.getHyid() != null && !ghxx.getHyid().equals("")) { //号源id存在时才进行操作
                    //先判断是否当前号源是否允许使用
                    Ghb_yshycModel yshycBean = new Ghb_yshycModel();
                    yshycBean.setYljgbm(msg.getYljgbm());
                    yshycBean.setHyid(ghxx.getHyid());
                    Ghb_yshycModel resultYshyc = ghb_yshycModelMapper.queryOne(yshycBean);
                    if (resultYshyc.getHyzt().equals("1")) {
                        result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                        result.setResultMsg("该号源已被使用");
                        logger.error("调用查询单条号源记录接口【Ghb_brghCspServiceImpl queryOne】  号源已被使用");
                        throw new Exception();  //异常自动返回  并且SQL事务回滚
                    }
                    //进行号源的更新操作
                    Ghb_yshycModel updateYshyc = new Ghb_yshycModel();
                    updateYshyc.setHyzt("1");
                    updateYshyc.setYljgbm(msg.getYljgbm());
                    updateYshyc.setRyghxh(ghxx.getGhxh());
                    updateYshyc.setHyid(ghxx.getHyid());
                    hycxxRef = ghb_yshycModelMapper.update(updateYshyc);
                    //判断更新行数
                    if (hycxxRef <= 0) {
                        result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                        result.setResultMsg("医生号源池更新 【医生号源池更新】 失败:    提交行数：" + hycxxRef);
                        logger.error("调用医生号源池更新接口【Ghb_brghCspServiceImpl Brgh_Save】失败， 【医生号源池更新】 提交行数：" + hycxxRef);
                        throw new Exception();  //异常自动返回  并且SQL事务回滚
                    }
                }

            }
            //更新发票号
            //修改发票
            if (fpulist != null && fpulist.size() > 0) {
                Integer ref = pjhclModelMapper.Update_pjxx(fpulist);
            }
            //********************  处理更新 End **********************
            //返回成功标志
            result.setResultMsg("病人挂号保存成功！");
            result.getResResult().put("ref", 1);
            result.getResResult().put("gh_brfy", mzfyS);
            //检查检验
            if(null != zcxx.getSfzjhm()){
                List<MedicalJchrModel> validResults = new ArrayList<>();
                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                MedicalRecordModel medicalRecordModel = new MedicalRecordModel();
                medicalRecordModel.setJzrq(today);
                medicalRecordModel.setZjhm(zcxx.getSfzjhm());
                List<MedicalRecordModel> ghinfoList = medicalRecordMapper.findRecordsByDate(medicalRecordModel);
                if (ghinfoList != null && !ghinfoList.isEmpty()) {
                    for (MedicalRecordModel record : ghinfoList) {
                        try {
                            MedicalJchrModel resultHr = sendRequest(record);
                            if (resultHr != null) {
                                resultHr.setIndate(today);
                                validResults.add(resultHr);
                            }
                        } catch (Exception e) {
                            logger.error("调用 sendRequest 时异常，异常信息：{}",e.getMessage(), e);
                        }
                    }
                    if (!validResults.isEmpty()) {
                        medicalRecordMapper.jzhr_insert(validResults);
                    }
                    logger.info("筛选后的有效数据共 {} 条", validResults.size());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病人挂号保存失败:" + ex.getCause().getMessage());
            logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl savebatch】发生异常", ex);
            throw new Exception();
        }
        return result;
    }


    /**
     * 退号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UtilResponse Brgh_TH(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {

            Ghb_brghModel ghxx = (Ghb_brghModel) msg.getParam().get("ghxx");
            List<Mzb_brfyModel> mzfyS = (List<Mzb_brfyModel>) msg.getParam1().get("mzfy");
            List<Mzb_jsjlModel> jsjlS = (List<Mzb_jsjlModel>) msg.getParam2().get("jsjl");
            List<Mzb_brfyModel> updateGhf = (List<Mzb_brfyModel>) msg.getParam3().get("updateBrfyS");

            //********************  处理更新  **********************

            if (ghxx == null) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("病人退号保存【退号信息为空】");
                logger.error("【Ghb_brghCspServiceImpl Brgh_TH】 病人退号保存失败【退号信息为空】");
                throw new Exception();  //异常自动返回  并且SQL事务回滚
            }


            Integer ghxxRef = 0, fyxxRef = 0, jsxxRef = 0, updateGhfRef = 0;

            if (ghxx != null) {
                ghxx.setYljgbm(msg.getYljgbm());
            }
            if (jsjlS != null && jsjlS.size() > 0) {
                for (Mzb_jsjlModel jsjl : jsjlS) {
                    jsjl.setYljgbm(msg.getYljgbm());
                }
            }
            if (mzfyS != null && mzfyS.size() > 0) {
                for (Mzb_brfyModel mzfy : mzfyS) {
                    mzfy.setYljgbm(msg.getYljgbm());
                }
            }
            if (updateGhf != null && updateGhf.size() > 0) {
                for (Mzb_brfyModel mzfy : updateGhf) {
                    mzfy.setYljgbm(msg.getYljgbm());
                }
            }
            //挂号信息提交
            ghxxRef = Ghb_brghModelMapper.update(ghxx);
            //判断更新行数
            if (ghxxRef <= 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                result.setResultMsg("病人挂号保存 【注册信息保存】 失败:    提交行数：" + ghxxRef);
                logger.error("调用病人挂号保存接口【Ghb_brghCspServiceImpl Brgh_Save】失败， 【注册信息保存】 提交行数：" + ghxxRef);
                throw new Exception();  //异常自动返回  并且SQL事务回滚
            }

            //费用和结算记录提交
            if (mzfyS != null && mzfyS.size() > 0 && jsjlS != null && jsjlS.size() > 0) {

                //结算信息提交
                jsxxRef = Mzb_jsjlModelMapper.insert(jsjlS);
                if (jsxxRef != -1) {  //判断更新行数
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg("病人退号 【结算记录信息保存】 失败:    提交行数：" + jsxxRef);
                    logger.error("调用病人退号接口【Ghb_brghCspServiceImpl Brgh_TH】 【结算记录信息保存】 失败， 【挂号信息保存】 提交行数：" + jsxxRef);
                    throw new Exception();  //异常自动返回  并且SQL事务回滚
                }

                //费用信息提交
                fyxxRef = Mzb_brfyModelMapper.insert(mzfyS);
                if (fyxxRef != -1) {  //判断更新行数
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg("病人退号 【费用信息保存】 失败:    提交行数：" + fyxxRef);
                    logger.error("调用病人退号接口【Ghb_brghCspServiceImpl Brgh_TH】 【费用信息保存】 失败， 【挂号信息保存】 提交行数：" + jsxxRef);
                    throw new Exception();  //异常自动返回  并且SQL事务回滚
                }
            }
            //需要修改的挂号费用集合
            if (updateGhf != null && updateGhf.size() > 0) {
                updateGhfRef = Mzb_brfyModelMapper.update(updateGhf);
                if (fyxxRef != -1) {  //判断更新行数
                    result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                    result.setResultMsg("病人退号 【费用信息修改】 失败:    提交行数：" + updateGhfRef);
                    logger.error("调用病人退号接口【Ghb_brghCspServiceImpl Brgh_TH】 【费用信息修改】 失败， 【挂号信息保存】 提交行数：" + updateGhfRef);
                    throw new Exception();  //异常自动返回  并且SQL事务回滚
                }
            }

            //返回成功标志
            result.setResultMsg("病人退号成功！");
            result.getResResult().put("ref", 1);
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病人退号失败:" + e.getCause().getMessage());
            logger.error("调用病人退号接口【Ghb_brghCspServiceImpl Brgh_TH】发生异常", e);
            throw new Exception();
        }

        return result;
    }

    /**
     * 这里主要是针对门诊收费所需要的门诊信息
     */
    @Override
    public UtilResponse queryByMzsf(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Ghb_brghModel> list = (List<Ghb_brghModel>) Ghb_brghModelMapper.queryByMzsf(bean);
            result.getResResult().put("list", list);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("门诊收费所需要的信息查询失败:" + ex.getCause().getMessage());
            logger.error("调用查询门诊收费所需要的信息接口【Ghb_brghCspServiceImpl queryByMzsf】发生异常", ex);
        }
        return result;
    }

    /**
     * 查询门特就诊列表
     */
    @Override
    public UtilResponse selectMtJzjl(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            YHYB_MT_JZJL bean = (YHYB_MT_JZJL) msg.getParam().get("bean");
            YHYB_MT_JZJL yhybBean = Ghb_brghModelMapper.selectMtJzjl(bean);
            result.getResResult().put("bean", yhybBean);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询门特就诊记录信息查询失败:" + ex.getCause().getMessage());
            logger.error("查询门特就诊记录信息查询失败【Ghb_brghCspServiceImpl selectMtJzjl】发生异常", ex);
        }
        return result;
    }

    /**
     * 针对门诊取消门诊接诊
     */
    @Override
    public UtilResponse updateQxjz(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            Integer ref = Ghb_brghModelMapper.updateQxjz(bean);
            result.getResResult().put("ref", ref.toString());

        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病门诊取消门诊接诊失败:" + ex.getCause().getMessage());
            logger.error("调用门诊取消门诊接诊接口【Ghb_brghCspServiceImpl updateQxjz】发生异常", ex);
        }
        return result;
    }

    /**
     * 取消完成接诊
     */
    @Override
    public UtilResponse updateJzwc(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            Integer ref = Ghb_brghModelMapper.updateJzwc(bean);
            result.getResResult().put("ref", ref.toString());

        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("病门诊取消门诊接诊失败:" + ex.getCause().getMessage());
            logger.error("调用门诊取消门诊接诊接口【Ghb_brghCspServiceImpl updateQxjz】发生异常", ex);
        }
        return result;
    }


    /**
     * 根据医生查询今天挂号数
     */
    @Override
    public UtilResponse queryTodayNum(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");

            if (bean.getEndrq() == null && bean.getBeginrq() == null) {
                Calendar beginDate = new GregorianCalendar();
                //获取当天0点时间
                beginDate.set(Calendar.HOUR_OF_DAY, 0);
                beginDate.set(Calendar.MINUTE, 0);
                beginDate.set(Calendar.SECOND, 0);
                Date begin = beginDate.getTime();
                bean.setBeginrq(begin);
                bean.setEndrq(new Date());
            }
            bean.setYljgbm(msg.getYljgbm());

            Ghyw_sy_ghShowModel resultbean = Ghb_brghModelMapper.queryTodayNum(bean);
            result.getResResult().put("bean", resultbean);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("根据医生查询今天挂号数信息失败:" + ex.getCause().getMessage());
            logger.error("调用根据医生查询今天挂号数信息接口【Ghb_brghCspServiceImpl queryTodayNum】发生异常", ex);
        }
        return result;
    }

    /**
     * 根据病人id查询该病人所有挂号信息
     */
    @Override
    public UtilResponse quertBrAllGhxx(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Ghb_brghModel bean = (Ghb_brghModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Ghb_brghModel> list = (List<Ghb_brghModel>) Ghb_brghModelMapper.quertBrAllGhxx(bean, new RowBounds((bean.getPage() - 1) * bean.getRows(), bean.getRows()));
            result.getResResult().put("list", list);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("根据病人id查询该病人所有挂号信息失败:" + ex.getCause().getMessage());
            logger.error("调用根据病人id查询该病人所有挂号信息接口【Ghb_brghCspServiceImpl quertBrAllGhxx】发生异常", ex);
        }
        return result;
    }

    /**
     * 注销院内就诊卡
     */
    @Override
    public UtilResponse resetBrylkxx(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //查询是否存在此病人的医疗卡信息
            Gyb_brylkxxModel bean = (Gyb_brylkxxModel) msg.getParam().get("bean");
            String dzxkh = bean.getYlkh();//待注销医疗卡号
            bean.setYljgbm(msg.getYljgbm());
            bean.setYlkh(bean.getBrid());
            int ref = 0;
            //查询是否已经存在（一个人多个卡的情况）
            Gyb_brylkxxModel gyb_brylkxxModel = Gyb_brylkxxModelMapper.selectKey(bean);
            bean.setYlkh(dzxkh);
            if (gyb_brylkxxModel == null) {//如果不存在医疗卡号为病人id的，作废当前，新增一个以病人ID为卡号的医疗卡信息
                gyb_brylkxxModel = Gyb_brylkxxModelMapper.selectKey(bean);//查询出当前卡号对应的相关信息
                if (gyb_brylkxxModel == null) {
                    result.getResResult().put("ref", 0);
                    result.getResResult().put("resultMsg", "注销院内就诊卡失败，请检查此卡是否已经注销！");
                    return result;
                }
                gyb_brylkxxModel.setYlkh(bean.getBrid());
                gyb_brylkxxModel.setBzms("注销还原卡");
                List<Gyb_brylkxxModel> list = new ArrayList<>();
                list.add(gyb_brylkxxModel);
                Gyb_brylkxxModelMapper.insert(list);
            }
            ref = Ghb_brghModelMapper.resetBrylkxx(bean);//作废以前卡号
            result.getResResult().put("ref", ref);
            if (ref > 0) {
                result.getResResult().put("resultMsg", "注销院内就诊卡成功！");
            } else {
                result.getResResult().put("resultMsg", "注销院内就诊卡失败！");
            }
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("注销院内就诊卡失败:" + ex.getCause().getMessage());
            logger.error("调用注销院内就诊卡接口【Ghb_brghCspServiceImpl resetBrylkxx】发生异常", ex);
        }
        return result;
    }

    /**
     * 更新病人基本信息
     *
     * @param msg
     * @return
     */
    @Override
    public UtilResponse updateBrjbxx(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Gyb_brjbxxModel bean = (Gyb_brjbxxModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            UserInfoModel user = (UserInfoModel) msg.getUserinfo().get("userinfo");
            //写修改记录
            if (bean.getBrid() != null && !bean.getBrid().equals("")) {
                Gyb_brjbxxModel pve = Gyb_brjbxxModelMapper.queryGyb_brjbxxOne(bean);

                if (pve != null) {
                    String logs = getChange(pve, bean);
                    if (logs != null && !logs.equals("")) {
                        msg.getParam().put("xmbm", bean.getBrid());//项目编码
                        msg.getParam().put("logs", logs);//修改日志内容
                        msg.getParam().put("xglb", "04");//修改类别
                        gyb_ylxx_xgjlCspService.insert(user, msg);
                    }
                }
            }
            int ref = Gyb_brjbxxModelMapper.update(bean);
            result.getResResult().put("ref", ref);
            result.getResResult().put("resultMsg", "更新病人基本信息成功！");
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("更新病人基本信息失败:" + ex.getCause().getMessage());
            logger.error("调用更新病人基本信息接口【Ghb_brghCspServiceImpl updateBrjbxx】发生异常", ex);
        }
        return result;
    }

    @Override
    public UtilResponse update(Ghb_brghModel model) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            //写修改记录
            int ref = Ghb_brghModelMapper.update(model);
            result.getResResult().put("ref", ref);
            result.getResResult().put("resultMsg", "更新病人挂号信息成功！");
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("更新病人挂号信息失败:" + ex.getCause().getMessage());
            logger.error("调用更新病人基本信息接口【Ghb_brghCspServiceImpl update】发生异常", ex);
        }
        return result;
    }

    @Override
    public UtilResponse saveMtJzjl(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            YHYB_MT_JZJL bean = (YHYB_MT_JZJL) msg.getParam().get("bean");
            Integer ref = Ghb_brghModelMapper.saveMtJzjl(bean);
            result.getResResult().put("ref", ref);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("保存门特就诊信息失败:" + ex.getCause().getMessage());
            logger.error("保存门特就诊信息失败【Ghb_brghCspServiceImpl update】发生异常", ex);
        }
        return result;
    }

    //注册信息
    private String getChange(Gyb_brjbxxModel pve, Gyb_brjbxxModel cur) {
        try {
            String logs = "";
            if (pve.getBrxm() != null && cur.getBrxm() != null && !pve.getBrxm().equals(cur.getBrxm())) {
                logs += "病人姓名:" + pve.getBrxm() + "->" + cur.getBrxm() + ";  ";
            }
            if (pve.getBrxb() != null && cur.getBrxb() != null && !pve.getBrxb().equals(cur.getBrxb())) {
                logs += "病人性别:" + pve.getBrxb() + "->" + cur.getBrxb() + ";  ";
            }
            if (pve.getCsrq() != null && cur.getCsrq() != null && !pve.getCsrq().equals(cur.getCsrq())) {
                logs += "出生日期:" + DateTimeUtil.getChar8En(pve.getCsrq()) + "->" + DateTimeUtil.getChar8En(cur.getCsrq()) + ";  ";
            }
            if (pve.getBrgj() != null && cur.getBrgj() != null && !pve.getBrgj().equals(cur.getBrgj())) {
                logs += "病人国籍:" + pve.getBrgj() + "->" + cur.getBrgj() + ";  ";
            }
            if (pve.getBrmz() != null && cur.getBrmz() != null && !pve.getBrmz().equals(cur.getBrmz())) {
                logs += "病人民族:" + pve.getBrmz() + "->" + cur.getBrmz() + ";  ";
            }
            if (pve.getSfzjlx() != null && cur.getSfzjlx() != null && !pve.getSfzjlx().equals(cur.getSfzjlx())) {
                logs += "身份证件类型:" + pve.getSfzjlx() + "->" + cur.getSfzjlx() + ";  ";
            }
            if (pve.getSfzjhm() != null && cur.getSfzjhm() != null && !pve.getSfzjhm().equals(cur.getSfzjhm())) {
                logs += "身份证件号码:" + pve.getSfzjhm() + "->" + cur.getSfzjhm() + ";  ";
            }
            if (pve.getHyzk() != null && cur.getHyzk() != null && !pve.getHyzk().equals(cur.getHyzk())) {
                logs += "婚姻状况:" + pve.getHyzk() + "->" + cur.getHyzk() + ";  ";
            }
            if (pve.getZybm() != null && cur.getZybm() != null && !pve.getZybm().equals(cur.getZybm())) {
                logs += "职业编码:" + pve.getZybm() + "->" + cur.getZybm() + ";  ";
            }
            if (pve.getHzlx() != null && cur.getHzlx() != null && !pve.getHzlx().equals(cur.getHzlx())) {
                logs += "患者类型hzlx:" + pve.getHzlx() + "->" + cur.getHzlx() + ";  ";
            }
            if (pve.getCsd() != null && cur.getCsd() != null && !pve.getCsd().equals(cur.getCsd())) {
                logs += "出生地:" + pve.getCsd() + "->" + cur.getCsd() + ";  ";
            }
            if (pve.getSjhm() != null && cur.getSjhm() != null && !pve.getSjhm().equals(cur.getSjhm())) {
                logs += "手机号码:" + pve.getSjhm() + "->" + cur.getSjhm() + ";  ";
            }
            if (pve.getGzdw() != null && cur.getGzdw() != null && !pve.getGzdw().equals(cur.getGzdw())) {
                logs += "工作单位:" + pve.getGzdw() + "->" + cur.getGzdw() + ";  ";
            }
            if (pve.getDwdz() != null && cur.getDwdz() != null && !pve.getDwdz().equals(cur.getDwdz())) {
                logs += "单位地址:" + pve.getDwdz() + "->" + cur.getDwdz() + ";  ";
            }
            if (pve.getDwyb() != null && cur.getDwyb() != null && !pve.getDwyb().equals(cur.getDwyb())) {
                logs += "单位邮编:" + pve.getDwyb() + "->" + cur.getDwyb() + ";  ";
            }
            if (pve.getJzdxzqh() != null && cur.getJzdxzqh() != null && !pve.getJzdxzqh().equals(cur.getJzdxzqh())) {
                logs += "居住地行政区划:" + pve.getJzdxzqh() + "->" + cur.getJzdxzqh() + ";  ";
            }
            if (pve.getJzdsheng() != null && cur.getJzdsheng() != null && !pve.getJzdsheng().equals(cur.getJzdsheng())) {
                logs += "居住地省份:" + pve.getJzdsheng() + "->" + cur.getJzdsheng() + ";  ";
            }
            if (pve.getJzdshi() != null && cur.getJzdshi() != null && !pve.getJzdshi().equals(cur.getJzdshi())) {
                logs += "居住地市:" + pve.getJzdshi() + "->" + cur.getJzdshi() + ";  ";
            }
            if (pve.getCsdXian() != null && cur.getCsdXian() != null && !pve.getCsdXian().equals(cur.getCsdXian())) {
                logs += "居住地县:" + pve.getCsdXian() + "->" + cur.getCsdXian() + ";  ";
            }
            if (pve.getJzdxiang() != null && cur.getJzdxiang() != null && !pve.getJzdxiang().equals(cur.getJzdxiang())) {
                logs += "居住地乡:" + pve.getJzdxiang() + "->" + cur.getJzdxiang() + ";  ";
            }
            if (pve.getJzdjwh() != null && cur.getJzdjwh() != null && !pve.getJzdjwh().equals(cur.getJzdjwh())) {
                logs += "居住地居委会:" + pve.getJzdjwh() + "->" + cur.getJzdjwh() + ";  ";
            }
            if (pve.getJzdmc() != null && cur.getJzdmc() != null && !pve.getJzdmc().equals(cur.getJzdmc())) {
                logs += "居住地名称:" + pve.getJzdmc() + "->" + cur.getJzdmc() + ";  ";
            }
            if (pve.getHkdz() != null && cur.getHkdz() != null && !pve.getHkdz().equals(cur.getHkdz())) {
                logs += "户口地址:" + pve.getHkdz() + "->" + cur.getHkdz() + ";  ";
            }
            if (pve.getLxrxm() != null && cur.getLxrxm() != null && !pve.getLxrxm().equals(cur.getLxrxm())) {
                logs += "联系人姓名:" + pve.getLxrxm() + "->" + cur.getLxrxm() + ";  ";
            }
            if (pve.getLxrgx() != null && cur.getLxrgx() != null && !pve.getLxrgx().equals(cur.getLxrgx())) {
                logs += "联系人关系:" + pve.getLxrgx() + "->" + cur.getLxrgx() + ";  ";
            }
            if (pve.getLxrdz() != null && cur.getLxrdz() != null && !pve.getLxrdz().equals(cur.getLxrdz())) {
                logs += "联系人地址:" + pve.getLxrdz() + "->" + cur.getLxrdz() + ";  ";
            }
            if (pve.getLxrdw() != null && cur.getLxrdw() != null && !pve.getLxrdw().equals(cur.getLxrdw())) {
                logs += "联系人单位:" + pve.getLxrdw() + "->" + cur.getLxrdw() + ";  ";
            }
            if (pve.getLxryb() != null && cur.getLxryb() != null && !pve.getLxryb().equals(cur.getLxryb())) {
                logs += "联系人邮编:" + pve.getLxryb() + "->" + cur.getLxryb() + ";  ";
            }
            if (pve.getLxrdh() != null && cur.getLxrdh() != null && !pve.getLxrdh().equals(cur.getLxrdh())) {
                logs += "联系人电话:" + pve.getLxrdh() + "->" + cur.getLxrdh() + ";  ";
            }
            if (pve.getSg() != null && cur.getSg() != null && !pve.getSg().equals(cur.getSg())) {
                logs += "身高:" + pve.getSg() + "->" + cur.getSg() + ";  ";
            }
            if (pve.getTz() != null && cur.getTz() != null && !pve.getTz().equals(cur.getTz())) {
                logs += "体重:" + pve.getTz() + "->" + cur.getTz() + ";  ";
            }

            return logs;
        } catch (Exception ex) {
            System.out.println("发药异常：" + ex.getCause().getMessage());
            logger.error("调用修改记录转换接口【Gyb_mxfyxmCspServiceImpl getChange】发生异常", ex.getCause().getMessage());
            return null;
        }
    }

    //挂号信息
    private String getChangegh(Ghb_brghModel pve, Ghb_brghModel cur) {
        try {
            String logs = "";
            if (pve.getFbbm() != null && cur.getFbbm() != null && !pve.getFbbm().equals(cur.getFbbm())) {
                logs += "费别编码:" + pve.getFbbm() + "->" + cur.getFbbm() + ";  ";
            }
            if (pve.getBxlbbm() != null && cur.getBxlbbm() != null && !pve.getBxlbbm().equals(cur.getBxlbbm())) {
                logs += "保险类别编码:" + pve.getBxlbbm() + "->" + cur.getBxlbbm() + ";  ";
            }
            if (pve.getGhrq() != null && cur.getGhrq() != null && !pve.getGhrq().equals(cur.getGhrq())) {
                logs += "挂号日期:" + DateTimeUtil.getChar8En(pve.getGhrq()) + "->" + DateTimeUtil.getChar8En(cur.getGhrq()) + ";  ";
            }
            if (pve.getGhzl() != null && cur.getGhzl() != null && !pve.getGhzl().equals(cur.getGhzl())) {
                logs += "挂号种类:" + pve.getGhzl() + "->" + cur.getGhzl() + ";  ";
            }
            if (pve.getGhks() != null && cur.getGhks() != null && !pve.getGhks().equals(cur.getGhks())) {
                logs += "挂号科室:" + pve.getGhks() + "->" + cur.getGhks() + ";  ";
            }
            if (pve.getJzys() != null && cur.getJzys() != null && !pve.getJzys().equals(cur.getJzys())) {
                logs += "接诊医生:" + pve.getJzys() + "->" + cur.getJzys() + ";  ";
            }
            if (pve.getBrnl() != null && cur.getBrnl() != null && !pve.getBrnl().equals(cur.getBrnl())) {
                logs += "病人年龄:" + pve.getBrnl() + "->" + cur.getBrnl() + ";  ";
            }
            if (pve.getNldw() != null && cur.getNldw() != null && !pve.getNldw().equals(cur.getNldw())) {
                logs += "年龄单位:" + pve.getNldw() + "->" + cur.getNldw() + ";  ";
            }
            if (pve.getSffz() != null && cur.getSffz() != null && !pve.getSffz().equals(cur.getSffz())) {
                logs += "是否复诊:" + pve.getSffz() + "->" + cur.getSffz() + ";  ";
            }
            if (pve.getXt() != null && cur.getXt() != null && !pve.getXt().equals(cur.getXt())) {
                logs += "血糖:" + pve.getXt() + "->" + cur.getXt() + ";  ";
            }
            if (pve.getXySzy() != null && cur.getXySzy() != null && !pve.getXySzy().equals(cur.getXySzy())) {
                logs += "舒张血压:" + pve.getXySzy() + "->" + cur.getXySzy() + ";  ";
            }
            if (pve.getXySsy() != null && cur.getXySsy() != null && !pve.getXySsy().equals(cur.getXySsy())) {
                logs += "收缩压:" + pve.getXySsy() + "->" + cur.getXySsy() + ";  ";
            }
            if (pve.getTw() != null && cur.getTw() != null && !pve.getTw().equals(cur.getTw())) {
                logs += "体温:" + pve.getTw() + "->" + cur.getTw() + ";  ";
            }
            if (pve.getFbbm() != null && cur.getFbbm() != null && !pve.getFbbm().equals(cur.getFbbm())) {
                logs += ":" + pve.getFbbm() + "->" + cur.getFbbm() + ";  ";
            }
            if (pve.getDycl() != null && cur.getDycl() != null && !pve.getDycl().equals(cur.getDycl())) {
                logs += "对应处理:" + pve.getDycl() + "->" + cur.getDycl() + ";  ";
            }
            if (pve.getFbrq() != null && cur.getFbrq() != null && !pve.getFbrq().equals(cur.getFbrq())) {
                logs += "发病日期:" + DateTimeUtil.getChar8En(pve.getFbrq()) + "->" + DateTimeUtil.getChar8En(cur.getFbrq()) + ";  ";
            }
            if (pve.getZyzztz() != null && cur.getZyzztz() != null && !pve.getZyzztz().equals(cur.getZyzztz())) {
                logs += "主要症状、体征:" + pve.getZyzztz() + "->" + cur.getZyzztz() + ";  ";
            }
            if (pve.getJbbm() != null && cur.getJbbm() != null && !pve.getJbbm().equals(cur.getJbbm())) {
                logs += "疾病编码:" + pve.getJbbm() + "->" + cur.getJbbm() + ";  ";
            }
            if (pve.getJbmc() != null && cur.getJbmc() != null && !pve.getJbmc().equals(cur.getJbmc())) {
                logs += "疾病名称:" + pve.getJbmc() + "->" + cur.getJbmc() + ";  ";
            }
            if (pve.getSfcrb() != null && cur.getSfcrb() != null && !pve.getSfcrb().equals(cur.getSfcrb())) {
                logs += "是否传染病:" + pve.getSfcrb() + "->" + cur.getSfcrb() + ";  ";
            }
            if (pve.getSfgm() != null && cur.getSfgm() != null && !pve.getSfgm().equals(cur.getSfgm())) {
                logs += "是否过敏:" + pve.getSfgm() + "->" + cur.getSfgm() + ";  ";
            }
            if (pve.getGms() != null && cur.getGms() != null && !pve.getGms().equals(cur.getGms())) {
                logs += "过敏史:" + pve.getGms() + "->" + cur.getGms() + ";  ";
            }
            if (pve.getMb() != null && cur.getMb() != null && !pve.getMb().equals(cur.getMb())) {
                logs += "脉埔:" + pve.getMb() + "->" + cur.getMb() + ";  ";
            }
            if (pve.getHx() != null && cur.getHx() != null && !pve.getHx().equals(cur.getHx())) {
                logs += "呼吸:" + pve.getHx() + "->" + cur.getHx() + ";  ";
            }
            if (pve.getXtms() != null && cur.getXtms() != null && !pve.getXtms().equals(cur.getXtms())) {
                logs += "血糖描述:" + pve.getXtms() + "->" + cur.getXtms() + ";  ";
            }
            if (pve.getXl() != null && cur.getXl() != null && !pve.getXl().equals(cur.getXl())) {
                logs += "心率:" + pve.getXl() + "->" + cur.getXl() + ";  ";
            }
            if (pve.getQtzdbm() != null && cur.getQtzdbm() != null && !pve.getQtzdbm().equals(cur.getQtzdbm())) {
                logs += "其他诊断编码:" + pve.getQtzdbm() + "->" + cur.getQtzdbm() + ";  ";
            }
            if (pve.getQtzdmc() != null && cur.getQtzdmc() != null && !pve.getQtzdmc().equals(cur.getQtzdmc())) {
                logs += "其他诊断名称:" + pve.getQtzdmc() + "->" + cur.getQtzdmc() + ";  ";
            }
            if (pve.getJws() != null && cur.getJws() != null && !pve.getJws().equals(cur.getJws())) {
                logs += "既往史:" + pve.getJws() + "->" + cur.getJws() + ";  ";
            }
            if (pve.getJzs() != null && cur.getJzs() != null && !pve.getJzs().equals(cur.getJzs())) {
                logs += "家族史:" + pve.getJzs() + "->" + cur.getJzs() + ";  ";
            }
            if (pve.getSfqz() != null && cur.getSfqz() != null && !pve.getSfqz().equals(cur.getSfqz())) {
                logs += "是否确诊:" + pve.getSfqz() + "->" + cur.getSfqz() + ";  ";
            }
            if (pve.getZyzh() != null && cur.getZyzh() != null && !pve.getZyzh().equals(cur.getZyzh())) {
                logs += "主病:" + pve.getZyzh() + "->" + cur.getZyzh() + ";  ";
            }
            if (pve.getZyzf() != null && cur.getZyzf() != null && !pve.getZyzf().equals(cur.getZyzf())) {
                logs += "症型:" + pve.getZyzf() + "->" + cur.getZyzf() + ";  ";
            }
            if (pve.getZs() != null && cur.getZs() != null && !pve.getZs().equals(cur.getZs())) {
                logs += "主诉:" + pve.getZs() + "->" + cur.getZs() + ";  ";
            }
            if (pve.getXbs() != null && cur.getXbs() != null && !pve.getXbs().equals(cur.getXbs())) {
                logs += "现病史:" + pve.getXbs() + "->" + cur.getXbs() + ";  ";
            }
            if (pve.getQtzdbm1() != null && cur.getQtzdbm1() != null && !pve.getQtzdbm1().equals(cur.getQtzdbm1())) {
                logs += "其他诊断1编码:" + pve.getQtzdbm1() + "->" + cur.getQtzdbm1() + ";  ";
            }
            if (pve.getQtzdmc1() != null && cur.getQtzdmc1() != null && !pve.getQtzdmc1().equals(cur.getQtzdmc1())) {
                logs += "其他诊断1名称:" + pve.getQtzdmc1() + "->" + cur.getQtzdmc1() + ";  ";
            }
            if (pve.getJbmc() != null && cur.getJbmc() != null && !pve.getJbmc().equals(cur.getJbmc())) {
                logs += "诊断名称:" + pve.getJbmc() + "->" + cur.getJbmc() + ";  ";
            }
            if (pve.getQtzdbm2() != null && cur.getQtzdbm2() != null && !pve.getQtzdbm2().equals(cur.getQtzdbm2())) {
                logs += "其他诊断2编码:" + pve.getQtzdbm2() + "->" + cur.getQtzdbm2() + ";  ";
            }
            if (pve.getQtzdmc2() != null && cur.getQtzdmc2() != null && !pve.getQtzdmc2().equals(cur.getQtzdmc2())) {
                logs += "其他诊断2名称:" + pve.getQtzdmc2() + "->" + cur.getQtzdmc2() + ";  ";
            }
            if (pve.getQtzdbm3() != null && cur.getQtzdbm3() != null && !pve.getQtzdbm3().equals(cur.getQtzdbm3())) {
                logs += "其他诊断3编码:" + pve.getQtzdbm3() + "->" + cur.getQtzdbm3() + ";  ";
            }
            if (pve.getQtzdmc3() != null && cur.getQtzdmc3() != null && !pve.getQtzdmc3().equals(cur.getQtzdmc3())) {
                logs += "其他诊断3名称:" + pve.getQtzdmc3() + "->" + cur.getQtzdmc3() + ";  ";
            }
            if (pve.getQtzdbm4() != null && cur.getQtzdbm4() != null && !pve.getQtzdbm4().equals(cur.getQtzdbm4())) {
                logs += "其他诊断4编码:" + pve.getQtzdbm4() + "->" + cur.getQtzdbm4() + ";  ";
            }
            if (pve.getQtzdmc4() != null && cur.getQtzdmc4() != null && !pve.getQtzdmc4().equals(cur.getQtzdmc4())) {
                logs += "其他诊断4名称:" + pve.getQtzdmc4() + "->" + cur.getQtzdmc4() + ";  ";
            }
            if (pve.getZdsm1() != null && cur.getZdsm1() != null && !pve.getZdsm1().equals(cur.getZdsm1())) {
                logs += "左诊断:" + pve.getZdsm1() + "->" + cur.getZdsm1() + ";  ";
            }
            if (pve.getZdsm2() != null && cur.getZdsm2() != null && !pve.getZdsm2().equals(cur.getZdsm2())) {
                logs += "右诊断:" + pve.getZdsm2() + "->" + cur.getZdsm2() + ";  ";
            }
            return logs;
        } catch (Exception ex) {
            logger.error("调用修改记录转换接口【Ghb_brghCspServiceImpl getChangegh】发生异常", ex);
            return null;
        }
    }


    @Override
    public UtilResponse saveMzGhxhZd(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            List<Mzb_Hzzd> bean = (List<Mzb_Hzzd>) msg.getParam().get("bean");

            for (int i = 0; i < bean.size(); i++) {
                bean.get(i).setYljgbm(msg.getYljgbm());
            }
            if (bean.size() > 0) {
                Ghb_brghModelMapper.deleteMzGhxhZd(bean.get(0));
            }
            result.getResResult().put("bean", Ghb_brghModelMapper.saveMzGhxhZd(bean));
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("保存诊断记录信息查询失败:" + ex.getCause().getMessage());
            logger.error("保存诊断记录信息查询失败【Ghb_brghCspServiceImpl selectMtJzjl】发生异常", ex);
        }
        return result;
    }

    @Override
    public UtilResponse getMzGhxhZd(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Mzb_Hzzd bean = (Mzb_Hzzd) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            List<Mzb_Hzzd> yhybBean = Ghb_brghModelMapper.getMzGhxhZd(bean);
            //这是已经填写过的


            Ghb_brghModel obj = new Ghb_brghModel();

            obj.setYljgbm(msg.getYljgbm());
            obj.setGhxh(bean.getGhxh());
            Ghb_brghModel ghxx = Ghb_brghModelMapper.queryGhb_brghOne(obj);


            List<Mzb_Hzzd> fhsj = new ArrayList<>();
            if (ghxx.getJbbm() != null && !"".equals(ghxx.getJbbm())) {
                Mzb_Hzzd hzzd = new Mzb_Hzzd();
                hzzd.setGhxh(bean.getGhxh());
                hzzd.setJbmb(ghxx.getJbbm());
                hzzd.setJbmc(ghxx.getJbmc());
                fhsj.add(hzzd);
            }
            if (ghxx.getQtzdbm() != null && !"".equals(ghxx.getQtzdbm())) {
                Mzb_Hzzd hzzd = new Mzb_Hzzd();
                hzzd.setGhxh(bean.getGhxh());
                hzzd.setJbmb(ghxx.getQtzdbm());
                hzzd.setJbmc(ghxx.getQtzdmc());
                fhsj.add(hzzd);
            }
            if (ghxx.getQtzdbm1() != null && !"".equals(ghxx.getQtzdbm1())) {
                Mzb_Hzzd hzzd = new Mzb_Hzzd();
                hzzd.setGhxh(bean.getGhxh());
                hzzd.setJbmb(ghxx.getQtzdbm1());
                hzzd.setJbmc(ghxx.getQtzdmc1());
                fhsj.add(hzzd);
            }
            if (ghxx.getQtzdbm2() != null && !"".equals(ghxx.getQtzdbm2())) {
                Mzb_Hzzd hzzd = new Mzb_Hzzd();
                hzzd.setGhxh(bean.getGhxh());
                hzzd.setJbmb(ghxx.getQtzdbm2());
                hzzd.setJbmc(ghxx.getQtzdmc2());
                fhsj.add(hzzd);
            }
            if (ghxx.getQtzdbm3() != null && !"".equals(ghxx.getQtzdbm3())) {
                Mzb_Hzzd hzzd = new Mzb_Hzzd();
                hzzd.setGhxh(bean.getGhxh());
                hzzd.setJbmb(ghxx.getQtzdbm3());
                hzzd.setJbmc(ghxx.getQtzdmc3());
                fhsj.add(hzzd);
            }
            if (ghxx.getQtzdbm4() != null && !"".equals(ghxx.getQtzdbm4())) {
                Mzb_Hzzd hzzd = new Mzb_Hzzd();
                hzzd.setGhxh(bean.getGhxh());
                hzzd.setJbmb(ghxx.getQtzdbm4());
                hzzd.setJbmc(ghxx.getQtzdmc4());
                fhsj.add(hzzd);
            }


            if (ghxx.getFjzd() != null && !"".equals(ghxx.getFjzd())) {
                try {
                    List<Mzb_Hzzd> fhzd = JSON.parseArray(ghxx.getFjzd(), Mzb_Hzzd.class);
                    fhsj.addAll(fhzd);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            for (int i = 0; i < fhsj.size(); i++) {


                for (int j = 0; j < yhybBean.size(); j++) {
                    if (fhsj.get(i).getJbmb().equals(yhybBean.get(j).getJbmb())) {
                        fhsj.get(i).setCyqk(yhybBean.get(j).getCyqk());
                        fhsj.get(i).setRybq(yhybBean.get(j).getRybq());
                        break;
                    }
                }
            }
            result.getResResult().put("bean", fhsj);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("查询诊断记录信息查询失败:" + ex.getCause().getMessage());
            logger.error("查询诊断记录信息查询失败【Ghb_brghCspServiceImpl selectMtJzjl】发生异常", ex);
        }
        return result;
    }

    private MedicalJchrModel sendRequest(MedicalRecordModel record) {
        String JCJY_URL = read.getValue("jcjy_url");
        String APP_CODE = read.getValue("app_code");
        String HRBS_METHOD = read.getValue("hrbs_method");
        String HRBS_SIGN = read.getValue("hrbs_sign");
        if(JCJY_URL.isEmpty() && APP_CODE.isEmpty() && HRBS_SIGN.isEmpty()){
            logger.error("获取服务器地址错误!") ;
            return null;
        }
        // 1️⃣ 判断 IP 是否可访问
        if (!isServerReachable(JCJY_URL)) {
            logger.error("无法访问服务器: {}", JCJY_URL);
            return null;
        }
        try (CloseableHttpClient httpClient = createHttpClient()) {
            HttpPost request = new HttpPost(JCJY_URL+HRBS_METHOD);
            request.addHeader("Content-Type", "application/json; charset=utf-8");
            request.addHeader("appCode", APP_CODE);
            request.addHeader("sign", HRBS_SIGN);

            // **1️⃣  请求数据 -> 转 JSON -> SM4 加密**
            String jsonBody = JSONObject.toJSONString(record);  // 转换为 JSON
            String encryptedRequest = Sm4HexEn.encode(jsonBody); // **SM4 加密**
            request.setEntity(new StringEntity(encryptedRequest));

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject responseJson = JSONObject.parseObject(responseString);
                String code = responseJson.getString("code");
                String encryptedData = responseJson.getString("data"); // 获取密文数据
                // **2️⃣ 解析 API 响应**
                if ("0".equals(code) || "2".equals(code) || "5".equals(code)) {
                    // **解密 `data` 字段**
                    String decryptedData = Sm4HexDe.decode(encryptedData);
                    JSONObject dataNode = JSONObject.parseObject(decryptedData);
                    // **封装到对象**
                    MedicalJchrModel medicalJchr = new MedicalJchrModel();
                    medicalJchr.setCode(code);
                    medicalJchr.setMsg(responseJson.getString("msg"));
                    medicalJchr.setKh(dataNode.getString("kh"));
                    medicalJchr.setKlx(dataNode.getString("klx"));
                    medicalJchr.setZjhm(dataNode.getString("zjhm"));
                    medicalJchr.setZjlx(dataNode.getString("zjlx"));
                    medicalJchr.setBrxm(dataNode.getString("brxm"));
                    medicalJchr.setUrl(dataNode.getString("url"));
                    return medicalJchr;
                }
            }
        } catch (Exception e) {
            logger.error("调用 API 失败: {}", e.getMessage(), e);
            return null;
        }
        return null;
    }

    private boolean isServerReachable(String serverUrl) {
        try {
            // 提取 IP 地址部分（假设 JCJY_URL 是一个完整的 URL）
            String host = new URL(serverUrl).getHost();
            InetAddress inetAddress = InetAddress.getByName(host);
            return inetAddress.isReachable(3000);  // 3000ms 超时
        } catch (UnknownHostException | MalformedURLException e) {
            logger.error("无法解析主机: {}", e.getMessage(), e);
            return false;
        } catch (IOException e) {
            logger.error("无法访问主机: {}", e.getMessage(), e);
            return false;
        }
    }

    private CloseableHttpClient createHttpClient() throws Exception {
        // 创建一个信任所有证书的 TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {}
                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {}
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                }
        };
        // 初始化 SSL 上下文，忽略证书验证
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, (chain, authType) -> true)
                .build();
        SSLConnectionSocketFactory sslFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE // 忽略 Hostname 校验
        );
        return HttpClients.custom()
                .setSSLSocketFactory(sslFactory)
                .build();
    }
}
