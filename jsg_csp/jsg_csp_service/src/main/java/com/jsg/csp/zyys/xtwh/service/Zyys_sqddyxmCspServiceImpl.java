package com.jsg.csp.zyys.xtwh.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsg.csp.api.zyys.xtwh.pojo.Zyys_sqddyxmModel;
import com.jsg.csp.api.zyys.xtwh.service.IZyyssqddyxmCspService;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.csp.zyys.xtwh.dao.New1Zyys_sqddyxmModelMapper;
/**
 *
* @ClassName: Zyys_sqddyxmCspServiceImpl
* @Description: TODO(申请单对应诊疗项目)
* <AUTHOR>
* @date 2020年6月11日 下午10:23:22
*
 */
public class Zyys_sqddyxmCspServiceImpl extends ServiceInvocationHelper implements IZyyssqddyxmCspService{

	private final Logger logger = LoggerFactory.getLogger(Zyys_sqddyxmCspServiceImpl.class);
	@Autowired
	private New1Zyys_sqddyxmModelMapper zyys_sqddyxmModelMapper;

	@Override
	public UtilResponse queryList(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Zyys_sqddyxmModel bean = (Zyys_sqddyxmModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Zyys_sqddyxmModel> list = (List<Zyys_sqddyxmModel>)zyys_sqddyxmModelMapper.query4Mem(bean);
			PageInfo<Zyys_sqddyxmModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("申请单对应诊疗项目查询失败:"+ex.getCause().getMessage());
            logger.error("调用查询申请单对应诊疗项目信息接口【Zyys_sqddyxmCspServiceImpl queryList】发生异常", ex);
		}
		return result;
	}

	@Override
	public UtilResponse queryOne(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Zyys_sqddyxmModel bean = (Zyys_sqddyxmModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			bean = zyys_sqddyxmModelMapper.queryOne4Mem(bean);
			result.getResResult().put("bean", bean);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("申请单对应诊疗项目查询失败:"+ex.getCause().getMessage());
			logger.error("调用查询申请单对应诊疗项目信息接口【Zyys_sqddyxmCspServiceImpl queryOne】发生异常", ex);
		}
		return result;
	}

	@Override
	public UtilResponse deletebatch(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			List<Zyys_sqddyxmModel> bean = (List<Zyys_sqddyxmModel>)msg.getParam().get("bean");
			for (Zyys_sqddyxmModel sqd : bean) {
				sqd.setYljgbm(msg.getYljgbm());
			}
			Integer ref = zyys_sqddyxmModelMapper.delete3Mem(bean);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除申请单对应诊疗项目失败:"+e.getCause().getMessage());
			logger.error("调用删除申请单对应诊疗项目接口【Zyys_sqddyxmCspServiceImpl deletebatch】发生异常", e);
		}
		return result;
	}

	@Override
	public UtilResponse savebatch(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Zyys_sqddyxmModel bean = (Zyys_sqddyxmModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = zyys_sqddyxmModelMapper.save3Mem(bean);
			result.getResResult().put("ref", ref.toString());

		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("申请单对应诊疗项目保存失败:"+ex.getCause().getMessage());
			logger.error("调用申请单对应诊疗项目保存接口【Zyys_sqddyxmCspServiceImpl savebatch】发生异常",ex);
		}
		return result;
	}

}
