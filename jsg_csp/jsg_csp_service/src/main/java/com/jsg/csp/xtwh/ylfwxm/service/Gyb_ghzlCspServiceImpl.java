package com.jsg.csp.xtwh.ylfwxm.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsg.csp.api.xtwh.ylfwxm.pojo.Gyb_ghzlModel;
import com.jsg.csp.api.xtwh.ylfwxm.service.IGyb_ghzlCspService;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.csp.xtwh.ylfwxm.dao.New1Gyb_ghzlModelMapper;

/**
 *
* @ClassName: Gyb_ghzlCspServiceImpl
* @Description: TODO(挂号种类)
* <AUTHOR>
* @date 2020年5月19日 上午12:08:08
*
 */
public class Gyb_ghzlCspServiceImpl  extends ServiceInvocationHelper implements IGyb_ghzlCspService{

	private final Logger logger = LoggerFactory.getLogger(Gyb_ghzlCspServiceImpl.class);
	@Autowired
	private New1Gyb_ghzlModelMapper gyb_ghzlModelMapper;

	/**
	 * 查询所有挂号种类
	 */
	@Override
	public UtilResponse queryGyb_ghzlList(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Gyb_ghzlModel bean = (Gyb_ghzlModel)msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			PageHelper.startPage(bean.getPage(), bean.getRows());
			List<Gyb_ghzlModel> list = (List<Gyb_ghzlModel>)gyb_ghzlModelMapper.querygyb_ghzl(bean);
			PageInfo<Gyb_ghzlModel> pageInfo = new PageInfo<>(list);
			result.getResResult().put("pageInfo", pageInfo);
		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("挂号种类查询失败:"+ex.getCause().getMessage());
            logger.error("调用查询挂号种类列表信息接口【Gyb_ghzlCspServiceImpl queryGyb_ghzlList】发生异常", ex);
		}
		return result;
	}

	/**
	 * 查询单个挂号种类
	 */
	@Override
	public UtilResponse queryGyb_ghzlOne(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			Gyb_ghzlModel record=(Gyb_ghzlModel) msg.getParam().get("bean");
			record.setYljgbm(msg.getYljgbm());
			Gyb_ghzlModel bean = gyb_ghzlModelMapper.queryGyb_ghzlOne(record);
			result.getResResult().put("bean", bean);
		} catch (Exception ex) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("挂号种类查询失败:"+ex.getCause().getMessage());
			logger.error("调用查询单个挂号种类信息接口【Gyb_ghzlCspServiceImpl queryGyb_ghzlOne】发生异常", ex);
		}
		return result;
	}

	/**
	 * 删除挂号种类
	 */
	@Override
	public UtilResponse deletebatch(UtilRequest msg) {
		UtilResponse result = UtilResponse.newInstance();
		try {
			List<Gyb_ghzlModel> beans = (List<Gyb_ghzlModel>)msg.getParam().get("bean");
			for(Gyb_ghzlModel ghzl: beans){
				ghzl.setYljgbm(msg.getYljgbm());
			}
			Integer ref = gyb_ghzlModelMapper.deletebatch(beans);
			result.getResResult().put("ref", ref.toString());
		} catch (Exception e) {
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("删除挂号种类失败:"+e.getCause().getMessage());
			logger.error("调用删除挂号种类接口【Gyb_ghzlCspServiceImpl deletebatch】发生异常", e);
		}
		return result;
	}

	/**
	 * 挂号种类保存修改
	 */
	@Override
	public UtilResponse savebatch(UtilRequest msg) throws Exception {
		UtilResponse result = UtilResponse.newInstance();
		try{
			Gyb_ghzlModel bean =(Gyb_ghzlModel) msg.getParam().get("bean");
			bean.setYljgbm(msg.getYljgbm());
			Integer ref = gyb_ghzlModelMapper.save3Mem(bean);
			result.getResResult().put("ref", ref.toString());

		}catch (Exception ex){
			result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
			result.setResultMsg("挂号种类保存失败:"+ex.getCause().getMessage());
			logger.error("调用挂号种类保存接口【Gyb_ghzlCspServiceImpl savebatch】发生异常",ex);
		}
		return result;
	}

}


















