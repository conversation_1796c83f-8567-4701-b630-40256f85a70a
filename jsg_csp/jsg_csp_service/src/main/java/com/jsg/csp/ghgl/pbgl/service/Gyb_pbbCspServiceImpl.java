package com.jsg.csp.ghgl.pbgl.service;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsg.common.pojo.CsqxInfoModel;
import com.jsg.common.util.Utilpubfun;
import com.jsg.csp.api.ghgl.pbgl.pojo.Gyb_pbbModel;
import com.jsg.csp.api.ghgl.pbgl.pojo.Gyb_rybmModel;
import com.jsg.csp.api.ghgl.pbgl.service.IGyb_pbbCspService;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.constants.ISystemConstants;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.frame.csp.pojo.UserInfoModel;
import com.jsg.csp.ghgl.pbgl.dao.New1Gyb_pbbModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> HY
 * @ClassName: Gyb_pbbCspServiceImpl
 * @Description: TODO(排班表)
 * @date 2020年6月8日 下午9:28:32
 */
public class Gyb_pbbCspServiceImpl extends ServiceInvocationHelper implements IGyb_pbbCspService {

    private final Logger logger = LoggerFactory.getLogger(Gyb_pbbCspServiceImpl.class);
    @Autowired
    private New1Gyb_pbbModelMapper Gyb_pbbModelMapper;

    /**
     * 查询所有排班表信息
     */
    @Override
    public UtilResponse queryGyb_pbbList(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Gyb_pbbModel bean = (Gyb_pbbModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            PageHelper.startPage(bean.getPage(), bean.getRows());
            List<Gyb_pbbModel> list = (List<Gyb_pbbModel>) Gyb_pbbModelMapper.queryGyb_pbbList(bean);
            PageInfo<Gyb_pbbModel> pageInfo = new PageInfo<>(list);
            result.getResResult().put("pageInfo", pageInfo);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("Gyb_pbbModel排班表信息查询失败:" + ex.getCause().getMessage());
            logger.error("调用Gyb_pbbModel排班表信息信息接口【Gyb_brjbxxCspServiceImpl queryGyb_brjbxxList】发生异常", ex);
        }
        return result;
    }

    /**
     * 查询排班表
     */
    @Override
    public UtilResponse queryGyb_pbbOne(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Gyb_pbbModel cxbean = (Gyb_pbbModel) msg.getParam().get("bean");
            cxbean.setYljgbm(msg.getYljgbm());
            Gyb_pbbModel bean = Gyb_pbbModelMapper.queryGyb_pbbOne(cxbean);
            result.getResResult().put("bean", bean);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("排班表信息查询失败:" + ex.getCause().getMessage());
            logger.error("调用查询单个排班表信息信息接口【Gyb_pbbCspServiceImpl queryGyb_pbbOne】发生异常", ex);
        }
        return result;
    }

    /**
     * 批量删除排班表
     */
    @Override
    public UtilResponse deletebatch(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            List<Gyb_pbbModel> beans = (List<Gyb_pbbModel>) msg.getParam().get("bean");
            if (beans != null && beans.size() > 0) {
                for (Gyb_pbbModel bean : beans) {
                    bean.setYljgbm(msg.getYljgbm());
                }
            }
            Integer ref = Gyb_pbbModelMapper.deletebatch(beans);
            result.getResResult().put("ref", ref.toString());
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("删除排班表信息失败:" + e.getCause().getMessage());
            logger.error("调用排班表注册信息接口【Gyb_pbbCspServiceImpl deletebatch】发生异常", e);
        }
        return result;
    }

    /**
     * 单个删除排班表
     */
    @Override
    public UtilResponse deleteOne(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Gyb_pbbModel bean = (Gyb_pbbModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            Integer ref = Gyb_pbbModelMapper.deleteOne(bean);
            result.getResResult().put("ref", ref.toString());
        } catch (Exception e) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("删除排班表信息失败:" + e.getCause().getMessage());
            logger.error("调用排班表注册信息接口【Gyb_pbbCspServiceImpl deletebatch】发生异常", e);
        }
        return result;
    }

    /**
     * 排班表新增保存修改
     */
    @Override
    public UtilResponse insertbatch(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Gyb_pbbModel bean = (Gyb_pbbModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            //门牌号必填
            if (StrUtil.isBlank(bean.getMph())){
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                String s="必须选择门牌号";
                result.setResultMsg(s);
                logger.error("调用排班表信息保存接口【Gyb_pbbCspServiceImpl savebatch】发生异常", s);
                return result;
            }
            //验证某人是否已经在该时段有排班,同一个科室只能有一个班
            Gyb_pbbModel gybPbbModel1 = new Gyb_pbbModel();
            gybPbbModel1.setKsbm(bean.getKsbm());
            gybPbbModel1.setRybm(bean.getRybm());
            gybPbbModel1.setYljgbm(bean.getYljgbm());
            gybPbbModel1.setBeginrq(bean.getSbsj());
            gybPbbModel1.setEndrq(bean.getXbsj());
            gybPbbModel1.setSwxw(DateUtil.hour(bean.getSbsj(),true)<12?"上午":"下午");
            List<Gyb_pbbModel> gybPbbModelList1 = Gyb_pbbModelMapper.queryGyb_pbbList(gybPbbModel1);
            if (gybPbbModelList1 != null && gybPbbModelList1.size() > 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                String s="排班数据已经存在";
                result.setResultMsg(s);
                logger.error("调用排班表信息保存接口【Gyb_pbbCspServiceImpl savebatch】发生异常", s);
                return result;
            }
            //修改前验证门牌号是否冲突，除自己外其他医生不能占用房间
            Gyb_pbbModel gybPbbModel2 = new Gyb_pbbModel();
            gybPbbModel2.setMyrybm(bean.getRybm());
            gybPbbModel2.setYljgbm(bean.getYljgbm());
            gybPbbModel2.setBeginrq(bean.getSbsj());
            gybPbbModel2.setEndrq(bean.getXbsj());
            gybPbbModel2.setMph(bean.getMph());
            gybPbbModel2.setSwxw(DateUtil.hour(bean.getSbsj(),true)<12?"上午":"下午");
            List<Gyb_pbbModel> gybPbbModelList2 = Gyb_pbbModelMapper.queryGyb_pbbList(gybPbbModel2);
            if (gybPbbModelList2 != null && gybPbbModelList2.size() > 0) {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                String s = "排班表信息保存失败:门牌号为" + bean.getMph() + "的房间已经被占用";
                result.setResultMsg(s);
                logger.error("调用排班表信息保存接口【Gyb_pbbCspServiceImpl savebatch】发生异常", s);
                return result;
            }
            Integer ref = Gyb_pbbModelMapper.insert(bean);
            result.getResResult().put("ref", ref.toString());
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("排班表信息保存失败:" + ex.getCause().getMessage());
            logger.error("调用排班表信息保存接口【Gyb_pbbCspServiceImpl save3Mem】发生异常", ex);
        }
        return result;
    }

    /**
     * 排班表修改保存
     */
    @Override
    public UtilResponse updatebatch(UtilRequest msg) throws Exception {
        UtilResponse result = UtilResponse.newInstance();
        try {
            Gyb_pbbModel bean = (Gyb_pbbModel) msg.getParam().get("bean");
            bean.setYljgbm(msg.getYljgbm());
            //修改前验证门牌号是否冲突
            Gyb_pbbModel gybPbbModel = new Gyb_pbbModel();
            gybPbbModel.setSbsj(bean.getSbsj());
            gybPbbModel.setXbsj(bean.getXbsj());
            gybPbbModel.setMph(bean.getMph());
            List<Gyb_pbbModel> gybPbbModelList = Gyb_pbbModelMapper.queryGyb_pbbList(gybPbbModel);
            if (gybPbbModelList == null || gybPbbModelList.size() == 0) {
                Integer ref = Gyb_pbbModelMapper.update(bean);
                result.getResResult().put("ref", ref.toString());
            } else {
                result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
                String s = "排班表信息保存失败:门牌号为" + bean.getMph() + "的房间已经被占用";
                result.setResultMsg(s);
                logger.error("调用排班表信息保存接口【Gyb_pbbCspServiceImpl savebatch】发生异常", s);
            }
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("排班表信息保存失败:" + ex.getCause().getMessage());
            logger.error("调用排班表信息保存接口【Gyb_pbbCspServiceImpl savebatch】发生异常", ex);
        }
        return result;
    }


    /**
     * 根据科室查询科室人员
     */
    @Override
    public UtilResponse queryGyb_rybmList(UtilRequest msg) {
        UtilResponse result = UtilResponse.newInstance();
        try {
            UserInfoModel userInfoModel = (UserInfoModel) msg.getUserinfo().get("userinfo"); //获取用户
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("ksbm", msg.getParam().get("ksbm"));
            map.put("rybm", msg.getParam().get("rybm"));
            map.put("yljgbm", msg.getYljgbm());
            map.put("firstDate", msg.getParam().get("firstDate"));
            List<CsqxInfoModel> csqxinfo = (List<CsqxInfoModel>) msg.getCsqxinfo().get("csqxinfo");// 接收参数权限
            String csck = Utilpubfun.getCsqxAll(csqxinfo, "N05001400202");
            if (("1").equals(csck)) {
                map.put("csck", "1");
            }
            List<Gyb_rybmModel> list = (List<Gyb_rybmModel>) Gyb_pbbModelMapper.queryGyb_rybm(map);

            result.getResResult().put("list", list);
        } catch (Exception ex) {
            result.setErrorCode(ISystemConstants.CSP_BUSINESS_ERROR);
            result.setResultMsg("排班表信息查询失败:" + ex.getCause().getMessage());
            logger.error("调用查询单个排班表信息信息接口【Gyb_pbbCspServiceImpl queryGyb_pbbOne】发生异常", ex);
        }
        return result;
    }


}
