package com.jsg.csp.cdyb.service;

import com.github.pagehelper.PageInfo;
import com.jsg.csp.api.cdyb.pojo.*;
import com.jsg.csp.api.cdyb.service.IYb_3101Service;
import com.jsg.csp.service.ServiceInvocationHelper;
import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;
import com.jsg.csp.cdyb.dao.Yb3101ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @program: geiHis
 * @author: liangrui
 * @create: 2024-08-30 11:10
 */
public class Yb_3101CspServiceImpl extends ServiceInvocationHelper implements IYb_3101Service {
    @Autowired
    private Yb3101ModelMapper yb3101ModelMapper;

    @Override
    public UtilResponse queryPatient(UtilRequest msg) {
        String zyh = msg.getBmString();
        UtilResponse result = UtilResponse.newInstance();
        List<PatientModel> list = yb3101ModelMapper.queryPatient(zyh);
        PageInfo<PatientModel> pageInfo = new PageInfo<>(list);
        result.getResResult().put("pageInfo", pageInfo);
        return result;
    }

    @Override
    public UtilResponse queryFsiEncounter(UtilRequest msg) {
        String zyh = msg.getBmString();
        UtilResponse result = UtilResponse.newInstance();
        List<FsiEncounterModel> list = yb3101ModelMapper.queryFsiEncounter(zyh);
        PageInfo<FsiEncounterModel> pageInfo = new PageInfo<>(list);
        result.getResResult().put("pageInfo", pageInfo);
        return result;
    }

    @Override
    public UtilResponse queryFsiDiagnose(UtilRequest msg) {
        String zyh = msg.getBmString();
        UtilResponse result = UtilResponse.newInstance();
        List<FsiDiagnoseModel> list = yb3101ModelMapper.queryFsiDiagnose(zyh);
        PageInfo<FsiDiagnoseModel> pageInfo = new PageInfo<>(list);
        result.getResResult().put("pageInfo", pageInfo);
        return result;
    }

    @Override
    public UtilResponse queryFsiOrder(UtilRequest msg) {
        String zyh = msg.getBmString();
        UtilResponse result = UtilResponse.newInstance();
        List<FsiOrderModel> list = yb3101ModelMapper.queryFsiOrder(zyh);
        PageInfo<FsiOrderModel> pageInfo = new PageInfo<>(list);
        result.getResResult().put("pageInfo", pageInfo);
        return result;
    }

    @Override
    public UtilResponse queryFsiOperation(UtilRequest msg) {
        String zyh = msg.getBmString();
        UtilResponse result = UtilResponse.newInstance();
        List<FsiOperationModel> list = yb3101ModelMapper.queryFsiOperation(zyh);
        PageInfo<FsiOperationModel> pageInfo = new PageInfo<>(list);
        result.getResResult().put("pageInfo", pageInfo);
        return result;
    }
}
