package com.jsg.csp.api.zyys.ysyw.pojo;

import com.jsg.common.pojo.DataGrid;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class Zyys_ylyzModel extends DataGrid implements Serializable  {
	private String yljgbm;
	private String ylyzxh;
    private int mxxh;
    private String xhid;  //序号ID
    private String zxxhid;  //临时存放为了与执行单序号ID同步
    private String zyh;
    private String ksbm;
    private String yzlx;
    private String yzfl;
    private String jcfl;
    private String yebh;
    private String xseyzbz;
    private Date ksrq;
    private String xdys;
    private String xdysxm;
    private String rymxzlbm;
    private String rymxzlmc;
    private String ryypyzxh;
    private int ryypmxxh;
    private String gllb;
    private String hldj;
    private String bqdj;
    private String tsyz;
    private String ryjyxh;
    private String sqdh;
    private int xssx;
    private String yssm;
    private Double sl;
    private Double dj;
    private String sfjj;
    private String ysqm;
    private String ysqmxm;
    private String ysqmks;
    private String ysqmksmc;
    private Date ysqmsj;
    private String ysecqm;
    private String ysecqmxm;
    private String ysecqmks;
    private String ysecqmksmc;
    private Date ysecqmsj;
    private String zxbz;
    private String zxhs;
    private String zxhsxm;
    private String zxks;
    private String zxksmc;
    private Date zxsj;
    private String hsecqm;
    private String hsecqmxm;
    private Date hsecqmsj;
    private String shbz;
    private String shhs;
    private String shhsxm;
    private Date shsj;
    private String ystzbz;
    private String tzys;
    private String tzysxm;
    private Date ystzsj;
    private String ystzsm;
    private String hstzbz;
    private String tzhs;
    private String tzhsxm;
    private Date hstzsj;
    private String yzzcbz;
    private String yzzcys;
    private String yzzcysxm;
    private Date yzzcsj;
    private String sfjz;
    private String sqlbbm;
    private String zfbz;
    private String zfys;
    private String zfysxm;
    private Date zfsj;
    private String zfyy;
    private String bzsm;
    private String ksmc;
    private String ybtclb;
    private String ybtclbmc;
    private String nbtclb;
    private Date zx_begin;
    private Date zx_end;
    private int zxcs;
    private int zxts;
    private int numb;
    private Date zxkssj;
    private String mbbm; //检查检验模板编码
    private String mbxmbm; //检查检验模板项目编码
    private String[] searchxhid;  //用于查询序号ID
    private String pcbm;//频次编码
    private String pcmc;//频次名称
    private Double pccs;//频次次数

    private String lczd;   //临床诊断（主要用于检查检验申请单）
    private String jcms;   //检查描述（主要用于检查检验申请单）
    private String jcbw;   //检查部位（主要用于检查检验申请单）
    private String lczz;   //临床症状（主要用于检查检验申请单）
    private String bbsm;   //标本说明（主要用于检查检验申请单）
    private String jymd;   //检验目的（主要用于检查检验申请单）
    private String jybb;   //检验标本（主要用于检查检验申请单）

    private String lcljJdbm; //临床路径阶段编码
	private String lcljJdmc; //阶段名称
	private  String lcljYzxmbm;//医嘱项目编码
	private String lcljYzxmmc;  //医嘱项目名称
	private String psbz;       //皮试标志
    private Map<String, Object> queryXhids;
    private Date shzfsj;//审核作废时间

    private String xkdybz; // 医嘱变更业务-新开打印标志
	private String tzdybz; // 医嘱变更业务-停止打印标志
	private String cxdybz; // 医嘱变更业务-撤销打印标志

	private String dysj; // 打印时间

	private String yyffmc; // 用药方法名称
    private String cyzd;//出院诊断(开今日出院)

private String yke186;

private int fzh;

    private String yyzq; // 特殊诊断名称

    private String  zysrcs;

    private String yzuuid; //yc用

    public String getYzuuid() {
        return yzuuid;
    }

    public void setYzuuid(String yzuuid) {
        this.yzuuid = yzuuid;
    }

    public String getZysrcs() {
        return zysrcs;
    }

    public void setZysrcs(String zysrcs) {
        this.zysrcs = zysrcs;
    }

    public String getYyzq() {
        return yyzq;
    }

    public void setYyzq(String yyzq) {
        this.yyzq = yyzq;
    }

    public int getFzh() {
        return fzh;
    }

    public void setFzh(int fzh) {
        this.fzh = fzh;
    }

    private String ypyzxh;



    public String getYpyzxh() {
        return ypyzxh;
    }

    public void setYpyzxh(String ypyzxh) {
        this.ypyzxh = ypyzxh;
    }

    public String getYke186() {
        return yke186;
    }

    public void setYke186(String yke186) {
        this.yke186 = yke186;
    }

    private String[] searchylyzxh;  //用于查询序号ID

    public String[] getSearchylyzxh() {
        return searchylyzxh;
    }

    public void setSearchylyzxh(String[] searchylyzxh) {
        this.searchylyzxh = searchylyzxh;
    }

    public String getCyzd() {
        return cyzd;
    }

    public void setCyzd(String cyzd) {
        this.cyzd = cyzd;
    }

    public String getYyffmc() {
		return yyffmc;
	}

	public void setYyffmc(String yyffmc) {
		this.yyffmc = yyffmc;
	}

	public String getDysj() {
		return dysj;
	}

	public void setDysj(String dysj) {
		this.dysj = dysj;
	}

	public String getXkdybz() {
		return xkdybz;
	}

	public void setXkdybz(String xkdybz) {
		this.xkdybz = xkdybz;
	}

	public String getTzdybz() {
		return tzdybz;
	}

	public void setTzdybz(String tzdybz) {
		this.tzdybz = tzdybz;
	}

	public String getCxdybz() {
		return cxdybz;
	}

	public void setCxdybz(String cxdybz) {
		this.cxdybz = cxdybz;
	}

    public Date getShzfsj() {
        return shzfsj;
    }

    public void setShzfsj(Date shzfsj) {
        this.shzfsj = shzfsj;
    }

    public String getMbbm() {
		return mbbm;
	}
	public void setMbbm(String mbbm) {
		this.mbbm = mbbm;
	}
	public Map<String, Object> getQueryXhids() {
		return queryXhids;
	}
	public void setQueryXhids(Map<String, Object> queryXhids) {
		this.queryXhids = queryXhids;
	}
	public String getXseyzbz() {
		return xseyzbz;
	}
	public void setXseyzbz(String xseyzbz) {
		this.xseyzbz = xseyzbz;
	}
	public Date getZxkssj() {
		return zxkssj;
	}
	public void setZxkssj(Date zxkssj) {
		this.zxkssj = zxkssj;
	}
	public String getYlyzxh() {
        return ylyzxh;
    }
    public void setYlyzxh(String ylyzxh) {
        this.ylyzxh = ylyzxh == null ? null : ylyzxh.trim();
    }
    public int getMxxh() {
        return mxxh;
    }
    public void setMxxh(int mxxh) {
        this.mxxh = mxxh;
    }
    public String getZyh() {
        return zyh;
    }
    public void setZyh(String zyh) {
        this.zyh = zyh == null ? null : zyh.trim();
    }
    public String getKsbm() {
        return ksbm;
    }
    public void setKsbm(String ksbm) {
        this.ksbm = ksbm == null ? null : ksbm.trim();
    }
    public String getYzlx() {
        return yzlx;
    }
    public void setYzlx(String yzlx) {
        this.yzlx = yzlx == null ? null : yzlx.trim();
    }

    public String getYzfl() {
		return yzfl;
	}
	public void setYzfl(String yzfl) {
		this.yzfl = yzfl;
	}
	public String getJcfl() {
        return jcfl;
    }
    public void setJcfl(String jcfl) {
        this.jcfl = jcfl == null ? null : jcfl.trim();
    }
    public String getYebh() {
        return yebh;
    }
    public void setYebh(String yebh) {
        this.yebh = yebh == null ? null : yebh.trim();
    }
    public Date getKsrq() {
        return ksrq;
    }
    public void setKsrq(Date ksrq) {
        this.ksrq = ksrq;
    }
    public String getXdys() {
        return xdys;
    }
    public void setXdys(String xdys) {
        this.xdys = xdys == null ? null : xdys.trim();
    }
    public String getRymxzlbm() {
        return rymxzlbm;
    }
    public void setRymxzlbm(String rymxzlbm) {
        this.rymxzlbm = rymxzlbm == null ? null : rymxzlbm.trim();
    }
    public String getRymxzlmc() {
        return rymxzlmc;
    }
    public void setRymxzlmc(String rymxzlmc) {
        this.rymxzlmc = rymxzlmc == null ? null : rymxzlmc.trim();
    }
    public String getRyypyzxh() {
        return ryypyzxh;
    }
    public void setRyypyzxh(String ryypyzxh) {
        this.ryypyzxh = ryypyzxh == null ? null : ryypyzxh.trim();
    }
    public int getRyypmxxh() {
        return ryypmxxh;
    }
    public void setRyypmxxh(int ryypmxxh) {
        this.ryypmxxh = ryypmxxh;
    }
    public String getGllb() {
        return gllb;
    }
    public void setGllb(String gllb) {
        this.gllb = gllb == null ? null : gllb.trim();
    }
    public String getHldj() {
        return hldj;
    }
    public void setHldj(String hldj) {
        this.hldj = hldj == null ? null : hldj.trim();
    }
    public String getTsyz() {
        return tsyz;
    }
    public void setTsyz(String tsyz) {
        this.tsyz = tsyz == null ? null : tsyz.trim();
    }
    public String getRyjyxh() {
        return ryjyxh;
    }
    public void setRyjyxh(String ryjyxh) {
        this.ryjyxh = ryjyxh == null ? null : ryjyxh.trim();
    }
    public String getSqdh() {
        return sqdh;
    }
    public void setSqdh(String sqdh) {
        this.sqdh = sqdh == null ? null : sqdh.trim();
    }
    public int getXssx() {
        return xssx;
    }
    public void setXssx(int xssx) {
        this.xssx = xssx;
    }
    public String getYssm() {
        return yssm;
    }
    public void setYssm(String yssm) {
        this.yssm = yssm == null ? null : yssm.trim();
    }
    public Double getSl() {
        return sl;
    }
    public void setSl(Double sl) {
        this.sl = sl;
    }
    public Double getDj() {
        return dj;
    }
    public void setDj(Double dj) {
        this.dj = dj;
    }
    public String getSfjj() {
        return sfjj;
    }
    public void setSfjj(String sfjj) {
        this.sfjj = sfjj == null ? null : sfjj.trim();
    }
    public String getYsqm() {
        return ysqm;
    }
    public void setYsqm(String ysqm) {
        this.ysqm = ysqm == null ? null : ysqm.trim();
    }
    public String getYsqmxm() {
        return ysqmxm;
    }
    public void setYsqmxm(String ysqmxm) {
        this.ysqmxm = ysqmxm == null ? null : ysqmxm.trim();
    }
    public String getYsqmks() {
        return ysqmks;
    }
    public void setYsqmks(String ysqmks) {
        this.ysqmks = ysqmks == null ? null : ysqmks.trim();
    }
    public String getYsqmksmc() {
        return ysqmksmc;
    }
    public void setYsqmksmc(String ysqmksmc) {
        this.ysqmksmc = ysqmksmc == null ? null : ysqmksmc.trim();
    }
    public Date getYsqmsj() {
        return ysqmsj;
    }
    public void setYsqmsj(Date ysqmsj) {
        this.ysqmsj = ysqmsj;
    }
    public String getYsecqm() {
        return ysecqm;
    }
    public void setYsecqm(String ysecqm) {
        this.ysecqm = ysecqm == null ? null : ysecqm.trim();
    }
    public String getYsecqmxm() {
        return ysecqmxm;
    }
    public void setYsecqmxm(String ysecqmxm) {
        this.ysecqmxm = ysecqmxm == null ? null : ysecqmxm.trim();
    }
    public String getYsecqmks() {
        return ysecqmks;
    }
    public void setYsecqmks(String ysecqmks) {
        this.ysecqmks = ysecqmks == null ? null : ysecqmks.trim();
    }
    public String getYsecqmksmc() {
        return ysecqmksmc;
   }
    public void setYsecqmksmc(String ysecqmksmc) {
        this.ysecqmksmc = ysecqmksmc == null ? null : ysecqmksmc.trim();
    }
    public Date getYsecqmsj() {
        return ysecqmsj;
    }
    public void setYsecqmsj(Date ysecqmsj) {
        this.ysecqmsj = ysecqmsj;
    }
    public String getZxbz() {
        return zxbz;
    }
    public void setZxbz(String zxbz) {
        this.zxbz = zxbz == null ? null : zxbz.trim();
    }
    public String getZxhs() {
        return zxhs;
    }
    public void setZxhs(String zxhs) {
        this.zxhs = zxhs == null ? null : zxhs.trim();
    }
     public String getZxhsxm() {
        return zxhsxm;
    }
    public void setZxhsxm(String zxhsxm) {
        this.zxhsxm = zxhsxm == null ? null : zxhsxm.trim();
    }
    public String getZxks() {
        return zxks;
    }
    public void setZxks(String zxks) {
        this.zxks = zxks == null ? null : zxks.trim();
    }
    public String getZxksmc() {
        return zxksmc;
    }
    public void setZxksmc(String zxksmc) {
        this.zxksmc = zxksmc == null ? null : zxksmc.trim();
    }
    public Date getZxsj() {
        return zxsj;
    }
    public void setZxsj(Date zxsj) {
        this.zxsj = zxsj;
    }
    public String getHsecqm() {
        return hsecqm;
    }
    public void setHsecqm(String hsecqm) {
        this.hsecqm = hsecqm == null ? null : hsecqm.trim();
    }
    public String getHsecqmxm() {
        return hsecqmxm;
    }
    public void setHsecqmxm(String hsecqmxm) {
        this.hsecqmxm = hsecqmxm == null ? null : hsecqmxm.trim();
    }
    public Date getHsecqmsj() {
        return hsecqmsj;
    }
    public void setHsecqmsj(Date hsecqmsj) {
        this.hsecqmsj = hsecqmsj;
    }
    public String getShbz() {
        return shbz;
    }
    public void setShbz(String shbz) {
        this.shbz = shbz == null ? null : shbz.trim();
    }
    public String getShhs() {
        return shhs;
    }
    public void setShhs(String shhs) {
        this.shhs = shhs == null ? null : shhs.trim();
    }
    public String getShhsxm() {
        return shhsxm;
    }
    public void setShhsxm(String shhsxm) {
        this.shhsxm = shhsxm == null ? null : shhsxm.trim();
    }
    public Date getShsj() {
        return shsj;
    }
    public void setShsj(Date shsj) {
        this.shsj = shsj;
    }
    public String getYstzbz() {
        return ystzbz;
    }
    public void setYstzbz(String ystzbz) {
        this.ystzbz = ystzbz == null ? null : ystzbz.trim();
    }
    public String getTzys() {
        return tzys;
    }
    public void setTzys(String tzys) {
        this.tzys = tzys == null ? null : tzys.trim();
    }
    public String getTzysxm() {
        return tzysxm;
    }
    public void setTzysxm(String tzysxm) {
        this.tzysxm = tzysxm == null ? null : tzysxm.trim();
    }
    public Date getYstzsj() {
        return ystzsj;
    }
    public void setYstzsj(Date ystzsj) {
        this.ystzsj = ystzsj;
    }
    public String getYstzsm() {
        return ystzsm;
    }
    public void setYstzsm(String ystzsm) {
        this.ystzsm = ystzsm == null ? null : ystzsm.trim();
    }
    public String getHstzbz() {
        return hstzbz;
    }
    public void setHstzbz(String hstzbz) {
        this.hstzbz = hstzbz == null ? null : hstzbz.trim();
    }
    public String getTzhs() {
        return tzhs;
    }
    public void setTzhs(String tzhs) {
        this.tzhs = tzhs == null ? null : tzhs.trim();
    }
    public String getTzhsxm() {
        return tzhsxm;
    }
    public void setTzhsxm(String tzhsxm) {
        this.tzhsxm = tzhsxm == null ? null : tzhsxm.trim();
    }
    public Date getHstzsj() {
        return hstzsj;
    }
    public void setHstzsj(Date hstzsj) {
        this.hstzsj = hstzsj;
    }
    public String getYzzcbz() {
        return yzzcbz;
    }
    public void setYzzcbz(String yzzcbz) {
        this.yzzcbz = yzzcbz == null ? null : yzzcbz.trim();
    }
    public String getYzzcys() {
        return yzzcys;
    }
    public void setYzzcys(String yzzcys) {
        this.yzzcys = yzzcys == null ? null : yzzcys.trim();
    }
    public String getYzzcysxm() {
        return yzzcysxm;
    }
    public void setYzzcysxm(String yzzcysxm) {
        this.yzzcysxm = yzzcysxm == null ? null : yzzcysxm.trim();
    }
    public Date getYzzcsj() {
        return yzzcsj;
    }
    public void setYzzcsj(Date yzzcsj) {
        this.yzzcsj = yzzcsj;
    }
    public String getSfjz() {
        return sfjz;
    }
    public void setSfjz(String sfjz) {
        this.sfjz = sfjz == null ? null : sfjz.trim();
    }
    public String getSqlbbm() {
        return sqlbbm;
    }
    public void setSqlbbm(String sqlbbm) {
        this.sqlbbm = sqlbbm == null ? null : sqlbbm.trim();
    }
    public String getZfbz() {
        return zfbz;
    }
    public void setZfbz(String zfbz) {
        this.zfbz = zfbz == null ? null : zfbz.trim();
    }
    public String getZfys() {
        return zfys;
    }
    public void setZfys(String zfys) {
        this.zfys = zfys == null ? null : zfys.trim();
    }
    public String getZfysxm() {
        return zfysxm;
    }
    public void setZfysxm(String zfysxm) {
        this.zfysxm = zfysxm == null ? null : zfysxm.trim();
    }
    public Date getZfsj() {
        return zfsj;
    }
    public void setZfsj(Date zfsj) {
        this.zfsj = zfsj;
    }
    public String getZfyy() {
        return zfyy;
    }
    public void setZfyy(String zfyy) {
        this.zfyy = zfyy == null ? null : zfyy.trim();
    }
    public String getBzsm() {
        return bzsm;
    }
    public void setBzsm(String bzsm) {
        this.bzsm = bzsm == null ? null : bzsm.trim();
    }
    public String getKsmc() {
        return ksmc;
    }
    public void setKsmc(String ksmc) {
        this.ksmc = ksmc == null ? null : ksmc.trim();
    }
	public String getXdysxm() {
		return xdysxm;
	}
	public void setXdysxm(String xdysxm) {
		this.xdysxm = xdysxm;
	}
	public String getBqdj() {
		return bqdj;
	}
	public void setBqdj(String bqdj) {
		this.bqdj = bqdj;
	}
	public String getYbtclb() {
		return ybtclb;
	}
	public void setYbtclb(String ybtclb) {
		this.ybtclb = ybtclb;
	}
	public String getYbtclbmc() {
		return ybtclbmc;
	}
	public void setYbtclbmc(String ybtclbmc) {
		this.ybtclbmc = ybtclbmc;
	}
	public String getNbtclb() {
		return nbtclb;
	}
	public void setNbtclb(String nbtclb) {
		this.nbtclb = nbtclb;
	}
	public Date getZx_begin() {
		return zx_begin;
	}
	public void setZx_begin(Date zx_begin) {
		this.zx_begin = zx_begin;
	}
	public Date getZx_end() {
		return zx_end;
	}
	public void setZx_end(Date zx_end) {
		this.zx_end = zx_end;
	}
	public int getZxcs() {
		return zxcs;
	}
	public void setZxcs(int zxcs) {
		this.zxcs = zxcs;
	}
	public int getZxts() {
		return zxts;
	}
	public void setZxts(int zxts) {
		this.zxts = zxts;
	}
	public String getXhid() {
		return xhid;
	}
	public void setXhid(String xhid) {
		this.xhid = xhid;
	}
	public String[] getSearchxhid() {
		return searchxhid;
	}
	public void setSearchxhid(String[] searchxhid) {
		this.searchxhid = searchxhid;
	}
	public int getNumb() {
		return numb;
	}
	public void setNumb(int numb) {
		this.numb = numb;
	}
	public String getZxxhid() {
		return zxxhid;
	}
	public void setZxxhid(String zxxhid) {
		this.zxxhid = zxxhid;
	}
	public String getLczd() {
		return lczd;
	}
	public void setLczd(String lczd) {
		this.lczd = lczd;
	}
	public String getJcms() {
		return jcms;
	}
	public void setJcms(String jcms) {
		this.jcms = jcms;
	}
	public String getJcbw() {
		return jcbw;
	}
	public void setJcbw(String jcbw) {
		this.jcbw = jcbw;
	}
	public String getLczz() {
		return lczz;
	}
	public void setLczz(String lczz) {
		this.lczz = lczz;
	}
	public String getBbsm() {
		return bbsm;
	}
	public void setBbsm(String bbsm) {
		this.bbsm = bbsm;
	}
	public String getJymd() {
		return jymd;
	}
	public void setJymd(String jymd) {
		this.jymd = jymd;
	}
	public String getYljgbm() {
		return yljgbm;
	}
	public void setYljgbm(String yljgbm) {
		this.yljgbm = yljgbm;
	}
	public String getLcljJdbm() {
		return lcljJdbm;
	}
	public void setLcljJdbm(String lcljJdbm) {
		this.lcljJdbm = lcljJdbm;
	}
	public String getLcljJdmc() {
		return lcljJdmc;
	}
	public void setLcljJdmc(String lcljJdmc) {
		this.lcljJdmc = lcljJdmc;
	}
	public String getLcljYzxmbm() {
		return lcljYzxmbm;
	}
	public void setLcljYzxmbm(String lcljYzxmbm) {
		this.lcljYzxmbm = lcljYzxmbm;
	}
	public String getLcljYzxmmc() {
		return lcljYzxmmc;
	}
	public void setLcljYzxmmc(String lcljYzxmmc) {
		this.lcljYzxmmc = lcljYzxmmc;
	}

    public String getJybb() {
        return jybb;
    }

    public void setJybb(String jybb) {
        this.jybb = jybb;
    }
	public String getPsbz() {
		return psbz;
	}
	public void setPsbz(String psbz) {
		this.psbz = psbz;
	}
	public String getMbxmbm() {
		return mbxmbm;
	}
	public void setMbxmbm(String mbxmbm) {
		this.mbxmbm = mbxmbm;
	}

    public String getPcbm() {
        return pcbm;
    }

    public void setPcbm(String pcbm) {
        this.pcbm = pcbm;
    }

    public String getPcmc() {
        return pcmc;
    }

    public void setPcmc(String pcmc) {
        this.pcmc = pcmc;
    }

    public Double getPccs() {
        return pccs;
    }

    public void setPccs(Double pccs) {
        this.pccs = pccs;
    }
}
