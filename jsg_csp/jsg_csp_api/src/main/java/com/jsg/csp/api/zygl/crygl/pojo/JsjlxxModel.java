package com.jsg.csp.api.zygl.crygl.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
* @ClassName: JsjlxxModel
* @Description: TODO(结算管理所需字段)
* <AUTHOR>
* @date 2020年6月20日 下午5:15:49
*
 */
public class JsjlxxModel implements Serializable{
	/**************病人基本信息**********************/
	private String brid; //病人id
	public String getBrid() {
		return brid;
	}
	public void setBrid(String brid) {
		this.brid = brid;
	}
	private Date csrq;
	private String brxm; //病人姓名
	private String brxb; //病人性别
	private String jzdmc ; //居住地名称
	private String sfzjhm ; //身份证件号码
	/***************病人入院登记信息*********************/
	private Short nl; //年龄
	private String zyh; //住院号
	private String bxlbbm;//保险类别编码
	private String bxlbmc; //保险类别名称
	private String brfb; //入院费别
    private String brfbmc; //入院费别名称
	private String ryks; //住院科室
	private String ryksmc; //住院科室
	private String rycwid; //床位id
	private String rycwbh; //床位编号
	private String zyys; //住院医生
    private String zyysxm; //住院医生姓名
	private Date ryrq; //入院日期
	private Date cyrq; //出院日期
	private String bqcybz;//病区出院标志
	private Date bqcyrq;//病区出院日期
	private String ryzdbm;//入院诊断编码
    private String ryzdmc;//入院诊断名称

	/***************医疗卡信息*********************/
	private String ylklx; //医疗卡类型
	private String ylkh; //医疗卡号
	private Double zhzfje; //账户支付金额

	/****************以下字段不计入sql查询中****************/

	/*****展示信息需要字段*****/
	private Double fyhj; //费用合计（将该病人所有费用合计）
	private Double zhhj; //账户合计 （将该病人所有预交记录合计）
	private Double zhye; //账户余额（账户合计-费用合计）
	private long zyts; //计算住院天数
	private Double yhjehj;//优惠金额合计
	private Double qtzfhj; //其他支付合计
	private Double jsqf;//结算欠费
	private Double xjzf;//现金支付

	/******设计参数权限的字段**********/
	//结算记录参数权限
	private String ywckbh;//业务窗口编号
	private String ifyxqf; //是否允许欠费 0不 1允许
	private String ifyxqtzf; //是否允许其他支付  0 不 1允许
	private String yhfs; //优惠方式 0 不 1 录入优惠总金额 2 按细类录入

	private List<Zyb_jsjl_qtzfModel> qtzfList;//其他支付集合
	private List<Zyb_jsjl_yhjeModel> yhjeList; //优惠金额集合
	private Zyb_jsjlModel jsjl;//结算记录对象

	private String cxlx;//查询类型（针对判断查询住院类型的病人0---在院，且病区在院，且已安床，1--只查出院，2---只查病区出院）

	private BigDecimal jzbl; //记账比例
	private String yljgbm; //医疗机构编码
	private Double jzxe;  //记帐限额

	private String zflxbm; // 支付类型编码
	private String ysks; // 医生科室


	public String getYsks() {
		return ysks;
	}

	public void setYsks(String ysks) {
		this.ysks = ysks;
	}

	public String getZflxbm() {
		return zflxbm;
	}
	public void setZflxbm(String zflxbm) {
		this.zflxbm = zflxbm;
	}
	public BigDecimal getJzbl() {
		return jzbl;
	}

	public void setJzbl(BigDecimal jzbl) {
		this.jzbl = jzbl;
	}

	public Date getCsrq() {
		return csrq;
	}
	public void setCsrq(Date csrq) {
		this.csrq = csrq;
	}
	public String getBqcybz() {
		return bqcybz;
	}
	public void setBqcybz(String bqcybz) {
		this.bqcybz = bqcybz;
	}
	public Date getBqcyrq() {
		return bqcyrq;
	}
	public void setBqcyrq(Date bqcyrq) {
		this.bqcyrq = bqcyrq;
	}

	public String getBrxm() {
		return brxm;
	}
	public void setBrxm(String brxm) {
		this.brxm = brxm;
	}
	public String getBrxb() {
		return brxb;
	}
	public void setBrxb(String brxb) {
		this.brxb = brxb;
	}
	public String getJzdmc() {
		return jzdmc;
	}
	public void setJzdmc(String jzdmc) {
		this.jzdmc = jzdmc;
	}
	public String getSfzjhm() {
		return sfzjhm;
	}
	public void setSfzjhm(String sfzjhm) {
		this.sfzjhm = sfzjhm;
	}
	public Short getNl() {
		return nl;
	}
	public void setNl(Short nl) {
		this.nl = nl;
	}
	public String getZyh() {
		return zyh;
	}
	public void setZyh(String zyh) {
		this.zyh = zyh;
	}
	public String getBxlbbm() {
		return bxlbbm;
	}
	public void setBxlbbm(String bxlbbm) {
		this.bxlbbm = bxlbbm;
	}
	public String getBxlbmc() {
		return bxlbmc;
	}
	public void setBxlbmc(String bxlbmc) {
		this.bxlbmc = bxlbmc;
	}
	public String getBrfb() {
		return brfb;
	}
	public void setBrfb(String brfb) {
		this.brfb = brfb;
	}
	public String getBrfbmc() {
		return brfbmc;
	}
	public void setBrfbmc(String brfbmc) {
		this.brfbmc = brfbmc;
	}
	public String getRyks() {
		return ryks;
	}
	public void setRyks(String ryks) {
		this.ryks = ryks;
	}
	public String getRyksmc() {
		return ryksmc;
	}
	public void setRyksmc(String ryksmc) {
		this.ryksmc = ryksmc;
	}
	public String getRycwid() {
		return rycwid;
	}
	public void setRycwid(String rycwid) {
		this.rycwid = rycwid;
	}
	public String getRycwbh() {
		return rycwbh;
	}
	public void setRycwbh(String rycwbh) {
		this.rycwbh = rycwbh;
	}
	public String getZyys() {
		return zyys;
	}
	public void setZyys(String zyys) {
		this.zyys = zyys;
	}
	public String getZyysxm() {
		return zyysxm;
	}
	public void setZyysxm(String zyysxm) {
		this.zyysxm = zyysxm;
	}
	public Date getRyrq() {
		return ryrq;
	}
	public void setRyrq(Date ryrq) {
		this.ryrq = ryrq;
	}
	public Date getCyrq() {
		return cyrq;
	}
	public void setCyrq(Date cyrq) {
		this.cyrq = cyrq;
	}
	public String getRyzdbm() {
		return ryzdbm;
	}
	public void setRyzdbm(String ryzdbm) {
		this.ryzdbm = ryzdbm;
	}
	public String getRyzdmc() {
		return ryzdmc;
	}
	public void setRyzdmc(String ryzdmc) {
		this.ryzdmc = ryzdmc;
	}
	public String getYlklx() {
		return ylklx;
	}
	public void setYlklx(String ylklx) {
		this.ylklx = ylklx;
	}
	public String getYlkh() {
		return ylkh;
	}
	public void setYlkh(String ylkh) {
		this.ylkh = ylkh;
	}
	public Double getZhzfje() {
		return zhzfje;
	}
	public void setZhzfje(Double zhzfje) {
		this.zhzfje = zhzfje;
	}
	public Double getFyhj() {
		return fyhj;
	}
	public void setFyhj(Double fyhj) {
		this.fyhj = fyhj;
	}
	public Double getZhhj() {
		return zhhj;
	}
	public void setZhhj(Double zhhj) {
		this.zhhj = zhhj;
	}
	public Double getZhye() {
		return zhye;
	}
	public void setZhye(Double zhye) {
		this.zhye = zhye;
	}
	public long getZyts() {
		return zyts;
	}
	public void setZyts(long zyts) {
		this.zyts = zyts;
	}
	public Double getYhjehj() {
		return yhjehj;
	}
	public void setYhjehj(Double yhjehj) {
		this.yhjehj = yhjehj;
	}
	public Double getQtzfhj() {
		return qtzfhj;
	}
	public void setQtzfhj(Double qtzfhj) {
		this.qtzfhj = qtzfhj;
	}
	public Double getJsqf() {
		return jsqf;
	}
	public void setJsqf(Double jsqf) {
		this.jsqf = jsqf;
	}
	public Double getXjzf() {
		return xjzf;
	}
	public void setXjzf(Double xjzf) {
		this.xjzf = xjzf;
	}
	public String getYwckbh() {
		return ywckbh;
	}
	public void setYwckbh(String ywckbh) {
		this.ywckbh = ywckbh;
	}
	public String getIfyxqf() {
		return ifyxqf;
	}
	public void setIfyxqf(String ifyxqf) {
		this.ifyxqf = ifyxqf;
	}
	public String getIfyxqtzf() {
		return ifyxqtzf;
	}
	public void setIfyxqtzf(String ifyxqtzf) {
		this.ifyxqtzf = ifyxqtzf;
	}
	public String getYhfs() {
		return yhfs;
	}
	public void setYhfs(String yhfs) {
		this.yhfs = yhfs;
	}
	public List<Zyb_jsjl_qtzfModel> getQtzfList() {
		return qtzfList;
	}
	public void setQtzfList(List<Zyb_jsjl_qtzfModel> qtzfList) {
		this.qtzfList = qtzfList;
	}
	public List<Zyb_jsjl_yhjeModel> getYhjeList() {
		return yhjeList;
	}
	public void setYhjeList(List<Zyb_jsjl_yhjeModel> yhjeList) {
		this.yhjeList = yhjeList;
	}
	public Zyb_jsjlModel getJsjl() {
		return jsjl;
	}
	public void setJsjl(Zyb_jsjlModel jsjl) {
		this.jsjl = jsjl;
	}
	public String getCxlx() {
		return cxlx;
	}
	public void setCxlx(String cxlx) {
		this.cxlx = cxlx;
	}
	public String getYljgbm() {
		return yljgbm;
	}
	public void setYljgbm(String yljgbm) {
		this.yljgbm = yljgbm;
	}
	public Double getJzxe() {
		return jzxe;
	}
	public void setJzxe(Double jzxe) {
		this.jzxe = jzxe;
	}

}
