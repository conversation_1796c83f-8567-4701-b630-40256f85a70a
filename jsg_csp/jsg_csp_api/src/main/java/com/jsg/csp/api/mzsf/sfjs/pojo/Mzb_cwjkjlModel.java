package com.jsg.csp.api.mzsf.sfjs.pojo;

import java.io.Serializable;
import java.util.Date;

import com.jsg.common.pojo.DataGrid;
/**
 *
* @ClassName: Mzb_cwjkjlModel
* @Description: TODO(门诊财务交款记录)
* <AUTHOR>
* @date 2020年8月10日 下午11:43:59
*
 */
public class Mzb_cwjkjlModel extends DataGrid implements Serializable{
    private String jkjlid;

    private String jkpzh;

    private String czlx;

    private Date czrq;

    private String czry;

    private String czryxm;

    private String bzsm;

    private String yljgbm; //医疗机构编码

    public String getJkjlid() {
        return jkjlid;
    }

    public void setJkjlid(String jkjlid) {
        this.jkjlid = jkjlid == null ? null : jkjlid.trim();
    }

    public String getJkpzh() {
        return jkpzh;
    }

    public void setJkpzh(String jkpzh) {
        this.jkpzh = jkpzh == null ? null : jkpzh.trim();
    }

    public String getCzlx() {
        return czlx;
    }

    public void setCzlx(String czlx) {
        this.czlx = czlx == null ? null : czlx.trim();
    }

    public Date getCzrq() {
        return czrq;
    }

    public void setCzrq(Date czrq) {
        this.czrq = czrq;
    }

    public String getCzry() {
        return czry;
    }

    public void setCzry(String czry) {
        this.czry = czry == null ? null : czry.trim();
    }

    public String getCzryxm() {
        return czryxm;
    }

    public void setCzryxm(String czryxm) {
        this.czryxm = czryxm == null ? null : czryxm.trim();
    }

    public String getBzsm() {
        return bzsm;
    }

    public void setBzsm(String bzsm) {
        this.bzsm = bzsm == null ? null : bzsm.trim();
    }

	public String getYljgbm() {
		return yljgbm;
	}

	public void setYljgbm(String yljgbm) {
		this.yljgbm = yljgbm;
	}

}
