package com.jsg.csp.api.pubfun.pojo.sbkc;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
* @ClassName: YfkcclMsgModel
* @Description: TODO(设备库房设备库存处理请求类)
* <AUTHOR>
* @date 2020年5月22日 下午11:57:19
*
 */
public class SbkcclMsgModel implements Serializable{

	private String yfbm;  //设备库房编码
	//01－设备库房入库,02-盘盈入库,03－库房退库 04-设备库房出库,05-报损出库,06-设备库房盘亏出库,

	private String crlx;
	private String czybm; //操作员编码
	private String djh;   //单据号
	private String fypdjg;//1、判断批次单价是否与配方单价一致；0、不判断单价，按设备编码下帐
	private Map<String, Object> param = new HashMap<String, Object>();
		public String getYfbm() {
		return yfbm;
	}
	public void setYfbm(String yfbm) {
		this.yfbm = yfbm;
	}
	public String getCrlx() {
		return crlx;
	}
	public void setCrlx(String crlx) {
		this.crlx = crlx;
	}
	public String getCzybm() {
		return czybm;
	}
	public void setCzybm(String czybm) {
		this.czybm = czybm;
	}
	public String getDjh() {
		return djh;
	}
	public void setDjh(String djh) {
		this.djh = djh;
	}
	public Map<String, Object> getParam() {
		return param;
	}
	public void setParam(Map<String, Object> param) {
		this.param = param;
	}
	public String getFypdjg() {
		return fypdjg;
	}
	public void setFypdjg(String fypdjg) {
		this.fypdjg = fypdjg;
	}

}
