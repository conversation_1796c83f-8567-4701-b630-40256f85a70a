package com.jsg.csp.api.lis.bggl.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

import com.jsg.common.pojo.DataGrid;

public class Lis_bgzbModel extends DataGrid implements Serializable {

	private static final long serialVersionUID = -1L;
	//医疗机构
	private String yljgbm;
	//检验序号
	private String jyxh;
	//指标项目编码
	private String zbxm;
	//指标项目名称
	private String zbxmmc;
	//指标项目拼音
	private String zbxmpy;
	//检测结果
	private String jcjg;
	//结果列标志
	private String col;
	//数值结果
	private String value_n;
	//选择结果
	private String value_l;
	//文本结果
	private String value_t;
	//单位
	private String dw;
	//状态  null -1 偏低  0正常  1 偏高
	private String zt;
	//参考区间
	private String ckz_t;
	//最小值
	private BigDecimal n_min;
	//最大值
	private BigDecimal n_max;


	public String getCol() {
		return col;
	}
	public void setCol(String col) {
		this.col = col;
	}
	public String getJyxh() {
		return jyxh;
	}
	public void setJyxh(String jyxh) {
		this.jyxh = jyxh;
	}
	public String getZbxm() {
		return zbxm;
	}
	public void setZbxm(String zbxm) {
		this.zbxm = zbxm;
	}
	public String getZbxmmc() {
		return zbxmmc;
	}
	public void setZbxmmc(String zbxmmc) {
		this.zbxmmc = zbxmmc;
	}
	public String getZbxmpy() {
		return zbxmpy;
	}
	public void setZbxmpy(String zbxmpy) {
		this.zbxmpy = zbxmpy;
	}
	public String getJcjg() {
		return jcjg;
	}
	public void setJcjg(String jcjg) {
		this.jcjg = jcjg;
	}
	public String getDw() {
		return dw;
	}
	public void setDw(String dw) {
		this.dw = dw;
	}
	public String getZt() {
		return zt;
	}
	public void setZt(String zt) {
		this.zt = zt;
	}
	public String getCkz_t() {
		return ckz_t;
	}
	public void setCkz_t(String ckz_t) {
		this.ckz_t = ckz_t;
	}
	public String getValue_n() {
		return value_n;
	}
	public void setValue_n(String value_n) {
		this.value_n = value_n;
	}
	public String getValue_l() {
		return value_l;
	}
	public void setValue_l(String value_l) {
		this.value_l = value_l;
	}
	public String getValue_t() {
		return value_t;
	}
	public void setValue_t(String value_t) {
		this.value_t = value_t;
	}
	public String getYljgbm() {
		return yljgbm;
	}
	public void setYljgbm(String yljgbm) {
		this.yljgbm = yljgbm;
	}
	public BigDecimal getN_min() {
		return n_min;
	}
	public void setN_min(BigDecimal n_min) {
		this.n_min = n_min;
	}
	public BigDecimal getN_max() {
		return n_max;
	}
	public void setN_max(BigDecimal n_max) {
		this.n_max = n_max;
	}


}
