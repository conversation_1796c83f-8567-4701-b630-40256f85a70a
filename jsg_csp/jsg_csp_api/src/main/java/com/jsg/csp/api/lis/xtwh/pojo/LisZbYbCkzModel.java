package com.jsg.csp.api.lis.xtwh.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

import com.jsg.common.pojo.DataGrid;

public class LisZbYbCkzModel extends DataGrid implements Serializable{

	private static final long serialVersionUID = 1L;

	private String zbbm;

    private String ybbm;

    private String yljgbm;

	private BigDecimal allN;

    private BigDecimal allNH;

    private String allT;

    private BigDecimal manN;

    private BigDecimal manNH;

    private BigDecimal womanN;

    private BigDecimal womanNH;

    private String manT;

    private String womanT;

    private BigDecimal nl1;

    private BigDecimal nl2;

    private BigDecimal nl3;

    private BigDecimal nl4;

    private BigDecimal nl1N;

    private BigDecimal nl1NH;

    private BigDecimal nl2N;

    private BigDecimal nl2NH;

    private BigDecimal nl3N;

    private BigDecimal nl3NH;

    private BigDecimal nl4N;

    private BigDecimal nl4NH;

    private String nl1T;

    private String nl2T;

    private String nl3T;

    private String nl4T;

    private String nl1Dw;

    private String nl2Dw;

    private String nl3Dw;

    private String nl4Dw;

    public String getZbbm() {
        return zbbm;
    }

    public void setZbbm(String zbbm) {
        this.zbbm = zbbm == null ? null : zbbm.trim();
    }

    public String getYbbm() {
        return ybbm;
    }

    public void setYbbm(String ybbm) {
        this.ybbm = ybbm == null ? null : ybbm.trim();
    }

    public String getYljgbm() {
        return yljgbm;
    }

    public void setYljgbm(String yljgbm) {
        this.yljgbm = yljgbm == null ? null : yljgbm.trim();
    }

    public BigDecimal getAllN() {
        return allN;
    }

    public void setAllN(BigDecimal allN) {
        this.allN = allN;
    }

    public BigDecimal getAllNH() {
        return allNH;
    }

    public void setAllNH(BigDecimal allNH) {
        this.allNH = allNH;
    }

    public String getAllT() {
        return allT;
    }

    public void setAllT(String allT) {
        this.allT = allT == null ? null : allT.trim();
    }

    public BigDecimal getManN() {
        return manN;
    }

    public void setManN(BigDecimal manN) {
        this.manN = manN;
    }

    public BigDecimal getManNH() {
        return manNH;
    }

    public void setManNH(BigDecimal manNH) {
        this.manNH = manNH;
    }

    public BigDecimal getWomanN() {
        return womanN;
    }

    public void setWomanN(BigDecimal womanN) {
        this.womanN = womanN;
    }

    public BigDecimal getWomanNH() {
        return womanNH;
    }

    public void setWomanNH(BigDecimal womanNH) {
        this.womanNH = womanNH;
    }

    public String getManT() {
        return manT;
    }

    public void setManT(String manT) {
        this.manT = manT == null ? null : manT.trim();
    }

    public String getWomanT() {
        return womanT;
    }

    public void setWomanT(String womanT) {
        this.womanT = womanT == null ? null : womanT.trim();
    }

    public BigDecimal getNl1() {
        return nl1;
    }

    public void setNl1(BigDecimal nl1) {
        this.nl1 = nl1;
    }

    public BigDecimal getNl2() {
        return nl2;
    }

    public void setNl2(BigDecimal nl2) {
        this.nl2 = nl2;
    }

    public BigDecimal getNl3() {
        return nl3;
    }

    public void setNl3(BigDecimal nl3) {
        this.nl3 = nl3;
    }

    public BigDecimal getNl4() {
        return nl4;
    }

    public void setNl4(BigDecimal nl4) {
        this.nl4 = nl4;
    }

    public BigDecimal getNl1N() {
        return nl1N;
    }

    public void setNl1N(BigDecimal nl1N) {
        this.nl1N = nl1N;
    }

    public BigDecimal getNl1NH() {
        return nl1NH;
    }

    public void setNl1NH(BigDecimal nl1NH) {
        this.nl1NH = nl1NH;
    }

    public BigDecimal getNl2N() {
        return nl2N;
    }

    public void setNl2N(BigDecimal nl2N) {
        this.nl2N = nl2N;
    }

    public BigDecimal getNl2NH() {
        return nl2NH;
    }

    public void setNl2NH(BigDecimal nl2NH) {
        this.nl2NH = nl2NH;
    }

    public BigDecimal getNl3N() {
        return nl3N;
    }

    public void setNl3N(BigDecimal nl3N) {
        this.nl3N = nl3N;
    }

    public BigDecimal getNl3NH() {
        return nl3NH;
    }

    public void setNl3NH(BigDecimal nl3NH) {
        this.nl3NH = nl3NH;
    }

    public BigDecimal getNl4N() {
        return nl4N;
    }

    public void setNl4N(BigDecimal nl4N) {
        this.nl4N = nl4N;
    }

    public BigDecimal getNl4NH() {
        return nl4NH;
    }

    public void setNl4NH(BigDecimal nl4NH) {
        this.nl4NH = nl4NH;
    }

    public String getNl1T() {
        return nl1T;
    }

    public void setNl1T(String nl1T) {
        this.nl1T = nl1T == null ? null : nl1T.trim();
    }

    public String getNl2T() {
        return nl2T;
    }

    public void setNl2T(String nl2T) {
        this.nl2T = nl2T == null ? null : nl2T.trim();
    }

    public String getNl3T() {
        return nl3T;
    }

    public void setNl3T(String nl3T) {
        this.nl3T = nl3T == null ? null : nl3T.trim();
    }

    public String getNl4T() {
        return nl4T;
    }

    public void setNl4T(String nl4T) {
        this.nl4T = nl4T == null ? null : nl4T.trim();
    }

    public String getNl1Dw() {
        return nl1Dw;
    }

    public void setNl1Dw(String nl1Dw) {
        this.nl1Dw = nl1Dw == null ? null : nl1Dw.trim();
    }

    public String getNl2Dw() {
        return nl2Dw;
    }

    public void setNl2Dw(String nl2Dw) {
        this.nl2Dw = nl2Dw == null ? null : nl2Dw.trim();
    }

    public String getNl3Dw() {
        return nl3Dw;
    }

    public void setNl3Dw(String nl3Dw) {
        this.nl3Dw = nl3Dw == null ? null : nl3Dw.trim();
    }

    public String getNl4Dw() {
        return nl4Dw;
    }

    public void setNl4Dw(String nl4Dw) {
        this.nl4Dw = nl4Dw == null ? null : nl4Dw.trim();
    }

}
