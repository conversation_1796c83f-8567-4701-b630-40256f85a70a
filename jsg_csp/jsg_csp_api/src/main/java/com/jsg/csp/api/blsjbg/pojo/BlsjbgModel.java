package com.jsg.csp.api.blsjbg.pojo;

import com.jsg.common.pojo.DataGrid;

import java.io.Serializable;

public class BlsjbgModel extends DataGrid implements Serializable {

    private String id;//主键

    private String sjmc;//事件名称

    private String yljgbm;//医疗机构编码

    private String ksbm;//科室编码

    private String sjlx;//事件类型

    private String sjjb;//事件级别

    private String fssj;//发生时间

    private String txsj;//填写时间

    private String bgrbm;//报告人编码

    private String bgrxm;//报告人姓名

    private String brid;//病人编码

    private String brxm;//病人姓名

    private int nl;//病人年龄

    private String sjfszyjg;//时间发生主要经过

    private String sjfscs;//事件发生场所

    private String sjfscsqtxx;//时间发生场所其他信息

    private String zcry;//在场人员

    private String blsjlb;//不良事件类别

    private String blsjlbqtxx;//不良事件类别其他信息

    private String sjyx;//事件影响

    private String sblx;//上报类型 1医务科 2护理部

    private String sfnm;//是否匿名

    private String bgzt;//报告状态

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSjmc() {
        return sjmc;
    }

    public void setSjmc(String sjmc) {
        this.sjmc = sjmc;
    }

    public String getYljgbm() {
        return yljgbm;
    }

    public void setYljgbm(String yljgbm) {
        this.yljgbm = yljgbm;
    }

    public String getKsbm() {
        return ksbm;
    }

    public void setKsbm(String ksbm) {
        this.ksbm = ksbm;
    }

    public String getSjlx() {
        return sjlx;
    }

    public void setSjlx(String sjlx) {
        this.sjlx = sjlx;
    }

    public String getSjjb() {
        return sjjb;
    }

    public void setSjjb(String sjjb) {
        this.sjjb = sjjb;
    }

    public String getFssj() {
        return fssj;
    }

    public void setFssj(String fssj) {
        this.fssj = fssj;
    }

    public String getTxsj() {
        return txsj;
    }

    public void setTxsj(String txsj) {
        this.txsj = txsj;
    }

    public String getBgrbm() {
        return bgrbm;
    }

    public void setBgrbm(String bgrbm) {
        this.bgrbm = bgrbm;
    }

    public String getBgrxm() {
        return bgrxm;
    }

    public void setBgrxm(String bgrxm) {
        this.bgrxm = bgrxm;
    }

    public String getBrid() {
        return brid;
    }

    public void setBrid(String brid) {
        this.brid = brid;
    }

    public String getBrxm() {
        return brxm;
    }

    public void setBrxm(String brxm) {
        this.brxm = brxm;
    }

    public String getSjfszyjg() {
        return sjfszyjg;
    }

    public void setSjfszyjg(String sjfszyjg) {
        this.sjfszyjg = sjfszyjg;
    }

    public String getSjfscs() {
        return sjfscs;
    }

    public void setSjfscs(String sjfscs) {
        this.sjfscs = sjfscs;
    }

    public String getSjfscsqtxx() {
        return sjfscsqtxx;
    }

    public void setSjfscsqtxx(String sjfscsqtxx) {
        this.sjfscsqtxx = sjfscsqtxx;
    }

    public String getBlsjlb() {
        return blsjlb;
    }

    public void setBlsjlb(String blsjlb) {
        this.blsjlb = blsjlb;
    }

    public String getBlsjlbqtxx() {
        return blsjlbqtxx;
    }

    public void setBlsjlbqtxx(String blsjlbqtxx) {
        this.blsjlbqtxx = blsjlbqtxx;
    }

    public String getSjyx() {
        return sjyx;
    }

    public void setSjyx(String sjyx) {
        this.sjyx = sjyx;
    }

    public String getSblx() {
        return sblx;
    }

    public void setSblx(String sblx) {
        this.sblx = sblx;
    }

    public String getBgzt() {
        return bgzt;
    }

    public void setBgzt(String bgzt) {
        this.bgzt = bgzt;
    }

    public int getNl() {
        return nl;
    }

    public void setNl(int nl) {
        this.nl = nl;
    }

    public String getZcry() {
        return zcry;
    }

    public void setZcry(String zcry) {
        this.zcry = zcry;
    }

    public String getSfnm() {
        return sfnm;
    }

    public void setSfnm(String sfnm) {
        this.sfnm = sfnm;
    }
}
