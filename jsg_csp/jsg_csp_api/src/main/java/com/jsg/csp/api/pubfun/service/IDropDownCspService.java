package com.jsg.csp.api.pubfun.service;

import com.jsg.frame.csp.UtilRequest;
import com.jsg.frame.csp.UtilResponse;

/*
 * 下拉框接品
 */
public interface IDropDownCspService {

	/**
	 *
	* @Title: getLscfList
	* @Description: TODO(仅仅只是针对历史处方查询库存)
	* @param @param msg
	* @param @return    参数
	* @return UtilResponse    返回类型
	* @throws
	 */
	public UtilResponse getLscfList(UtilRequest msg);

	/**
	 *
	* @Title: getZhyzYpList
	* @Description: TODO(仅仅针对组合医嘱查询明细)
	* @param @param msg
	* @param @return    参数
	* @return UtilResponse    返回类型
	* @throws
	 */
	public UtilResponse getZhyzYpList(UtilRequest msg);

	/**
	 *
	 * @Title: getZhyzYpList
	 * @Description: TODO(仅仅针对组合医疗医嘱查询明细)
	 * @param @param msg
	 * @param @return    参数
	 * @return UtilResponse    返回类型
	 * @throws
	 */
	public UtilResponse getZhylyzList(UtilRequest msg);


	/**
	 *
	* @Title: getDropDownYfkc
	* @Description: TODO(药房库存查询)
	* @param @param msg
	* @param @return    参数
	* @return UtilResponse    返回类型
	* @throws
	 */
	public UtilResponse getDropDownYfkc(UtilRequest msg);

	/**
	 *
	* @Title: getDropDownYfpckc
	* @Description: TODO(药房单个药品查询)
	* @param @param msg
	* @param @return    参数
	* @return UtilResponse    返回类型
	* @throws
	 */
	public UtilResponse getDropDownYfpckcOne(UtilRequest msg);
	/**
	 *
	* @Title: getDropDownYfpckc
	* @Description: TODO(药房批次查询)
	* @param @param msg
	* @param @return    参数
	* @return UtilResponse    返回类型
	* @throws
	 */
	public UtilResponse getDropDownYfpckc(UtilRequest msg);
	/*
	 * 科室批次库存
	 */
	public UtilResponse getDropDownKspckc(UtilRequest msg);

	/*
	 * 获取患者注册信息列表
	 */
	public UtilResponse getDropDownHzxx(UtilRequest msg);
	/*
	 * 获取患者挂号信息列表
	 */
	public UtilResponse getDropDownBrgh(UtilRequest msg);



	/*
	 * 获取患者挂号信息收费列表(new1)
	 */
	public UtilResponse getDropDownBrghsflb(UtilRequest msg);

	/*
	 * 获取患者医疗卡信息列表
	 */
	public UtilResponse getDropDownHzylkxx(UtilRequest msg);

	/*
	 * 获取住院登记表信息
	 */
	public UtilResponse getDropDownRydj(UtilRequest msg);

	/*
	 * 针对病案首页下拉框检索信息
	 */
	public UtilResponse getBaJbxxList(UtilRequest msg);

	/*
	 * 获取医嘱项目（药品诊疗）
	 */
	public UtilResponse getDropDownYzxm(UtilRequest msg);

	/*
	 * 获取医嘱项目（诊疗）
	 */
	public UtilResponse getDropDownMbyzxm(UtilRequest msg);
	/*
	 * 药房对应科室
	 */
	public UtilResponse findYFByKS(UtilRequest msg);
	/*
	 * 库房对应科室
	 */
	public UtilResponse findYKByKS(UtilRequest msg);
	/*
	 * 皮试药品效期查询
	 */
	public UtilResponse getPsyzxq(UtilRequest msg);

	/**
	 * 获取药房剩余库存 = 实际库存-待发药-待领药-待出库
	 */
	public UtilResponse getYfsykc(UtilRequest msg);

	/**
	 *
	 * @Title: getDropDownYfpckc
	 * @Description: TODO(药房批次查询)
	 * @param @param msg
	 * @param @return    参数
	 * @return UtilResponse    返回类型
	 * @throws
	 */
	public UtilResponse getDropDownYfpckcxz(UtilRequest msg);
}
